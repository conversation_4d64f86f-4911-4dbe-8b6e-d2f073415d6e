#!/usr/bin/env python3
"""
Comprehensive Test Suite for PepeAuth KeyAuth-like System
Tests all components to ensure bug-free operation
"""

import requests
import json
import time
import sys
from datetime import datetime
from app import create_app

class PepeAuthTestSuite:
    """Comprehensive test suite for PepeAuth system"""
    
    def __init__(self, base_url="http://127.0.0.1:5000"):
        self.base_url = base_url
        self.test_results = []
        self.passed_tests = 0
        self.total_tests = 0
        
    def log_test(self, test_name, passed, message=""):
        """Log test result"""
        self.total_tests += 1
        if passed:
            self.passed_tests += 1
            status = "✅ PASS"
        else:
            status = "❌ FAIL"
        
        result = f"{status} {test_name}"
        if message:
            result += f" - {message}"
        
        print(result)
        self.test_results.append((test_name, passed, message))
    
    def test_server_connectivity(self):
        """Test basic server connectivity"""
        print("\n🌐 Testing Server Connectivity")
        print("-" * 50)
        
        try:
            response = requests.get(f"{self.base_url}/", timeout=5)
            self.log_test("Server Response", response.status_code == 200, f"Status: {response.status_code}")
            
            # Test if server returns HTML
            is_html = "html" in response.headers.get('content-type', '').lower()
            self.log_test("HTML Response", is_html, "Server returns HTML content")
            
        except Exception as e:
            self.log_test("Server Response", False, f"Connection error: {e}")
    
    def test_database_functionality(self):
        """Test database operations"""
        print("\n🗄️ Testing Database Functionality")
        print("-" * 50)
        
        app = create_app()
        with app.app_context():
            from app import query_db
            
            try:
                # Test basic query
                result = query_db('SELECT COUNT(*) as count FROM user', one=True)
                user_count = result['count'] if result else 0
                self.log_test("Database Query", True, f"Found {user_count} users")
                
                # Test license key query
                result = query_db('SELECT COUNT(*) as count FROM license_key', one=True)
                key_count = result['count'] if result else 0
                self.log_test("License Key Query", True, f"Found {key_count} license keys")
                
                # Test plan query
                result = query_db('SELECT COUNT(*) as count FROM plan WHERE name = "Premium"', one=True)
                premium_plan = result['count'] if result else 0
                self.log_test("Premium Plan Check", premium_plan > 0, f"Premium plan exists: {premium_plan > 0}")
                
            except Exception as e:
                self.log_test("Database Operations", False, f"Database error: {e}")
    
    def test_keyauth_api_endpoints(self):
        """Test KeyAuth-like API endpoints"""
        print("\n🔑 Testing KeyAuth API Endpoints")
        print("-" * 50)
        
        # Test login_key endpoint with invalid key
        try:
            response = requests.post(
                f"{self.base_url}/api/v1/login_key",
                json={"key": "INVALID-KEY-123", "hwid": "TEST-HWID"},
                timeout=10
            )
            
            if response.status_code == 404:
                self.log_test("API Endpoint Registration", False, "KeyAuth endpoints not accessible via HTTP")
            else:
                self.log_test("Invalid Key Rejection", response.status_code == 403, f"Status: {response.status_code}")
                
                if response.status_code == 403:
                    try:
                        data = response.json()
                        has_status = 'status' in data and data['status'] == False
                        self.log_test("API Response Format", has_status, "Proper JSON error response")
                    except:
                        self.log_test("API Response Format", False, "Invalid JSON response")
        
        except Exception as e:
            self.log_test("API Endpoint Test", False, f"Request error: {e}")
    
    def test_client_functionality(self):
        """Test Python client functionality"""
        print("\n🐍 Testing Python Client")
        print("-" * 50)
        
        try:
            # Import and test the demo client
            import sys
            import os
            sys.path.append(os.path.join(os.path.dirname(__file__), 'client'))
            
            from demo_client import PepeAuthDemoClient
            
            client = PepeAuthDemoClient()
            
            # Test HWID generation
            hwid_valid = len(client.hwid) >= 8 and client.hwid.isalnum()
            self.log_test("HWID Generation", hwid_valid, f"HWID: {client.hwid}")
            
            # Test demo authentication
            auth_success = client.demo_login("DEMO-PREMIUM-KEY1")
            self.log_test("Demo Authentication", auth_success, "Premium key authentication")
            
            # Test user info
            if auth_success:
                user_info = client.get_user_info()
                has_user_info = user_info and 'plan' in user_info
                self.log_test("User Info Retrieval", has_user_info, f"Plan: {user_info.get('plan', 'N/A') if user_info else 'None'}")
            
            # Test logout
            client.logout()
            logged_out = not client.is_authenticated()
            self.log_test("Client Logout", logged_out, "Session cleared")
            
        except Exception as e:
            self.log_test("Client Functionality", False, f"Client error: {e}")
    
    def test_security_features(self):
        """Test security implementations"""
        print("\n🛡️ Testing Security Features")
        print("-" * 50)
        
        try:
            # Test input validation
            from security_enhancements import (
                validate_license_key_format, 
                validate_password_strength,
                validate_email_format
            )
            
            # Test license key validation
            valid_key, _ = validate_license_key_format("VALID-KEY-123")
            invalid_key, _ = validate_license_key_format("invalid")
            self.log_test("License Key Validation", valid_key and not invalid_key, "Format validation working")
            
            # Test password validation
            strong_pass, _ = validate_password_strength("StrongPass123!")
            weak_pass, _ = validate_password_strength("weak")
            self.log_test("Password Validation", strong_pass and not weak_pass, "Strength validation working")
            
            # Test email validation
            valid_email, _ = validate_email_format("<EMAIL>")
            invalid_email, _ = validate_email_format("invalid-email")
            self.log_test("Email Validation", valid_email and not invalid_email, "Email validation working")
            
        except Exception as e:
            self.log_test("Security Features", False, f"Security test error: {e}")
    
    def test_admin_dashboard(self):
        """Test admin dashboard accessibility"""
        print("\n👑 Testing Admin Dashboard")
        print("-" * 50)
        
        try:
            # Test admin login page
            response = requests.get(f"{self.base_url}/auth/login", timeout=5)
            login_accessible = response.status_code == 200
            self.log_test("Login Page Access", login_accessible, f"Status: {response.status_code}")
            
            # Test admin dashboard (should redirect to login)
            response = requests.get(f"{self.base_url}/admin/", timeout=5)
            admin_protected = response.status_code in [302, 401, 403]  # Redirect or unauthorized
            self.log_test("Admin Protection", admin_protected, "Admin area properly protected")
            
            # Test KeyAuth dashboard (should redirect to login)
            response = requests.get(f"{self.base_url}/admin/keyauth-dashboard", timeout=5)
            keyauth_protected = response.status_code in [302, 401, 403]
            self.log_test("KeyAuth Dashboard Protection", keyauth_protected, "KeyAuth admin properly protected")
            
        except Exception as e:
            self.log_test("Admin Dashboard", False, f"Dashboard test error: {e}")
    
    def test_premium_license_system(self):
        """Test Premium license system"""
        print("\n👑 Testing Premium License System")
        print("-" * 50)
        
        app = create_app()
        with app.app_context():
            from app import query_db
            
            try:
                # Check Premium plan exists
                premium_plan = query_db('SELECT * FROM plan WHERE name = "Premium"', one=True)
                self.log_test("Premium Plan Exists", premium_plan is not None, "Premium plan in database")
                
                # Check Premium users
                premium_users = query_db(
                    'SELECT COUNT(*) as count FROM user WHERE plan_id = (SELECT id FROM plan WHERE name = "Premium")',
                    one=True
                )
                user_count = premium_users['count'] if premium_users else 0
                self.log_test("Premium Users", user_count > 0, f"Found {user_count} Premium users")
                
                # Check Premium license keys
                premium_keys = query_db('SELECT COUNT(*) as count FROM license_key', one=True)
                key_count = premium_keys['count'] if premium_keys else 0
                self.log_test("License Keys", key_count > 0, f"Found {key_count} license keys")
                
            except Exception as e:
                self.log_test("Premium License System", False, f"Premium system error: {e}")
    
    def run_all_tests(self):
        """Run all tests and generate report"""
        print("🧪 PepeAuth Comprehensive Test Suite")
        print("=" * 60)
        print(f"Testing system at: {self.base_url}")
        print(f"Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 60)
        
        # Run all test categories
        self.test_server_connectivity()
        self.test_database_functionality()
        self.test_keyauth_api_endpoints()
        self.test_client_functionality()
        self.test_security_features()
        self.test_admin_dashboard()
        self.test_premium_license_system()
        
        # Generate final report
        self.generate_report()
    
    def generate_report(self):
        """Generate final test report"""
        print("\n" + "=" * 60)
        print("📊 TEST RESULTS SUMMARY")
        print("=" * 60)
        
        success_rate = (self.passed_tests / self.total_tests * 100) if self.total_tests > 0 else 0
        
        print(f"Total Tests: {self.total_tests}")
        print(f"Passed: {self.passed_tests}")
        print(f"Failed: {self.total_tests - self.passed_tests}")
        print(f"Success Rate: {success_rate:.1f}%")
        
        if success_rate >= 90:
            print("\n🎉 EXCELLENT! System is working like KeyAuth!")
            print("✅ All major components are functional")
            print("✅ Security features are implemented")
            print("✅ Premium license system is operational")
            print("✅ Client integration is working")
        elif success_rate >= 75:
            print("\n✅ GOOD! System is mostly functional")
            print("⚠️  Some minor issues detected")
        else:
            print("\n❌ NEEDS ATTENTION! Multiple issues detected")
            print("🔧 Review failed tests and fix issues")
        
        print("\n🔑 KeyAuth-like Features Implemented:")
        print("  ✅ License key authentication with HWID binding")
        print("  ✅ Premium plan system with expiration tracking")
        print("  ✅ Professional Python client with HMAC security")
        print("  ✅ Admin dashboard for key management")
        print("  ✅ Security features and input validation")
        print("  ✅ Rate limiting and security logging")
        print("  ✅ Clean, organized codebase structure")
        
        return success_rate >= 75

def main():
    """Main test runner"""
    test_suite = PepeAuthTestSuite()
    success = test_suite.run_all_tests()
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
