{% extends "base.html" %}

{% block title %}PepeAuth - Modern License Management Platform{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="relative overflow-hidden py-20 lg:py-32">
    <div class="absolute inset-0 bg-gradient-to-br from-pepe-green/10 to-transparent"></div>
    <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <h1 class="text-4xl lg:text-6xl font-bold text-white mb-6">
                Modern License
                <span class="text-pepe-green">Management</span>
            </h1>
            <p class="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
                Secure, scalable license management platform for developers. 
                Generate, validate, and manage software licenses with ease.
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="{{ url_for('auth.register') }}" class="btn-primary text-lg px-8 py-3">
                    Get Started Free
                </a>
                <a href="#features" class="btn-secondary text-lg px-8 py-3">
                    Learn More
                </a>
            </div>
        </div>
        
        <!-- Hero Image/Demo -->
        <div class="mt-16 relative">
            <div class="glass-card p-6 rounded-2xl max-w-4xl mx-auto">
                <div class="bg-pepe-darker rounded-lg p-4">
                    <div class="flex items-center space-x-2 mb-4">
                        <div class="w-3 h-3 bg-red-500 rounded-full"></div>
                        <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
                        <div class="w-3 h-3 bg-pepe-green rounded-full"></div>
                    </div>
                    <pre class="text-sm text-gray-300 overflow-x-auto"><code>{
  "app_name": "MyAwesomeApp",
  "key": "550e8400-e29b-41d4-a716-446655440000",
  "hwid": "unique-hardware-id"
}</code></pre>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Features Section -->
<section id="features" class="py-20 bg-pepe-darker/50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h2 class="text-3xl lg:text-4xl font-bold text-white mb-4">
                Everything you need to manage licenses
            </h2>
            <p class="text-xl text-gray-300 max-w-2xl mx-auto">
                Built for developers, by developers. Simple API, powerful features.
            </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <!-- Feature 1 -->
            <div class="glass-card p-6 rounded-xl hover:scale-105 transition-transform">
                <div class="w-12 h-12 bg-pepe-green/20 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-pepe-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-white mb-2">Secure by Design</h3>
                <p class="text-gray-300">
                    Military-grade encryption, HWID binding, and secure key validation.
                </p>
            </div>
            
            <!-- Feature 2 -->
            <div class="glass-card p-6 rounded-xl hover:scale-105 transition-transform">
                <div class="w-12 h-12 bg-pepe-green/20 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-pepe-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-white mb-2">Lightning Fast</h3>
                <p class="text-gray-300">
                    Sub-100ms response times with global CDN and optimized database queries.
                </p>
            </div>
            
            <!-- Feature 3 -->
            <div class="glass-card p-6 rounded-xl hover:scale-105 transition-transform">
                <div class="w-12 h-12 bg-pepe-green/20 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-pepe-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-white mb-2">Real-time Analytics</h3>
                <p class="text-gray-300">
                    Track usage, monitor activations, and get insights into your license distribution.
                </p>
            </div>
            
            <!-- Feature 4 -->
            <div class="glass-card p-6 rounded-xl hover:scale-105 transition-transform">
                <div class="w-12 h-12 bg-pepe-green/20 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-pepe-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-white mb-2">Easy Integration</h3>
                <p class="text-gray-300">
                    Simple REST API with SDKs for popular programming languages.
                </p>
            </div>
            
            <!-- Feature 5 -->
            <div class="glass-card p-6 rounded-xl hover:scale-105 transition-transform">
                <div class="w-12 h-12 bg-pepe-green/20 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-pepe-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-white mb-2">Team Management</h3>
                <p class="text-gray-300">
                    Collaborate with your team, manage permissions, and track activity.
                </p>
            </div>
            
            <!-- Feature 6 -->
            <div class="glass-card p-6 rounded-xl hover:scale-105 transition-transform">
                <div class="w-12 h-12 bg-pepe-green/20 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-pepe-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-white mb-2">24/7 Support</h3>
                <p class="text-gray-300">
                    Get help when you need it with our dedicated support team.
                </p>
            </div>
        </div>
    </div>
</section>

<!-- Pricing Section -->
<section id="pricing" class="py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h2 class="text-3xl lg:text-4xl font-bold text-white mb-4">
                Simple, transparent pricing
            </h2>
            <p class="text-xl text-gray-300 max-w-2xl mx-auto">
                Start free, scale as you grow. No hidden fees, no surprises.
            </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto">
            <!-- Free Plan -->
            <div class="glass-card p-8 rounded-2xl">
                <div class="text-center">
                    <h3 class="text-2xl font-bold text-white mb-2">Free</h3>
                    <div class="text-4xl font-bold text-pepe-green mb-4">$0</div>
                    <p class="text-gray-300 mb-6">Perfect for getting started</p>
                </div>
                <ul class="space-y-3 mb-8">
                    <li class="flex items-center text-gray-300">
                        <svg class="w-5 h-5 text-pepe-green mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        1 App
                    </li>
                    <li class="flex items-center text-gray-300">
                        <svg class="w-5 h-5 text-pepe-green mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        50 License Keys
                    </li>
                    <li class="flex items-center text-gray-300">
                        <svg class="w-5 h-5 text-pepe-green mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        100 API Calls/day
                    </li>
                    <li class="flex items-center text-gray-300">
                        <svg class="w-5 h-5 text-pepe-green mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        Basic Analytics
                    </li>
                </ul>
                <a href="{{ url_for('auth.register') }}" class="btn-secondary w-full text-center">Get Started</a>
            </div>
            
            <!-- Pro Plan -->
            <div class="glass-card p-8 rounded-2xl border-2 border-pepe-green relative">
                <div class="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <span class="bg-pepe-green text-white px-4 py-1 rounded-full text-sm font-medium">Most Popular</span>
                </div>
                <div class="text-center">
                    <h3 class="text-2xl font-bold text-white mb-2">Pro</h3>
                    <div class="text-4xl font-bold text-pepe-green mb-4">$29</div>
                    <p class="text-gray-300 mb-6">For growing businesses</p>
                </div>
                <ul class="space-y-3 mb-8">
                    <li class="flex items-center text-gray-300">
                        <svg class="w-5 h-5 text-pepe-green mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        3 Apps
                    </li>
                    <li class="flex items-center text-gray-300">
                        <svg class="w-5 h-5 text-pepe-green mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        1,000 License Keys
                    </li>
                    <li class="flex items-center text-gray-300">
                        <svg class="w-5 h-5 text-pepe-green mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        1,000 API Calls/day
                    </li>
                    <li class="flex items-center text-gray-300">
                        <svg class="w-5 h-5 text-pepe-green mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        Advanced Analytics
                    </li>
                    <li class="flex items-center text-gray-300">
                        <svg class="w-5 h-5 text-pepe-green mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        Priority Support
                    </li>
                </ul>
                <a href="{{ url_for('auth.register') }}" class="btn-primary w-full text-center">Start Pro Trial</a>
            </div>
            
            <!-- Military Plan -->
            <div class="glass-card p-8 rounded-2xl">
                <div class="text-center">
                    <h3 class="text-2xl font-bold text-white mb-2">Military</h3>
                    <div class="text-4xl font-bold text-pepe-green mb-4">$99</div>
                    <p class="text-gray-300 mb-6">For enterprise needs</p>
                </div>
                <ul class="space-y-3 mb-8">
                    <li class="flex items-center text-gray-300">
                        <svg class="w-5 h-5 text-pepe-green mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        Unlimited Apps
                    </li>
                    <li class="flex items-center text-gray-300">
                        <svg class="w-5 h-5 text-pepe-green mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        Unlimited License Keys
                    </li>
                    <li class="flex items-center text-gray-300">
                        <svg class="w-5 h-5 text-pepe-green mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        Unlimited API Calls
                    </li>
                    <li class="flex items-center text-gray-300">
                        <svg class="w-5 h-5 text-pepe-green mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        Custom Analytics
                    </li>
                    <li class="flex items-center text-gray-300">
                        <svg class="w-5 h-5 text-pepe-green mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        24/7 Dedicated Support
                    </li>
                </ul>
                <a href="{{ url_for('auth.register') }}" class="btn-secondary w-full text-center">Contact Sales</a>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="py-20 bg-gradient-to-r from-pepe-green/20 to-transparent">
    <div class="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
        <h2 class="text-3xl lg:text-4xl font-bold text-white mb-4">
            Ready to secure your software?
        </h2>
        <p class="text-xl text-gray-300 mb-8">
            Join thousands of developers who trust PepeAuth for their license management needs.
        </p>
        <a href="{{ url_for('auth.register') }}" class="btn-primary text-lg px-8 py-3">
            Start Building Today
        </a>
    </div>
</section>
{% endblock %}
