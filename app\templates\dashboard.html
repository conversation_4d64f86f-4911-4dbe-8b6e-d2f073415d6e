{% extends "base.html" %}

{% block title %}Dashboard - PepeAuth{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div>
                <h1 class="text-3xl font-bold text-white">Dashboard</h1>
                <p class="mt-1 text-gray-300">Welcome back, {{ current_user.username }}!</p>
            </div>
            <div class="mt-4 sm:mt-0">
                <a href="{{ url_for('dashboard.create_app') }}" class="btn-primary">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Create App
                </a>
            </div>
        </div>
    </div>
    
    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="glass-card p-6 rounded-xl">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-blue-500/20 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-300">Total Apps</p>
                    <p class="text-2xl font-semibold text-white">{{ stats.total_apps }}</p>
                </div>
            </div>
        </div>
        
        <div class="glass-card p-6 rounded-xl">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-pepe-green/20 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-pepe-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m0 0a2 2 0 012 2m-2-2a2 2 0 00-2 2m2-2V5a2 2 0 00-2-2m0 0H9a2 2 0 00-2 2v0a2 2 0 002 2h2m-4 4v6a2 2 0 002 2h2a2 2 0 002-2v-6m-4 0a2 2 0 012-2h0a2 2 0 012 2v0"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-300">License Keys</p>
                    <p class="text-2xl font-semibold text-white">{{ stats.total_keys }}</p>
                </div>
            </div>
        </div>
        
        <div class="glass-card p-6 rounded-xl">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-green-500/20 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-300">Used Keys</p>
                    <p class="text-2xl font-semibold text-white">{{ stats.used_keys }}</p>
                </div>
            </div>
        </div>
        
        <div class="glass-card p-6 rounded-xl">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-purple-500/20 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-300">Current Plan</p>
                    <p class="text-2xl font-semibold text-white">{{ stats.plan.name }}</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Plan Usage -->
    <div class="glass-card p-6 rounded-xl mb-8">
        <h3 class="text-lg font-semibold text-white mb-4">Plan Usage</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <div class="flex justify-between items-center mb-2">
                    <span class="text-sm text-gray-300">Apps</span>
                    <span class="text-sm text-gray-300">
                        {{ stats.total_apps }}{% if stats.plan.max_apps != -1 %} / {{ stats.plan.max_apps }}{% else %} / ∞{% endif %}
                    </span>
                </div>
                <div class="w-full bg-pepe-gray rounded-full h-2">
                    {% if stats.plan.max_apps == -1 %}
                        <div class="bg-pepe-green h-2 rounded-full" style="width: 10%"></div>
                    {% else %}
                        {% set app_percentage = (stats.total_apps / stats.plan.max_apps * 100) if stats.plan.max_apps > 0 else 0 %}
                        <div class="bg-pepe-green h-2 rounded-full" style="width: {{ app_percentage | round }}%"></div>
                    {% endif %}
                </div>
            </div>
            
            <div>
                <div class="flex justify-between items-center mb-2">
                    <span class="text-sm text-gray-300">License Keys</span>
                    <span class="text-sm text-gray-300">
                        {{ stats.total_keys }}{% if stats.plan.max_keys != -1 %} / {{ stats.plan.max_keys }}{% else %} / ∞{% endif %}
                    </span>
                </div>
                <div class="w-full bg-pepe-gray rounded-full h-2">
                    {% if stats.plan.max_keys == -1 %}
                        <div class="bg-pepe-green h-2 rounded-full" style="width: 10%"></div>
                    {% else %}
                        {% set key_percentage = (stats.total_keys / stats.plan.max_keys * 100) if stats.plan.max_keys > 0 else 0 %}
                        <div class="bg-pepe-green h-2 rounded-full" style="width: {{ key_percentage | round }}%"></div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    
    <!-- Apps List -->
    <div class="glass-card p-6 rounded-xl">
        <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold text-white">Your Apps</h3>
            {% if apps %}
                <span class="text-sm text-gray-300">{{ apps|length }} app{{ 's' if apps|length != 1 else '' }}</span>
            {% endif %}
        </div>
        
        {% if apps %}
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {% for app in apps %}
                    <div class="glass-card p-6 rounded-lg hover:scale-105 transition-transform">
                        <div class="flex items-center justify-between mb-4">
                            <h4 class="text-lg font-semibold text-white truncate">{{ app.name }}</h4>
                            <div class="flex items-center space-x-2">
                                {% if app.hwid_lock %}
                                    <div class="w-2 h-2 bg-yellow-500 rounded-full" title="HWID Lock Enabled"></div>
                                {% endif %}
                                <div class="w-2 h-2 bg-pepe-green rounded-full" title="Active"></div>
                            </div>
                        </div>
                        
                        <div class="space-y-2 mb-4">
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-300">Total Keys:</span>
                                <span class="text-white">{{ app.key_count }}</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-300">Used Keys:</span>
                                <span class="text-white">{{ app.used_keys }}</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-300">Expiry:</span>
                                <span class="text-white">{{ app.expiry_days }} days</span>
                            </div>
                        </div>
                        
                        <div class="flex space-x-2">
                            <a href="{{ url_for('dashboard.app_detail', app_id=app.id) }}" 
                               class="btn-primary text-sm flex-1 text-center">
                                Manage
                            </a>
                            <a href="{{ url_for('dashboard.generate_keys', app_id=app.id) }}" 
                               class="btn-secondary text-sm px-3">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                            </a>
                        </div>
                    </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="text-center py-12">
                <div class="w-16 h-16 bg-pepe-gray rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-medium text-white mb-2">No apps yet</h3>
                <p class="text-gray-300 mb-6">Create your first app to start managing license keys.</p>
                <a href="{{ url_for('dashboard.create_app') }}" class="btn-primary">
                    Create Your First App
                </a>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}
