{% extends "base.html" %}

{% block title %}Login - PepeAuth{% endblock %}

{% block main_class %}flex items-center justify-center min-h-screen py-12 px-4 sm:px-6 lg:px-8{% endblock %}

{% block content %}
<div class="max-w-md w-full space-y-8">
    <div class="text-center">
        <div class="flex justify-center">
            <div class="w-16 h-16 bg-pepe-green rounded-2xl flex items-center justify-center mb-6">
                <span class="text-white font-bold text-2xl">P</span>
            </div>
        </div>
        <h2 class="text-3xl font-bold text-white">Welcome back</h2>
        <p class="mt-2 text-gray-300">Sign in to your PepeAuth account</p>
    </div>
    
    <div class="glass-card p-8 rounded-2xl">
        <form method="POST" class="space-y-6">
            {{ form.hidden_tag() }}
            
            <div>
                {{ form.username.label(class="block text-sm font-medium text-white mb-2") }}
                {{ form.username(class="input-field") }}
                {% if form.username.errors %}
                    <div class="mt-1 text-sm text-red-400">
                        {% for error in form.username.errors %}
                            <p>{{ error }}</p>
                        {% endfor %}
                    </div>
                {% endif %}
            </div>
            
            <div>
                {{ form.password.label(class="block text-sm font-medium text-white mb-2") }}
                {{ form.password(class="input-field") }}
                {% if form.password.errors %}
                    <div class="mt-1 text-sm text-red-400">
                        {% for error in form.password.errors %}
                            <p>{{ error }}</p>
                        {% endfor %}
                    </div>
                {% endif %}
            </div>
            
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    {{ form.remember_me(class="h-4 w-4 text-pepe-green focus:ring-pepe-green border-gray-600 rounded bg-pepe-gray") }}
                    {{ form.remember_me.label(class="ml-2 block text-sm text-gray-300") }}
                </div>
                
                <div class="text-sm">
                    <a href="#" class="text-pepe-green hover:text-pepe-green/80 transition-colors">
                        Forgot password?
                    </a>
                </div>
            </div>
            
            <div>
                <button type="submit" class="btn-primary w-full">
                    Sign In
                </button>
            </div>
        </form>
        
        <div class="mt-6">
            <div class="relative">
                <div class="absolute inset-0 flex items-center">
                    <div class="w-full border-t border-gray-600"></div>
                </div>
                <div class="relative flex justify-center text-sm">
                    <span class="px-2 bg-pepe-gray text-gray-300">Don't have an account?</span>
                </div>
            </div>
            
            <div class="mt-6">
                <a href="{{ url_for('auth.register') }}" class="btn-secondary w-full text-center">
                    Create Account
                </a>
            </div>
        </div>
    </div>
    
    <!-- Demo Credentials -->
    <div class="glass-card p-4 rounded-lg">
        <h3 class="text-sm font-medium text-white mb-2">Demo Credentials</h3>
        <div class="text-xs text-gray-300 space-y-1">
            <p><strong>Admin:</strong> <EMAIL> / admin123</p>
            <p><strong>User:</strong> <EMAIL> / demo123</p>
        </div>
    </div>
</div>
{% endblock %}
