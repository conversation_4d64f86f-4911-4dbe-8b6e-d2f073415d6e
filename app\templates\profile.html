{% extends "base.html" %}

{% block title %}Profile - PepeAuth{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-white">Profile</h1>
        <p class="mt-1 text-gray-300">Manage your account settings and view your usage</p>
    </div>
    
    <!-- User Info -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
        <!-- Profile Card -->
        <div class="lg:col-span-2">
            <div class="glass-card p-6 rounded-xl">
                <h3 class="text-lg font-semibold text-white mb-4">Account Information</h3>
                <div class="space-y-4">
                    <div class="flex items-center">
                        <div class="w-16 h-16 bg-pepe-green rounded-full flex items-center justify-center mr-4">
                            <span class="text-white font-bold text-2xl">{{ current_user.username[0].upper() }}</span>
                        </div>
                        <div>
                            <h4 class="text-xl font-semibold text-white">{{ current_user.username }}</h4>
                            <p class="text-gray-300">{{ current_user.email }}</p>
                            {% if current_user.is_admin %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-500/20 text-red-400 mt-1">
                                    Administrator
                                </span>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-2 gap-4 pt-4 border-t border-pepe-light-gray/20">
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-1">Member Since</label>
                            <p class="text-white">{{ current_user.created_at }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-1">User ID</label>
                            <p class="text-white font-mono">{{ current_user.id }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Plan Card -->
        <div>
            <div class="glass-card p-6 rounded-xl">
                <h3 class="text-lg font-semibold text-white mb-4">Current Plan</h3>
                <div class="text-center">
                    <div class="text-3xl font-bold text-pepe-green mb-2">{{ plan.name }}</div>
                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span class="text-gray-300">Apps:</span>
                            <span class="text-white">{{ plan.max_apps if plan.max_apps != -1 else '∞' }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Keys:</span>
                            <span class="text-white">{{ plan.max_keys if plan.max_keys != -1 else '∞' }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">API Calls:</span>
                            <span class="text-white">{{ plan.rate_limit if plan.rate_limit != -1 else '∞' }}/day</span>
                        </div>
                    </div>
                </div>
                
                {% if plan.name == 'Free' %}
                    <div class="mt-4">
                        <a href="#" class="btn-primary w-full text-center text-sm">
                            Upgrade Plan
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- Usage Statistics -->
    <div class="glass-card p-6 rounded-xl mb-8">
        <h3 class="text-lg font-semibold text-white mb-4">Usage Statistics</h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="text-center">
                <div class="text-3xl font-bold text-blue-400 mb-2">{{ stats.apps }}</div>
                <div class="text-gray-300">Apps Created</div>
                {% if plan.max_apps != -1 %}
                    <div class="w-full bg-pepe-gray rounded-full h-2 mt-2">
                        {% set app_percentage = (stats.apps / plan.max_apps * 100) if plan.max_apps > 0 else 0 %}
                        <div class="bg-blue-400 h-2 rounded-full" style="width: {{ app_percentage }}%"></div>
                    </div>
                    <div class="text-xs text-gray-400 mt-1">{{ stats.apps }} / {{ plan.max_apps }}</div>
                {% endif %}
            </div>
            
            <div class="text-center">
                <div class="text-3xl font-bold text-pepe-green mb-2">{{ stats.keys }}</div>
                <div class="text-gray-300">License Keys</div>
                {% if plan.max_keys != -1 %}
                    <div class="w-full bg-pepe-gray rounded-full h-2 mt-2">
                        {% set key_percentage = (stats.keys / plan.max_keys * 100) if plan.max_keys > 0 else 0 %}
                        <div class="bg-pepe-green h-2 rounded-full" style="width: {{ key_percentage }}%"></div>
                    </div>
                    <div class="text-xs text-gray-400 mt-1">{{ stats.keys }} / {{ plan.max_keys }}</div>
                {% endif %}
            </div>
            
            <div class="text-center">
                <div class="text-3xl font-bold text-green-400 mb-2">{{ stats.used_keys }}</div>
                <div class="text-gray-300">Keys Activated</div>
                {% if stats.keys > 0 %}
                    <div class="w-full bg-pepe-gray rounded-full h-2 mt-2">
                        {% set used_percentage = (stats.used_keys / stats.keys * 100) if stats.keys > 0 else 0 %}
                        <div class="bg-green-400 h-2 rounded-full" style="width: {{ used_percentage }}%"></div>
                    </div>
                    <div class="text-xs text-gray-400 mt-1">{{ stats.used_keys }} / {{ stats.keys }}</div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- Quick Actions -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div class="glass-card p-6 rounded-xl">
            <h3 class="text-lg font-semibold text-white mb-4">Quick Actions</h3>
            <div class="space-y-3">
                <a href="{{ url_for('dashboard.index') }}" class="btn-secondary w-full text-center">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    Go to Dashboard
                </a>
                {% if current_user.can_create_app() %}
                    <a href="{{ url_for('dashboard.create_app') }}" class="btn-primary w-full text-center">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        Create New App
                    </a>
                {% endif %}
            </div>
        </div>
        
        <div class="glass-card p-6 rounded-xl">
            <h3 class="text-lg font-semibold text-white mb-4">Account Security</h3>
            <div class="space-y-3">
                <button class="btn-secondary w-full" onclick="alert('Password change feature coming soon!')">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m0 0a2 2 0 012 2m-2-2a2 2 0 00-2 2m2-2V5a2 2 0 00-2-2m0 0H9a2 2 0 00-2 2v0a2 2 0 002 2h2m-4 4v6a2 2 0 002 2h2a2 2 0 002-2v-6m-4 0a2 2 0 012-2h0a2 2 0 012 2v0"></path>
                    </svg>
                    Change Password
                </button>
                <button class="btn-secondary w-full" onclick="alert('Two-factor authentication coming soon!')">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                    </svg>
                    Enable 2FA
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}
