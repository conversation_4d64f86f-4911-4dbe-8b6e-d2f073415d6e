#!/usr/bin/env python3
"""
PepeAuth Startup Script
Fixes database issues and starts the application
"""

import os
import sys
import subprocess

def print_banner():
    """Print startup banner"""
    print("=" * 50)
    print("🐸 PepeAuth - Starting Up")
    print("=" * 50)

def fix_database():
    """Fix database schema issues"""
    print("🔧 Checking database...")
    
    try:
        # Run the database fix script
        result = subprocess.run([sys.executable, 'fix_database.py'], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Database is ready!")
            if result.stdout:
                print(result.stdout)
        else:
            print("❌ Database fix failed!")
            if result.stderr:
                print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ Error fixing database: {e}")
        return False
    
    return True

def check_dependencies():
    """Check if required dependencies are installed"""
    print("📦 Checking dependencies...")
    
    required_packages = [
        'flask',
        'flask-login',
        'werkzeug',
        'requests'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ Missing packages: {', '.join(missing_packages)}")
        print("Installing missing packages...")
        
        try:
            subprocess.check_call([
                sys.executable, '-m', 'pip', 'install'
            ] + missing_packages)
            print("✅ Dependencies installed!")
        except subprocess.CalledProcessError:
            print("❌ Failed to install dependencies!")
            return False
    else:
        print("✅ All dependencies are installed!")
    
    return True

def create_env_if_missing():
    """Create .env file if it doesn't exist"""
    if not os.path.exists('.env'):
        print("⚙️ Creating .env file...")
        
        env_content = """# PepeAuth Configuration
SECRET_KEY=your-secret-key-change-this-in-production
DEBUG=True
HOST=0.0.0.0
PORT=5000
DATABASE_URL=sqlite:///pepeauth.db
"""
        
        with open('.env', 'w') as f:
            f.write(env_content)
        
        print("✅ .env file created!")

def start_application():
    """Start the Flask application"""
    print("🚀 Starting PepeAuth...")
    print("Application will be available at: http://localhost:5000")
    print("Press Ctrl+C to stop the server")
    print("-" * 50)
    
    try:
        # Start the application
        subprocess.run([sys.executable, 'run.py'])
    except KeyboardInterrupt:
        print("\n👋 PepeAuth stopped by user")
    except Exception as e:
        print(f"❌ Error starting application: {e}")

def main():
    """Main startup function"""
    print_banner()
    
    # Check and install dependencies
    if not check_dependencies():
        print("❌ Dependency check failed!")
        sys.exit(1)
    
    # Create .env if missing
    create_env_if_missing()
    
    # Fix database
    if not fix_database():
        print("❌ Database setup failed!")
        sys.exit(1)
    
    # Start application
    start_application()

if __name__ == "__main__":
    main()
