{% extends "base.html" %}

{% block title %}Create License Keys - PepeAuth{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center space-x-4 mb-4">
            <a href="{{ url_for('dashboard.index') }}" 
               class="text-gray-400 hover:text-white transition-colors">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
            </a>
            <div>
                <h1 class="text-3xl font-bold text-white">Create License Keys</h1>
                <p class="mt-1 text-gray-300">Generate new license keys for your applications</p>
            </div>
        </div>
        
        <!-- Plan Limits Info -->
        <div class="glass-card p-4 rounded-lg border-l-4 border-blue-500">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <svg class="w-5 h-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm text-white">
                        <span class="font-medium">{{ current_user.plan.name }} Plan:</span>
                        You can create 
                        {% if current_user.plan.max_keys == -1 %}
                            unlimited license keys
                        {% else %}
                            up to {{ current_user.plan.max_keys }} license keys ({{ stats.total_keys }}/{{ current_user.plan.max_keys }} used)
                        {% endif %}
                    </p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Create License Form -->
    <div class="glass-card p-8 rounded-xl">
        <form method="POST" class="space-y-6" x-data="{ 
            selectedApp: '{{ request.args.get('app_id', '') }}',
            selectedPlan: '1',
            quantity: 1,
            expiryDays: 30,
            hwidBind: true,
            customExpiry: false,
            bulkMode: false,
            estimatedCost: 0,
            updateCost: function() {
                // Calculate estimated cost based on plan and quantity
                const planPrices = { '1': 0, '2': 9.99, '3': 49.99 };
                this.estimatedCost = (planPrices[this.selectedPlan] || 0) * this.quantity;
            }
        }" x-init="updateCost()">
            {{ form.hidden_tag() }}
            
            <!-- App Selection -->
            <div class="form-group">
                <label for="app_id" class="form-label">
                    Select Application
                    <span class="text-red-400">*</span>
                </label>
                <select name="app_id" 
                        class="input-field" 
                        x-model="selectedApp"
                        required>
                    <option value="">Choose an application...</option>
                    {% for app in apps %}
                        <option value="{{ app.id }}" 
                                {% if request.args.get('app_id') == app.id|string %}selected{% endif %}>
                            {{ app.name }} ({{ app.key_count }} keys)
                        </option>
                    {% endfor %}
                </select>
                {% if form.app_id.errors %}
                    <div class="form-error">
                        {% for error in form.app_id.errors %}
                            <p>{{ error }}</p>
                        {% endfor %}
                    </div>
                {% endif %}
                <p class="mt-1 text-sm text-gray-400">
                    Select which application these license keys will be associated with.
                </p>
            </div>
            
            <!-- Plan Selection -->
            <div class="form-group">
                <label class="form-label">
                    License Plan
                    <span class="text-red-400">*</span>
                </label>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {% for plan in plans %}
                        <div class="relative">
                            <input type="radio" 
                                   name="plan_id" 
                                   value="{{ plan.id }}" 
                                   id="plan_{{ plan.id }}"
                                   class="sr-only"
                                   x-model="selectedPlan"
                                   @change="updateCost()">
                            <label for="plan_{{ plan.id }}" 
                                   class="glass-card p-4 rounded-lg cursor-pointer transition-all hover:scale-105"
                                   :class="selectedPlan == '{{ plan.id }}' ? 'border-2 border-pepe-green' : 'border border-pepe-light-gray/20'">
                                <div class="text-center">
                                    <h3 class="text-lg font-semibold text-white">{{ plan.name }}</h3>
                                    <p class="text-2xl font-bold text-pepe-green mt-2">
                                        {% if plan.price == 0 %}Free{% else %}&dollar;{{ plan.price }}{% endif %}
                                    </p>
                                    <p class="text-sm text-gray-400 mt-1">
                                        {% if plan.duration_days == -1 %}Lifetime{% else %}{{ plan.duration_days }} days{% endif %}
                                    </p>
                                    <div class="mt-3 text-xs text-gray-300">
                                        {% if plan.features %}
                                            {% set features = plan.features | from_json %}
                                            {% for feature in features[:2] %}
                                                <div class="flex items-center justify-center mb-1">
                                                    <svg class="w-3 h-3 text-pepe-green mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                                    </svg>
                                                    {{ feature }}
                                                </div>
                                            {% endfor %}
                                        {% endif %}
                                    </div>
                                </div>
                            </label>
                        </div>
                    {% endfor %}
                </div>
            </div>
            
            <!-- Quantity and Bulk Options -->
            <div class="form-group">
                <div class="flex items-center justify-between mb-4">
                    <label class="form-label">Quantity</label>
                    <div class="flex items-center space-x-2">
                        <input type="checkbox" 
                               class="w-4 h-4 text-pepe-green bg-pepe-gray border-pepe-light-gray rounded focus:ring-pepe-green focus:ring-2"
                               x-model="bulkMode">
                        <span class="text-sm text-white">Bulk Mode</span>
                    </div>
                </div>
                
                <div x-show="!bulkMode">
                    <input type="number" 
                           name="quantity" 
                           class="input-field" 
                           min="1" 
                           max="100"
                           x-model="quantity"
                           @input="updateCost()"
                           placeholder="Number of keys to generate">
                    <p class="mt-1 text-sm text-gray-400">Generate 1-100 license keys at once.</p>
                </div>
                
                <div x-show="bulkMode">
                    <input type="number" 
                           name="bulk_quantity" 
                           class="input-field" 
                           min="1" 
                           max="10000"
                           x-model="quantity"
                           @input="updateCost()"
                           placeholder="Bulk quantity (up to 10,000)">
                    <p class="mt-1 text-sm text-gray-400">Generate up to 10,000 license keys for bulk distribution.</p>
                </div>
            </div>
            
            <!-- Expiry Settings -->
            <div class="form-group">
                <div class="flex items-center justify-between mb-4">
                    <label class="form-label">Expiry Settings</label>
                    <div class="flex items-center space-x-2">
                        <input type="checkbox" 
                               class="w-4 h-4 text-pepe-green bg-pepe-gray border-pepe-light-gray rounded focus:ring-pepe-green focus:ring-2"
                               x-model="customExpiry">
                        <span class="text-sm text-white">Custom Expiry</span>
                    </div>
                </div>
                
                <div x-show="!customExpiry">
                    <select name="expiry_preset" class="input-field">
                        <option value="7">7 Days (Trial)</option>
                        <option value="30" selected>30 Days (Standard)</option>
                        <option value="90">90 Days (Extended)</option>
                        <option value="365">1 Year</option>
                        <option value="-1">Lifetime</option>
                    </select>
                </div>
                
                <div x-show="customExpiry">
                    <input type="number" 
                           name="expiry_days" 
                           class="input-field" 
                           min="1" 
                           max="3650"
                           x-model="expiryDays"
                           placeholder="Custom expiry in days">
                    <p class="mt-1 text-sm text-gray-400">Set custom expiry period (1-3650 days, or -1 for lifetime).</p>
                </div>
            </div>
            
            <!-- HWID Binding -->
            <div class="form-group">
                <label class="form-label">Hardware ID Binding</label>
                <div class="flex items-center space-x-3">
                    <input type="checkbox" 
                           name="hwid_bind" 
                           class="w-4 h-4 text-pepe-green bg-pepe-gray border-pepe-light-gray rounded focus:ring-pepe-green focus:ring-2"
                           x-model="hwidBind"
                           checked>
                    <span class="text-white">Bind keys to hardware ID</span>
                </div>
                <p class="mt-2 text-sm text-gray-400">
                    When enabled, each license key will be bound to the user's hardware ID, preventing sharing across devices.
                </p>
            </div>
            
            <!-- Additional Options -->
            <div class="form-group">
                <label for="note" class="form-label">Notes (Optional)</label>
                <textarea name="note" 
                          class="input-field" 
                          rows="3" 
                          placeholder="Add any notes about these license keys..."></textarea>
                <p class="mt-1 text-sm text-gray-400">
                    Internal notes for your reference. Not visible to end users.
                </p>
            </div>
            
            <!-- Cost Estimate -->
            <div class="glass-card p-4 rounded-lg bg-pepe-darker/50" x-show="estimatedCost > 0">
                <div class="flex items-center justify-between">
                    <div>
                        <h4 class="text-white font-medium">Estimated Cost</h4>
                        <p class="text-sm text-gray-400">Based on selected plan and quantity</p>
                    </div>
                    <div class="text-right">
                        <p class="text-2xl font-bold text-pepe-green" x-text="'$' + estimatedCost.toFixed(2)"></p>
                        <p class="text-xs text-gray-400" x-text="quantity + ' keys'"></p>
                    </div>
                </div>
            </div>
            
            <!-- Preview -->
            <div class="form-group" x-show="selectedApp && quantity > 0">
                <label class="form-label">Preview</label>
                <div class="glass-card p-4 rounded-lg border border-pepe-green/30">
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div>
                            <span class="text-gray-400">Application:</span>
                            <span class="text-white ml-2" x-text="document.querySelector('select[name=app_id] option:checked')?.textContent || 'Not selected'"></span>
                        </div>
                        <div>
                            <span class="text-gray-400">Plan:</span>
                            <span class="text-white ml-2" x-text="document.querySelector('input[name=plan_id]:checked')?.nextElementSibling?.querySelector('h3')?.textContent || 'Not selected'"></span>
                        </div>
                        <div>
                            <span class="text-gray-400">Quantity:</span>
                            <span class="text-white ml-2" x-text="quantity + ' keys'"></span>
                        </div>
                        <div>
                            <span class="text-gray-400">HWID Binding:</span>
                            <span class="text-white ml-2" x-text="hwidBind ? 'Enabled' : 'Disabled'"></span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Form Actions -->
            <div class="flex items-center justify-between pt-6 border-t border-pepe-light-gray/20">
                <a href="{{ url_for('dashboard.index') }}" class="btn-secondary">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    Cancel
                </a>
                
                <button type="submit" 
                        class="btn-primary"
                        :disabled="!selectedApp || quantity < 1"
                        :class="{ 'opacity-50 cursor-not-allowed': !selectedApp || quantity < 1 }">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m0 0a2 2 0 012 2m-2-2a2 2 0 00-2 2m2-2V5a2 2 0 00-2-2m0 0H9a2 2 0 00-2 2v0a2 2 0 002 2h2m-4 4v6a2 2 0 002 2h2a2 2 0 002-2v-6m-4 0a2 2 0 012-2h0a2 2 0 012 2v0"></path>
                    </svg>
                    <span x-text="'Generate ' + quantity + ' License Key' + (quantity > 1 ? 's' : '')"></span>
                </button>
            </div>
        </form>
    </div>
    
    <!-- Help Section -->
    <div class="mt-8 glass-card p-6 rounded-xl">
        <h3 class="text-lg font-semibold text-white mb-4">
            <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            License Key Generation Guide
        </h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <h4 class="text-white font-medium mb-2">Plan Types</h4>
                <ul class="text-sm text-gray-300 space-y-1">
                    <li>• <strong>Trial:</strong> Free, limited features, 7 days</li>
                    <li>• <strong>Premium:</strong> Full features, 30 days</li>
                    <li>• <strong>Lifetime:</strong> Permanent access, all features</li>
                </ul>
            </div>
            <div>
                <h4 class="text-white font-medium mb-2">Bulk Generation</h4>
                <ul class="text-sm text-gray-300 space-y-1">
                    <li>• Standard: Up to 100 keys at once</li>
                    <li>• Bulk Mode: Up to 10,000 keys</li>
                    <li>• Keys are generated instantly</li>
                </ul>
            </div>
            <div>
                <h4 class="text-white font-medium mb-2">HWID Binding</h4>
                <ul class="text-sm text-gray-300 space-y-1">
                    <li>• Prevents key sharing across devices</li>
                    <li>• Users can reset HWID (limited times)</li>
                    <li>• Recommended for most applications</li>
                </ul>
            </div>
            <div>
                <h4 class="text-white font-medium mb-2">Expiry Options</h4>
                <ul class="text-sm text-gray-300 space-y-1">
                    <li>• Preset options for common durations</li>
                    <li>• Custom expiry up to 10 years</li>
                    <li>• Lifetime keys never expire</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Form validation
    const form = document.querySelector('form');
    
    form.addEventListener('submit', function(e) {
        const appSelect = document.querySelector('select[name="app_id"]');
        const quantity = document.querySelector('input[name="quantity"]') || document.querySelector('input[name="bulk_quantity"]');
        
        if (!appSelect.value) {
            e.preventDefault();
            alert('Please select an application.');
            appSelect.focus();
            return;
        }
        
        if (!quantity || quantity.value < 1) {
            e.preventDefault();
            alert('Please enter a valid quantity.');
            quantity?.focus();
            return;
        }
        
        // Show loading state
        const submitBtn = document.querySelector('button[type="submit"]');
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<div class="spinner mr-2"></div>Generating Keys...';
    });
});
</script>
{% endblock %}
