"""
Tests for admin functionality
"""

import pytest
import json
from app import query_db, execute_db

class TestAdminAccess:
    """Test admin access control."""
    
    def test_admin_panel_requires_login(self, client):
        """Test that admin panel requires authentication."""
        response = client.get('/admin/')
        assert response.status_code == 302
        assert '/auth/login' in response.location
    
    def test_admin_panel_requires_admin_privileges(self, client, auth, regular_user):
        """Test that admin panel requires admin privileges."""
        auth.login(regular_user['username'], regular_user['password'])
        response = client.get('/admin/')
        assert response.status_code == 302
        assert '/dashboard' in response.location
    
    def test_admin_panel_loads_for_admin(self, client, auth, admin_user):
        """Test that admin panel loads for admin user."""
        auth.login(admin_user['username'], admin_user['password'])
        response = client.get('/admin/')
        assert response.status_code == 200
        assert b'Admin' in response.data
        assert b'System Statistics' in response.data or b'users' in response.data

class TestAdminDashboard:
    """Test admin dashboard functionality."""
    
    def test_admin_dashboard_shows_stats(self, client, auth, admin_user, regular_user, test_app):
        """Test that admin dashboard shows system statistics."""
        auth.login(admin_user['username'], admin_user['password'])
        response = client.get('/admin/')
        assert response.status_code == 200
        
        # Should show system-wide statistics
        assert b'2' in response.data  # 2 users (admin + regular)
        assert b'1' in response.data  # 1 app (test_app)

class TestUserManagement:
    """Test admin user management functionality."""
    
    def test_admin_users_page_loads(self, client, auth, admin_user):
        """Test that admin users page loads."""
        auth.login(admin_user['username'], admin_user['password'])
        response = client.get('/admin/users')
        assert response.status_code == 200
        assert b'Manage Users' in response.data or b'users' in response.data
    
    def test_admin_can_view_all_users(self, client, auth, admin_user, regular_user):
        """Test that admin can view all users."""
        auth.login(admin_user['username'], admin_user['password'])
        response = client.get('/admin/users')
        assert response.status_code == 200
        
        # Should show both admin and regular user
        assert admin_user['username'].encode() in response.data
        assert regular_user['username'].encode() in response.data
    
    def test_admin_edit_user_page_loads(self, client, auth, admin_user, regular_user):
        """Test that admin can load user edit page."""
        auth.login(admin_user['username'], admin_user['password'])
        response = client.get(f'/admin/user/{regular_user["id"]}/edit')
        assert response.status_code == 200
        assert b'Edit User' in response.data or regular_user['username'].encode() in response.data
    
    def test_admin_can_edit_user(self, client, auth, admin_user, regular_user, app):
        """Test that admin can edit user details."""
        auth.login(admin_user['username'], admin_user['password'])
        
        response = client.post(f'/admin/user/{regular_user["id"]}/edit', data={
            'plan_id': 2,  # Change to Pro plan
            'is_admin': True
        })
        
        assert response.status_code == 302
        assert '/admin/users' in response.location
        
        # Verify user was updated
        with app.app_context():
            user_data = query_db(
                'SELECT * FROM user WHERE id = ?',
                (regular_user['id'],),
                one=True
            )
            assert user_data['plan_id'] == 2
            assert user_data['is_admin'] == 1
    
    def test_admin_can_delete_user(self, client, auth, admin_user, app):
        """Test that admin can delete users."""
        # Create a test user to delete
        with app.app_context():
            from app import bcrypt
            password_hash = bcrypt.generate_password_hash('test123').decode('utf-8')
            user_id = execute_db(
                'INSERT INTO user (email, username, password, plan_id) VALUES (?, ?, ?, ?)',
                ('<EMAIL>', 'deleteuser', password_hash, 1)
            )
        
        auth.login(admin_user['username'], admin_user['password'])
        
        response = client.post(f'/admin/user/{user_id}/delete')
        assert response.status_code == 302
        assert '/admin/users' in response.location
        
        # Verify user was deleted
        with app.app_context():
            user_data = query_db(
                'SELECT * FROM user WHERE id = ?',
                (user_id,),
                one=True
            )
            assert user_data is None
    
    def test_admin_cannot_delete_self(self, client, auth, admin_user, app):
        """Test that admin cannot delete their own account."""
        # Get admin user ID
        with app.app_context():
            admin_data = query_db(
                'SELECT * FROM user WHERE username = ?',
                (admin_user['username'],),
                one=True
            )
        
        auth.login(admin_user['username'], admin_user['password'])
        
        response = client.post(f'/admin/user/{admin_data["id"]}/delete')
        assert response.status_code == 302
        
        # Follow redirect to see error message
        response = client.post(f'/admin/user/{admin_data["id"]}/delete', follow_redirects=True)
        assert b'cannot delete your own account' in response.data

class TestAppManagement:
    """Test admin app management functionality."""
    
    def test_admin_apps_page_loads(self, client, auth, admin_user):
        """Test that admin apps page loads."""
        auth.login(admin_user['username'], admin_user['password'])
        response = client.get('/admin/apps')
        assert response.status_code == 200
        assert b'Manage Apps' in response.data or b'apps' in response.data
    
    def test_admin_can_view_all_apps(self, client, auth, admin_user, test_app, regular_user):
        """Test that admin can view all apps."""
        auth.login(admin_user['username'], admin_user['password'])
        response = client.get('/admin/apps')
        assert response.status_code == 200
        
        # Should show test_app and its owner
        assert test_app['name'].encode() in response.data
        assert regular_user['username'].encode() in response.data
    
    def test_admin_can_delete_app(self, client, auth, admin_user, test_app, test_license_key, app):
        """Test that admin can delete any app."""
        auth.login(admin_user['username'], admin_user['password'])
        
        response = client.post(f'/admin/app/{test_app["id"]}/delete')
        assert response.status_code == 302
        assert '/admin/apps' in response.location
        
        # Verify app and its keys were deleted
        with app.app_context():
            app_data = query_db(
                'SELECT * FROM app WHERE id = ?',
                (test_app['id'],),
                one=True
            )
            assert app_data is None
            
            key_data = query_db(
                'SELECT * FROM license_key WHERE id = ?',
                (test_license_key['id'],),
                one=True
            )
            assert key_data is None

class TestPlanManagement:
    """Test admin plan management functionality."""
    
    def test_admin_plans_page_loads(self, client, auth, admin_user):
        """Test that admin plans page loads."""
        auth.login(admin_user['username'], admin_user['password'])
        response = client.get('/admin/plans')
        assert response.status_code == 200
        assert b'Manage Plans' in response.data or b'plans' in response.data
    
    def test_admin_can_view_all_plans(self, client, auth, admin_user):
        """Test that admin can view all plans."""
        auth.login(admin_user['username'], admin_user['password'])
        response = client.get('/admin/plans')
        assert response.status_code == 200
        
        # Should show default plans
        assert b'Free' in response.data
        assert b'Pro' in response.data
        assert b'Military' in response.data
    
    def test_admin_create_plan_page_loads(self, client, auth, admin_user):
        """Test that admin can load create plan page."""
        auth.login(admin_user['username'], admin_user['password'])
        response = client.get('/admin/plan/create')
        assert response.status_code == 200
        assert b'Create Plan' in response.data
    
    def test_admin_can_create_plan(self, client, auth, admin_user, app):
        """Test that admin can create new plans."""
        auth.login(admin_user['username'], admin_user['password'])
        
        response = client.post('/admin/plan/create', data={
            'name': 'Enterprise',
            'max_apps': 10,
            'max_keys': 5000,
            'rate_limit': 5000
        })
        
        assert response.status_code == 302
        assert '/admin/plans' in response.location
        
        # Verify plan was created
        with app.app_context():
            plan_data = query_db(
                'SELECT * FROM plan WHERE name = ?',
                ('Enterprise',),
                one=True
            )
            assert plan_data is not None
            assert plan_data['max_apps'] == 10
            assert plan_data['max_keys'] == 5000
            assert plan_data['rate_limit'] == 5000
    
    def test_admin_edit_plan_page_loads(self, client, auth, admin_user):
        """Test that admin can load edit plan page."""
        auth.login(admin_user['username'], admin_user['password'])
        response = client.get('/admin/plan/1/edit')  # Free plan
        assert response.status_code == 200
        assert b'Edit Plan' in response.data
    
    def test_admin_can_edit_plan(self, client, auth, admin_user, app):
        """Test that admin can edit existing plans."""
        auth.login(admin_user['username'], admin_user['password'])
        
        response = client.post('/admin/plan/1/edit', data={  # Free plan
            'name': 'Free Updated',
            'max_apps': 2,
            'max_keys': 100,
            'rate_limit': 200
        })
        
        assert response.status_code == 302
        assert '/admin/plans' in response.location
        
        # Verify plan was updated
        with app.app_context():
            plan_data = query_db(
                'SELECT * FROM plan WHERE id = ?',
                (1,),
                one=True
            )
            assert plan_data['name'] == 'Free Updated'
            assert plan_data['max_apps'] == 2
            assert plan_data['max_keys'] == 100
            assert plan_data['rate_limit'] == 200
    
    def test_admin_cannot_delete_default_plans(self, client, auth, admin_user):
        """Test that admin cannot delete default plans."""
        auth.login(admin_user['username'], admin_user['password'])
        
        # Try to delete Free plan (id=1)
        response = client.post('/admin/plan/1/delete')
        assert response.status_code == 302
        
        # Follow redirect to see error message
        response = client.post('/admin/plan/1/delete', follow_redirects=True)
        assert b'Cannot delete default plans' in response.data
    
    def test_admin_cannot_delete_plan_in_use(self, client, auth, admin_user, regular_user, app):
        """Test that admin cannot delete plans that are in use."""
        # Create a custom plan
        with app.app_context():
            plan_id = execute_db(
                'INSERT INTO plan (name, max_apps, max_keys, rate_limit) VALUES (?, ?, ?, ?)',
                ('TestPlan', 5, 500, 500)
            )
            
            # Assign user to this plan
            execute_db(
                'UPDATE user SET plan_id = ? WHERE id = ?',
                (plan_id, regular_user['id'])
            )
        
        auth.login(admin_user['username'], admin_user['password'])
        
        response = client.post(f'/admin/plan/{plan_id}/delete')
        assert response.status_code == 302
        
        # Follow redirect to see error message
        response = client.post(f'/admin/plan/{plan_id}/delete', follow_redirects=True)
        assert b'Cannot delete plan' in response.data
        assert b'users are currently using it' in response.data

class TestAdminAPI:
    """Test admin API endpoints."""
    
    def test_admin_api_stats_requires_admin(self, client, auth, regular_user):
        """Test that admin API requires admin privileges."""
        auth.login(regular_user['username'], regular_user['password'])
        response = client.get('/admin/api/stats')
        assert response.status_code == 302
    
    def test_admin_api_stats(self, client, auth, admin_user, regular_user, test_app, test_license_key):
        """Test admin stats API endpoint."""
        auth.login(admin_user['username'], admin_user['password'])
        
        response = client.get('/admin/api/stats')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert data['users'] == 2  # admin + regular user
        assert data['apps'] == 1   # test_app
        assert data['keys'] == 1   # test_license_key
        assert data['used_keys'] == 0

class TestAdminSearch:
    """Test admin search functionality."""
    
    def test_admin_user_search(self, client, auth, admin_user, regular_user):
        """Test admin user search functionality."""
        auth.login(admin_user['username'], admin_user['password'])
        
        response = client.get(f'/admin/users?search={regular_user["username"]}')
        assert response.status_code == 200
        assert regular_user['username'].encode() in response.data
        # Admin user should not appear in search results
        assert admin_user['username'].encode() not in response.data or regular_user['username'] in admin_user['username']
    
    def test_admin_app_search(self, client, auth, admin_user, test_app):
        """Test admin app search functionality."""
        auth.login(admin_user['username'], admin_user['password'])
        
        response = client.get(f'/admin/apps?search={test_app["name"]}')
        assert response.status_code == 200
        assert test_app['name'].encode() in response.data
