{% extends "base.html" %}

{% block title %}Register - PepeAuth{% endblock %}

{% block main_class %}flex items-center justify-center min-h-screen py-12 px-4 sm:px-6 lg:px-8{% endblock %}

{% block content %}
<div class="max-w-md w-full space-y-8">
    <div class="text-center">
        <div class="flex justify-center">
            <div class="w-16 h-16 bg-pepe-green rounded-2xl flex items-center justify-center mb-6">
                <span class="text-white font-bold text-2xl">P</span>
            </div>
        </div>
        <h2 class="text-3xl font-bold text-white">Create your account</h2>
        <p class="mt-2 text-gray-300">Start managing your licenses today</p>
    </div>
    
    <div class="glass-card p-8 rounded-2xl">
        <form method="POST" class="space-y-6">
            {{ form.hidden_tag() }}
            
            <div>
                {{ form.email.label(class="block text-sm font-medium text-white mb-2") }}
                {{ form.email(class="input-field", placeholder="<EMAIL>") }}
                {% if form.email.errors %}
                    <div class="mt-1 text-sm text-red-400">
                        {% for error in form.email.errors %}
                            <p>{{ error }}</p>
                        {% endfor %}
                    </div>
                {% endif %}
            </div>
            
            <div>
                {{ form.username.label(class="block text-sm font-medium text-white mb-2") }}
                {{ form.username(class="input-field", placeholder="username") }}
                {% if form.username.errors %}
                    <div class="mt-1 text-sm text-red-400">
                        {% for error in form.username.errors %}
                            <p>{{ error }}</p>
                        {% endfor %}
                    </div>
                {% endif %}
            </div>
            
            <div>
                {{ form.password.label(class="block text-sm font-medium text-white mb-2") }}
                {{ form.password(class="input-field") }}
                {% if form.password.errors %}
                    <div class="mt-1 text-sm text-red-400">
                        {% for error in form.password.errors %}
                            <p>{{ error }}</p>
                        {% endfor %}
                    </div>
                {% endif %}
            </div>
            
            <div>
                {{ form.confirm_password.label(class="block text-sm font-medium text-white mb-2") }}
                {{ form.confirm_password(class="input-field") }}
                {% if form.confirm_password.errors %}
                    <div class="mt-1 text-sm text-red-400">
                        {% for error in form.confirm_password.errors %}
                            <p>{{ error }}</p>
                        {% endfor %}
                    </div>
                {% endif %}
            </div>
            
            <div class="text-xs text-gray-300">
                <p>By creating an account, you agree to our 
                   <a href="#" class="text-pepe-green hover:text-pepe-green/80">Terms of Service</a> 
                   and <a href="#" class="text-pepe-green hover:text-pepe-green/80">Privacy Policy</a>.
                </p>
            </div>
            
            <div>
                <button type="submit" class="btn-primary w-full">
                    Create Account
                </button>
            </div>
        </form>
        
        <div class="mt-6">
            <div class="relative">
                <div class="absolute inset-0 flex items-center">
                    <div class="w-full border-t border-gray-600"></div>
                </div>
                <div class="relative flex justify-center text-sm">
                    <span class="px-2 bg-pepe-gray text-gray-300">Already have an account?</span>
                </div>
            </div>
            
            <div class="mt-6">
                <a href="{{ url_for('auth.login') }}" class="btn-secondary w-full text-center">
                    Sign In
                </a>
            </div>
        </div>
    </div>
    
    <!-- Features Preview -->
    <div class="glass-card p-4 rounded-lg">
        <h3 class="text-sm font-medium text-white mb-2">What you get with Free plan:</h3>
        <ul class="text-xs text-gray-300 space-y-1">
            <li>✓ 1 App</li>
            <li>✓ 50 License Keys</li>
            <li>✓ 100 API Calls/day</li>
            <li>✓ Basic Analytics</li>
        </ul>
    </div>
</div>
{% endblock %}
