#!/usr/bin/env python3
"""
Setup Premium license system with test data
Creates admin user, Premium plans, and test license keys
"""

from app import create_app, get_db, execute_db, query_db
from flask_bcrypt import Bcrypt
import secrets
import string
from datetime import datetime, timed<PERSON><PERSON>

def generate_license_key(length=16):
    """Generate a random license key"""
    chars = string.ascii_uppercase + string.digits
    return ''.join(secrets.choice(chars) for _ in range(length))

def setup_premium_system():
    """Set up the Premium license system"""
    app = create_app()
    bcrypt = Bcrypt(app)
    
    with app.app_context():
        print("🚀 Setting up Premium License System")
        print("=" * 50)
        
        # Create admin user
        admin_password = bcrypt.generate_password_hash('admin123').decode('utf-8')
        
        try:
            admin_id = execute_db(
                'INSERT INTO user (email, username, password, plan_id, is_admin) VALUES (?, ?, ?, ?, ?)',
                ('<EMAIL>', 'admin', admin_password, 2, 1)  # Premium plan (id=2)
            )
            print(f"✅ Created admin user (ID: {admin_id})")
        except Exception as e:
            print(f"⚠️  Admin user might already exist: {e}")
            admin_data = query_db('SELECT id FROM user WHERE username = ?', ('admin',), one=True)
            admin_id = admin_data['id'] if admin_data else 1
        
        # Create test Premium user
        user_password = bcrypt.generate_password_hash('user123').decode('utf-8')
        
        try:
            user_id = execute_db(
                'INSERT INTO user (email, username, password, plan_id, is_admin) VALUES (?, ?, ?, ?, ?)',
                ('<EMAIL>', 'testuser', user_password, 2, 0)  # Premium plan
            )
            print(f"✅ Created test user (ID: {user_id})")
        except Exception as e:
            print(f"⚠️  Test user might already exist: {e}")
            user_data = query_db('SELECT id FROM user WHERE username = ?', ('testuser',), one=True)
            user_id = user_data['id'] if user_data else 2
        
        # Create test license keys for Premium plan
        premium_keys = []
        for i in range(5):
            key = generate_license_key()
            
            try:
                key_id = execute_db(
                    'INSERT INTO license_key (key, plan_id, created_by, expiry_days) VALUES (?, ?, ?, ?)',
                    (key, 2, admin_id, 30)  # Premium plan, 30 days
                )
                premium_keys.append(key)
                print(f"✅ Created Premium license key: {key}")
            except Exception as e:
                print(f"❌ Failed to create key: {e}")
        
        # Create one activated key for testing
        if premium_keys:
            test_key = premium_keys[0]
            expires_at = datetime.now() + timedelta(days=30)
            
            execute_db(
                'UPDATE license_key SET used = 1, used_at = ?, hwid = ?, expires_at = ? WHERE key = ?',
                (datetime.now().isoformat(), 'TEST-HWID-123', expires_at.isoformat(), test_key)
            )
            print(f"✅ Activated test key: {test_key}")
        
        # Display system statistics
        print("\n📊 System Statistics:")
        print("-" * 30)
        
        # Count users by plan
        users_stats = query_db(
            '''SELECT p.name, COUNT(u.id) as count 
               FROM plan p 
               LEFT JOIN user u ON p.id = u.plan_id 
               GROUP BY p.id, p.name'''
        )
        
        for stat in users_stats:
            print(f"  {stat['name']} users: {stat['count']}")
        
        # Count license keys
        key_stats = query_db(
            '''SELECT 
                COUNT(*) as total,
                COUNT(CASE WHEN used = 1 THEN 1 END) as used,
                COUNT(CASE WHEN expires_at < ? THEN 1 END) as expired
               FROM license_key''',
            (datetime.now().isoformat(),),
            one=True
        )
        
        print(f"  Total license keys: {key_stats['total']}")
        print(f"  Used keys: {key_stats['used']}")
        print(f"  Unused keys: {key_stats['total'] - key_stats['used']}")
        print(f"  Expired keys: {key_stats['expired']}")
        
        print("\n🔑 Test Credentials:")
        print("-" * 30)
        print("  Admin: <EMAIL> / admin123")
        print("  User:  <EMAIL> / user123")
        
        if premium_keys:
            print(f"\n🎫 Test License Keys:")
            print("-" * 30)
            for key in premium_keys[:3]:  # Show first 3 keys
                print(f"  {key}")
        
        print("\n🎉 Premium system setup complete!")

if __name__ == "__main__":
    setup_premium_system()
