import uuid
import secrets
from datetime import datetime, timedelta
from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from app import query_db, execute_db
from app.forms import AppForm, KeyGenerationForm

bp = Blueprint('dashboard', __name__, url_prefix='/dashboard')

@bp.route('/')
@login_required
def index():
    """Main dashboard page"""
    # Get user's apps
    apps = query_db(
        'SELECT a.*, COUNT(lk.id) as key_count, '
        'COUNT(CASE WHEN lk.used = 1 THEN 1 END) as used_keys '
        'FROM app a LEFT JOIN license_key lk ON a.id = lk.app_id '
        'WHERE a.owner_id = ? GROUP BY a.id ORDER BY a.created_at DESC',
        (current_user.id,)
    )
    
    # Get user statistics
    stats = {
        'total_apps': len(apps),
        'total_keys': sum(app['key_count'] for app in apps),
        'used_keys': sum(app['used_keys'] for app in apps),
        'plan': current_user.get_plan()
    }
    
    return render_template('dashboard.html', apps=apps, stats=stats)

@bp.route('/create-app', methods=['GET', 'POST'])
@login_required
def create_app():
    """Create a new app"""
    if not current_user.can_create_app():
        flash('You have reached your app limit for your current plan.', 'error')
        return redirect(url_for('dashboard.index'))
    
    form = AppForm()
    if form.validate_on_submit():
        name = form.name.data.strip()
        hwid_lock = form.hwid_lock.data
        expiry_days = form.expiry_days.data
        
        # Check if app name already exists for this user
        existing_app = query_db(
            'SELECT id FROM app WHERE name = ? AND owner_id = ?',
            (name, current_user.id),
            one=True
        )
        
        if existing_app:
            flash('You already have an app with this name.', 'error')
            return render_template('create_app.html', form=form)
        
        # Generate unique secret
        secret = secrets.token_urlsafe(32)
        
        try:
            app_id = execute_db(
                'INSERT INTO app (name, owner_id, secret, hwid_lock, expiry_days) '
                'VALUES (?, ?, ?, ?, ?)',
                (name, current_user.id, secret, hwid_lock, expiry_days)
            )
            
            flash(f'App "{name}" created successfully!', 'success')
            return redirect(url_for('dashboard.app_detail', app_id=app_id))
            
        except Exception as e:
            flash('Failed to create app. Please try again.', 'error')
    
    return render_template('create_app.html', form=form)

@bp.route('/app/<int:app_id>')
@login_required
def app_detail(app_id):
    """App detail page"""
    # Get app details
    app = query_db(
        'SELECT * FROM app WHERE id = ? AND owner_id = ?',
        (app_id, current_user.id),
        one=True
    )
    
    if not app:
        flash('App not found.', 'error')
        return redirect(url_for('dashboard.index'))
    
    # Get license keys for this app
    keys = query_db(
        'SELECT * FROM license_key WHERE app_id = ? ORDER BY created_at DESC',
        (app_id,)
    )
    
    # Get key statistics
    key_stats = {
        'total': len(keys),
        'used': len([k for k in keys if k['used']]),
        'unused': len([k for k in keys if not k['used']]),
        'expired': len([k for k in keys if k['expires_at'] and 
                       datetime.fromisoformat(k['expires_at']) < datetime.now()])
    }
    
    return render_template('app_detail.html', app=app, keys=keys, key_stats=key_stats)

@bp.route('/app/<int:app_id>/generate-keys', methods=['GET', 'POST'])
@login_required
def generate_keys(app_id):
    """Generate license keys for an app"""
    # Verify app ownership
    app = query_db(
        'SELECT * FROM app WHERE id = ? AND owner_id = ?',
        (app_id, current_user.id),
        one=True
    )
    
    if not app:
        flash('App not found.', 'error')
        return redirect(url_for('dashboard.index'))
    
    form = KeyGenerationForm()
    if form.validate_on_submit():
        quantity = form.quantity.data
        note = form.note.data
        
        # Check if user can create more keys
        if not current_user.can_create_key(app_id):
            flash('You have reached your license key limit for your current plan.', 'error')
            return redirect(url_for('dashboard.app_detail', app_id=app_id))
        
        # Generate keys
        generated_keys = []
        try:
            for _ in range(quantity):
                key = str(uuid.uuid4())
                key_id = execute_db(
                    'INSERT INTO license_key (key, app_id) VALUES (?, ?)',
                    (key, app_id)
                )
                generated_keys.append(key)
            
            flash(f'Successfully generated {quantity} license key(s)!', 'success')
            return render_template('generated_keys.html', 
                                 keys=generated_keys, 
                                 app=app, 
                                 note=note)
            
        except Exception as e:
            flash('Failed to generate keys. Please try again.', 'error')
    
    return render_template('generate_keys.html', form=form, app=app)

@bp.route('/app/<int:app_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_app(app_id):
    """Edit app settings"""
    app = query_db(
        'SELECT * FROM app WHERE id = ? AND owner_id = ?',
        (app_id, current_user.id),
        one=True
    )
    
    if not app:
        flash('App not found.', 'error')
        return redirect(url_for('dashboard.index'))
    
    form = AppForm(obj=app)
    if form.validate_on_submit():
        try:
            execute_db(
                'UPDATE app SET name = ?, hwid_lock = ?, expiry_days = ? WHERE id = ?',
                (form.name.data.strip(), form.hwid_lock.data, 
                 form.expiry_days.data, app_id)
            )
            
            flash('App settings updated successfully!', 'success')
            return redirect(url_for('dashboard.app_detail', app_id=app_id))
            
        except Exception as e:
            flash('Failed to update app settings. Please try again.', 'error')
    
    return render_template('edit_app.html', form=form, app=app)

@bp.route('/app/<int:app_id>/delete', methods=['POST'])
@login_required
def delete_app(app_id):
    """Delete an app and all its keys"""
    app = query_db(
        'SELECT * FROM app WHERE id = ? AND owner_id = ?',
        (app_id, current_user.id),
        one=True
    )
    
    if not app:
        flash('App not found.', 'error')
        return redirect(url_for('dashboard.index'))
    
    try:
        # Delete all license keys first (due to foreign key constraint)
        execute_db('DELETE FROM license_key WHERE app_id = ?', (app_id,))
        # Delete the app
        execute_db('DELETE FROM app WHERE id = ?', (app_id,))
        
        flash(f'App "{app["name"]}" and all its license keys have been deleted.', 'success')
        
    except Exception as e:
        flash('Failed to delete app. Please try again.', 'error')
    
    return redirect(url_for('dashboard.index'))

@bp.route('/key/<int:key_id>/delete', methods=['POST'])
@login_required
def delete_key(key_id):
    """Delete a specific license key"""
    # Verify key ownership through app
    key_data = query_db(
        'SELECT lk.*, a.owner_id, a.id as app_id FROM license_key lk '
        'JOIN app a ON lk.app_id = a.id '
        'WHERE lk.id = ? AND a.owner_id = ?',
        (key_id, current_user.id),
        one=True
    )
    
    if not key_data:
        flash('License key not found.', 'error')
        return redirect(url_for('dashboard.index'))
    
    try:
        execute_db('DELETE FROM license_key WHERE id = ?', (key_id,))
        flash('License key deleted successfully.', 'success')
        
    except Exception as e:
        flash('Failed to delete license key. Please try again.', 'error')
    
    return redirect(url_for('dashboard.app_detail', app_id=key_data['app_id']))

@bp.route('/api/stats')
@login_required
def api_stats():
    """API endpoint for dashboard statistics"""
    apps = query_db(
        'SELECT COUNT(*) as count FROM app WHERE owner_id = ?',
        (current_user.id,),
        one=True
    )
    
    keys = query_db(
        'SELECT COUNT(*) as total, COUNT(CASE WHEN used = 1 THEN 1 END) as used '
        'FROM license_key lk JOIN app a ON lk.app_id = a.id '
        'WHERE a.owner_id = ?',
        (current_user.id,),
        one=True
    )
    
    return jsonify({
        'apps': apps['count'],
        'total_keys': keys['total'],
        'used_keys': keys['used'],
        'unused_keys': keys['total'] - keys['used']
    })
