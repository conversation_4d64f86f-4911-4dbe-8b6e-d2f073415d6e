#!/usr/bin/env python3
"""
PepeAuth Setup Script
Automated setup and configuration for PepeAuth
"""

import os
import sys
import subprocess
import secrets
import sqlite3
from pathlib import Path

def print_banner():
    """Print setup banner"""
    print("=" * 60)
    print("🐸 PepeAuth Setup Script")
    print("=" * 60)
    print()

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required!")
        print(f"Current version: {sys.version}")
        sys.exit(1)
    print(f"✅ Python version: {sys.version.split()[0]}")

def install_dependencies():
    """Install required dependencies"""
    print("📦 Installing dependencies...")
    
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ])
        print("✅ Dependencies installed successfully!")
    except subprocess.CalledProcessError:
        print("❌ Failed to install dependencies!")
        sys.exit(1)

def create_env_file():
    """Create .env file with configuration"""
    print("⚙️  Creating configuration file...")
    
    env_content = f"""# PepeAuth Configuration
# Generated by setup script

# Application Settings
SECRET_KEY={secrets.token_urlsafe(32)}
DEBUG=True
HOST=0.0.0.0
PORT=5000

# Database
DATABASE_URL=sqlite:///pepeauth.db

# Security Settings
MAX_HWID_RESETS=3
SESSION_TIMEOUT=3600

# Rate Limiting (optional - requires Redis)
# REDIS_URL=redis://localhost:6379/0
# RATE_LIMIT_STORAGE_URL=redis://localhost:6379/1

# Email Settings (optional)
# MAIL_SERVER=smtp.gmail.com
# MAIL_PORT=587
# MAIL_USERNAME=<EMAIL>
# MAIL_PASSWORD=your-app-password
# MAIL_USE_TLS=True

# Logging
LOG_LEVEL=INFO
LOG_DIR=logs

# Webhook Settings
WEBHOOK_TIMEOUT=10
WEBHOOK_RETRIES=3
"""
    
    with open('.env', 'w') as f:
        f.write(env_content)
    
    print("✅ Configuration file created (.env)")

def setup_database():
    """Initialize the database"""
    print("🗄️  Setting up database...")
    
    try:
        # Import and run database initialization
        from init_db import init_database
        init_database()
        print("✅ Database initialized successfully!")
    except Exception as e:
        print(f"❌ Failed to initialize database: {str(e)}")
        sys.exit(1)

def create_admin_user():
    """Create initial admin user"""
    print("👤 Creating admin user...")
    
    username = input("Enter admin username (default: admin): ").strip() or "admin"
    email = input("Enter admin email: ").strip()
    
    if not email:
        print("❌ Email is required!")
        return False
    
    password = input("Enter admin password: ").strip()
    if not password:
        print("❌ Password is required!")
        return False
    
    try:
        # Connect to database and create admin user
        conn = sqlite3.connect('pepeauth.db')
        cursor = conn.cursor()
        
        # Hash password (simplified for setup)
        import hashlib
        password_hash = hashlib.sha256(password.encode()).hexdigest()
        
        # Insert admin user
        cursor.execute("""
            INSERT INTO user (username, email, password_hash, is_admin, plan_id, created_at)
            VALUES (?, ?, ?, 1, 3, datetime('now'))
        """, (username, email, password_hash))
        
        conn.commit()
        conn.close()
        
        print(f"✅ Admin user '{username}' created successfully!")
        return True
        
    except sqlite3.IntegrityError:
        print(f"❌ User '{username}' already exists!")
        return False
    except Exception as e:
        print(f"❌ Failed to create admin user: {str(e)}")
        return False

def create_directories():
    """Create necessary directories"""
    print("📁 Creating directories...")
    
    directories = ['logs', 'uploads', 'backups']
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✅ Created directory: {directory}")

def setup_systemd_service():
    """Create systemd service file (Linux only)"""
    if sys.platform != 'linux':
        return
    
    create_service = input("Create systemd service? (y/N): ").strip().lower()
    if create_service != 'y':
        return
    
    print("🔧 Creating systemd service...")
    
    current_dir = os.path.abspath('.')
    python_path = sys.executable
    
    service_content = f"""[Unit]
Description=PepeAuth License Management System
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory={current_dir}
Environment=PATH={os.path.dirname(python_path)}
ExecStart={python_path} run.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
"""
    
    service_file = '/etc/systemd/system/pepeauth.service'
    
    try:
        with open('pepeauth.service', 'w') as f:
            f.write(service_content)
        
        print(f"✅ Service file created: pepeauth.service")
        print(f"To install: sudo cp pepeauth.service {service_file}")
        print("To enable: sudo systemctl enable pepeauth")
        print("To start: sudo systemctl start pepeauth")
        
    except Exception as e:
        print(f"❌ Failed to create service file: {str(e)}")

def print_completion_message():
    """Print setup completion message"""
    print()
    print("=" * 60)
    print("🎉 PepeAuth Setup Complete!")
    print("=" * 60)
    print()
    print("Next steps:")
    print("1. Review the configuration in .env file")
    print("2. Start the application: python run.py")
    print("3. Open http://localhost:5000 in your browser")
    print("4. Login with your admin credentials")
    print()
    print("For production deployment:")
    print("- Set DEBUG=False in .env")
    print("- Use a production database (PostgreSQL/MySQL)")
    print("- Set up Redis for rate limiting")
    print("- Configure email settings")
    print("- Use a reverse proxy (nginx)")
    print()
    print("Documentation: https://github.com/yourusername/pepeauth")
    print("Support: https://discord.gg/pepeauth")
    print()

def main():
    """Main setup function"""
    print_banner()
    
    # Check requirements
    check_python_version()
    
    # Install dependencies
    install_dependencies()
    
    # Create configuration
    if not os.path.exists('.env'):
        create_env_file()
    else:
        print("⚠️  Configuration file (.env) already exists, skipping...")
    
    # Create directories
    create_directories()
    
    # Setup database
    if not os.path.exists('pepeauth.db'):
        setup_database()
        
        # Create admin user
        while not create_admin_user():
            retry = input("Try again? (y/N): ").strip().lower()
            if retry != 'y':
                break
    else:
        print("⚠️  Database already exists, skipping initialization...")
    
    # Optional systemd service
    setup_systemd_service()
    
    # Completion message
    print_completion_message()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n❌ Setup cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ Setup failed: {str(e)}")
        sys.exit(1)
