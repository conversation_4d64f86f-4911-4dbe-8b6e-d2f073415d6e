-- PepeAuth Database Schema
-- KeyAuth-style license management system

-- Plans table
CREATE TABLE IF NOT EXISTS plan (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name TEXT UNIQUE NOT NULL,
  max_apps INTEGER NOT NULL,
  max_keys INTEGER NOT NULL,
  rate_limit INTEGER NOT NULL DEFAULT 100,
  price DECIMAL(10,2) DEFAULT 0.00,
  duration_days INTEGER NOT NULL DEFAULT 30,
  features TEXT, -- JSON string of features
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Insert default plans
INSERT OR IGNORE INTO plan (name, max_apps, max_keys, rate_limit, price, duration_days, features) VALUES
  ('Trial', 1, 10, 50, 0.00, 7, '["Basic API Access", "HWID Binding", "Email Support"]'),
  ('Premium', 5, 500, 1000, 9.99, 30, '["Full API Access", "HWID Binding", "Webhooks", "Priority Support"]'),
  ('Lifetime', -1, -1, -1, 49.99, -1, '["Unlimited Everything", "Priority Support", "Custom Features"]');

-- Users table
CREATE TABLE IF NOT EXISTS user (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  email TEXT UNIQUE NOT NULL,
  username TEXT UNIQUE NOT NULL,
  password TEXT NOT NULL,
  plan_id INTEGER NOT NULL DEFAULT 1, -- Default to Trial plan
  is_admin INTEGER NOT NULL DEFAULT 0,
  is_banned INTEGER NOT NULL DEFAULT 0,
  ban_reason TEXT,
  hwid_reset_count INTEGER NOT NULL DEFAULT 0,
  max_hwid_resets INTEGER NOT NULL DEFAULT 3,
  last_login DATETIME,
  last_ip TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (plan_id) REFERENCES plan(id)
);

-- Apps table
CREATE TABLE IF NOT EXISTS app (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name TEXT NOT NULL,
  owner_id INTEGER NOT NULL,
  secret TEXT UNIQUE NOT NULL,
  hwid_lock INTEGER NOT NULL DEFAULT 1,
  expiry_days INTEGER NOT NULL DEFAULT 30,
  webhook_url TEXT,
  webhook_secret TEXT,
  is_active INTEGER NOT NULL DEFAULT 1,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (owner_id) REFERENCES user(id) ON DELETE CASCADE
);

-- License keys table
CREATE TABLE IF NOT EXISTS license_key (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  key TEXT UNIQUE NOT NULL,
  app_id INTEGER,
  user_id INTEGER,
  plan_id INTEGER NOT NULL DEFAULT 1, -- Default to Trial plan
  created_by INTEGER, -- Admin who created the key
  expiry_days INTEGER NOT NULL DEFAULT 30,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  used INTEGER NOT NULL DEFAULT 0,
  used_at DATETIME,
  hwid TEXT,
  expires_at DATETIME,
  is_banned INTEGER NOT NULL DEFAULT 0,
  ban_reason TEXT,
  note TEXT, -- Admin notes
  FOREIGN KEY (app_id) REFERENCES app(id) ON DELETE CASCADE,
  FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE SET NULL,
  FOREIGN KEY (plan_id) REFERENCES plan(id),
  FOREIGN KEY (created_by) REFERENCES user(id) ON DELETE SET NULL
);

-- HWID Reset Logs table
CREATE TABLE IF NOT EXISTS hwid_reset_log (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  user_id INTEGER NOT NULL,
  license_key_id INTEGER,
  old_hwid TEXT,
  new_hwid TEXT,
  reset_by INTEGER, -- Admin who performed reset, NULL if self-reset
  reason TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE,
  FOREIGN KEY (license_key_id) REFERENCES license_key(id) ON DELETE CASCADE,
  FOREIGN KEY (reset_by) REFERENCES user(id) ON DELETE SET NULL
);

-- Authentication Logs table
CREATE TABLE IF NOT EXISTS auth_log (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  license_key_id INTEGER,
  app_id INTEGER,
  user_id INTEGER,
  hwid TEXT,
  ip_address TEXT,
  user_agent TEXT,
  success INTEGER NOT NULL DEFAULT 0,
  failure_reason TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (license_key_id) REFERENCES license_key(id) ON DELETE CASCADE,
  FOREIGN KEY (app_id) REFERENCES app(id) ON DELETE CASCADE,
  FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE SET NULL
);

-- Webhook Logs table
CREATE TABLE IF NOT EXISTS webhook_log (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  app_id INTEGER NOT NULL,
  event_type TEXT NOT NULL, -- 'auth_success', 'auth_fail', 'key_created', etc.
  payload TEXT, -- JSON payload sent
  response_code INTEGER,
  response_body TEXT,
  success INTEGER NOT NULL DEFAULT 0,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (app_id) REFERENCES app(id) ON DELETE CASCADE
);

-- System Logs table
CREATE TABLE IF NOT EXISTS system_log (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  level TEXT NOT NULL, -- 'INFO', 'WARNING', 'ERROR', 'CRITICAL'
  category TEXT NOT NULL, -- 'AUTH', 'ADMIN', 'API', 'WEBHOOK', etc.
  message TEXT NOT NULL,
  user_id INTEGER,
  app_id INTEGER,
  ip_address TEXT,
  user_agent TEXT,
  extra_data TEXT, -- JSON for additional context
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE SET NULL,
  FOREIGN KEY (app_id) REFERENCES app(id) ON DELETE SET NULL
);

-- API Rate Limiting table
CREATE TABLE IF NOT EXISTS rate_limit_log (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  identifier TEXT NOT NULL, -- IP address or API key
  endpoint TEXT NOT NULL,
  requests_count INTEGER NOT NULL DEFAULT 1,
  window_start DATETIME NOT NULL,
  blocked INTEGER NOT NULL DEFAULT 0,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_user_email ON user(email);
CREATE INDEX IF NOT EXISTS idx_user_username ON user(username);
CREATE INDEX IF NOT EXISTS idx_user_plan ON user(plan_id);
CREATE INDEX IF NOT EXISTS idx_user_banned ON user(is_banned);
CREATE INDEX IF NOT EXISTS idx_user_last_login ON user(last_login);

CREATE INDEX IF NOT EXISTS idx_app_owner ON app(owner_id);
CREATE INDEX IF NOT EXISTS idx_app_secret ON app(secret);
CREATE INDEX IF NOT EXISTS idx_app_active ON app(is_active);

CREATE INDEX IF NOT EXISTS idx_license_key_key ON license_key(key);
CREATE INDEX IF NOT EXISTS idx_license_key_app ON license_key(app_id);
CREATE INDEX IF NOT EXISTS idx_license_key_user ON license_key(user_id);
CREATE INDEX IF NOT EXISTS idx_license_key_plan ON license_key(plan_id);
CREATE INDEX IF NOT EXISTS idx_license_key_used ON license_key(used);
CREATE INDEX IF NOT EXISTS idx_license_key_expires ON license_key(expires_at);
CREATE INDEX IF NOT EXISTS idx_license_key_banned ON license_key(is_banned);

CREATE INDEX IF NOT EXISTS idx_hwid_reset_user ON hwid_reset_log(user_id);
CREATE INDEX IF NOT EXISTS idx_hwid_reset_key ON hwid_reset_log(license_key_id);
CREATE INDEX IF NOT EXISTS idx_hwid_reset_date ON hwid_reset_log(created_at);

CREATE INDEX IF NOT EXISTS idx_auth_log_key ON auth_log(license_key_id);
CREATE INDEX IF NOT EXISTS idx_auth_log_app ON auth_log(app_id);
CREATE INDEX IF NOT EXISTS idx_auth_log_success ON auth_log(success);
CREATE INDEX IF NOT EXISTS idx_auth_log_date ON auth_log(created_at);
CREATE INDEX IF NOT EXISTS idx_auth_log_ip ON auth_log(ip_address);

CREATE INDEX IF NOT EXISTS idx_webhook_log_app ON webhook_log(app_id);
CREATE INDEX IF NOT EXISTS idx_webhook_log_event ON webhook_log(event_type);
CREATE INDEX IF NOT EXISTS idx_webhook_log_success ON webhook_log(success);
CREATE INDEX IF NOT EXISTS idx_webhook_log_date ON webhook_log(created_at);

CREATE INDEX IF NOT EXISTS idx_system_log_level ON system_log(level);
CREATE INDEX IF NOT EXISTS idx_system_log_category ON system_log(category);
CREATE INDEX IF NOT EXISTS idx_system_log_user ON system_log(user_id);
CREATE INDEX IF NOT EXISTS idx_system_log_date ON system_log(created_at);

CREATE INDEX IF NOT EXISTS idx_rate_limit_identifier ON rate_limit_log(identifier);
CREATE INDEX IF NOT EXISTS idx_rate_limit_endpoint ON rate_limit_log(endpoint);
CREATE INDEX IF NOT EXISTS idx_rate_limit_window ON rate_limit_log(window_start);

-- Insert default admin user (password: admin123)
-- Note: In production, change this password immediately!
INSERT OR IGNORE INTO user (email, username, password, plan_id, is_admin) VALUES
  ('<EMAIL>', 'admin', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj3bp.Gm.F5e', 3, 1);

-- Create a sample app for testing
INSERT OR IGNORE INTO app (name, owner_id, secret, hwid_lock, expiry_days)
SELECT 'Sample App', id, 'sample_secret_' || hex(randomblob(16)), 1, 30
FROM user WHERE username = 'admin' LIMIT 1;
