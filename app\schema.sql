-- PepeAuth Database Schema
-- KeyAuth-style license management system

-- Plans table
CREATE TABLE IF NOT EXISTS plan (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name TEXT UNIQUE NOT NULL,
  max_apps INTEGER NOT NULL,
  max_keys INTEGER NOT NULL,
  rate_limit INTEGER NOT NULL DEFAULT 100
);

-- Insert default plans
INSERT OR IGNORE INTO plan (name, max_apps, max_keys, rate_limit) VALUES
  ('Free', 1, 50, 100),
  ('Premium', 5, 2000, 2000),
  ('Pro', 3, 1000, 1000),
  ('Military', -1, -1, -1);

-- Users table
CREATE TABLE IF NOT EXISTS user (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  email TEXT UNIQUE NOT NULL,
  username TEXT UNIQUE NOT NULL,
  password TEXT NOT NULL,
  plan_id INTEGER NOT NULL DEFAULT 2, -- Default to Premium plan
  is_admin INTEGER NOT NULL DEFAULT 0,
  is_banned INTEGER NOT NULL DEFAULT 0,
  ban_reason TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (plan_id) REFERENCES plan(id)
);

-- Apps table
CREATE TABLE IF NOT EXISTS app (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name TEXT NOT NULL,
  owner_id INTEGER NOT NULL,
  secret TEXT UNIQUE NOT NULL,
  hwid_lock INTEGER NOT NULL DEFAULT 0,
  expiry_days INTEGER NOT NULL DEFAULT 30,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (owner_id) REFERENCES user(id) ON DELETE CASCADE
);

-- License keys table
CREATE TABLE IF NOT EXISTS license_key (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  key TEXT UNIQUE NOT NULL,
  app_id INTEGER,
  user_id INTEGER,
  plan_id INTEGER NOT NULL DEFAULT 2, -- Default to Premium plan
  created_by INTEGER, -- Admin who created the key
  expiry_days INTEGER NOT NULL DEFAULT 30,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  used INTEGER NOT NULL DEFAULT 0,
  used_at DATETIME,
  hwid TEXT,
  expires_at DATETIME,
  FOREIGN KEY (app_id) REFERENCES app(id) ON DELETE CASCADE,
  FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE SET NULL,
  FOREIGN KEY (plan_id) REFERENCES plan(id),
  FOREIGN KEY (created_by) REFERENCES user(id) ON DELETE SET NULL
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_user_email ON user(email);
CREATE INDEX IF NOT EXISTS idx_user_username ON user(username);
CREATE INDEX IF NOT EXISTS idx_user_plan ON user(plan_id);
CREATE INDEX IF NOT EXISTS idx_user_banned ON user(is_banned);
CREATE INDEX IF NOT EXISTS idx_app_owner ON app(owner_id);
CREATE INDEX IF NOT EXISTS idx_app_secret ON app(secret);
CREATE INDEX IF NOT EXISTS idx_license_key_key ON license_key(key);
CREATE INDEX IF NOT EXISTS idx_license_key_app ON license_key(app_id);
CREATE INDEX IF NOT EXISTS idx_license_key_user ON license_key(user_id);
CREATE INDEX IF NOT EXISTS idx_license_key_plan ON license_key(plan_id);
CREATE INDEX IF NOT EXISTS idx_license_key_used ON license_key(used);
CREATE INDEX IF NOT EXISTS idx_license_key_expires ON license_key(expires_at);
