"""
Database utilities for PepeAuth
Simple database functions for API and other modules
"""

import sqlite3
import os
from flask import current_app, g

def get_db():
    """Get database connection"""
    if 'db' not in g:
        # Use the database path from config or default
        db_path = getattr(current_app.config, 'DATABASE', 'pepeauth.db')
        g.db = sqlite3.connect(db_path)
        g.db.row_factory = sqlite3.Row
    return g.db

def close_db(e=None):
    """Close database connection"""
    db = g.pop('db', None)
    if db is not None:
        db.close()

def query_db(query, args=(), one=False):
    """Query database and return results"""
    try:
        db = get_db()
        cur = db.execute(query, args)
        rv = cur.fetchall()
        return (rv[0] if rv else None) if one else rv
    except Exception as e:
        print(f"Database query error: {e}")
        return None if one else []

def execute_db(query, args=()):
    """Execute database query (INSERT, UPDATE, DELETE)"""
    try:
        db = get_db()
        db.execute(query, args)
        db.commit()
        return True
    except Exception as e:
        print(f"Database execute error: {e}")
        return False

# Standalone functions for use outside Flask context
def get_standalone_db():
    """Get database connection outside Flask context"""
    db_path = 'pepeauth.db'
    if not os.path.exists(db_path):
        raise FileNotFoundError(f"Database {db_path} not found")
    
    conn = sqlite3.connect(db_path)
    conn.row_factory = sqlite3.Row
    return conn

def query_db_standalone(query, args=(), one=False):
    """Query database outside Flask context"""
    try:
        conn = get_standalone_db()
        cur = conn.execute(query, args)
        rv = cur.fetchall()
        conn.close()
        return (rv[0] if rv else None) if one else rv
    except Exception as e:
        print(f"Standalone database query error: {e}")
        return None if one else []

def execute_db_standalone(query, args=()):
    """Execute database query outside Flask context"""
    try:
        conn = get_standalone_db()
        conn.execute(query, args)
        conn.commit()
        conn.close()
        return True
    except Exception as e:
        print(f"Standalone database execute error: {e}")
        return False
