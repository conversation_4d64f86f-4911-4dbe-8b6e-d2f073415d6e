"""
Test configuration and fixtures for PepeAuth
"""

import pytest
import tempfile
import os
import sqlite3
from flask import g
from app import create_app

def get_db():
    """Get database connection for tests"""
    if 'db' not in g:
        from flask import current_app
        g.db = sqlite3.connect(current_app.config['DATABASE'])
        g.db.row_factory = sqlite3.Row
    return g.db

def execute_db(query, args=()):
    """Execute a query and commit changes for tests"""
    db = get_db()
    cur = db.execute(query, args)
    db.commit()
    return cur.lastrowid

def query_db(query, args=(), one=False):
    """Execute a query and return results for tests"""
    db = get_db()
    cur = db.execute(query, args)
    rv = cur.fetchall()
    cur.close()
    return (rv[0] if rv else None) if one else rv

@pytest.fixture
def app():
    """Create and configure a new app instance for each test."""
    # Create a temporary file to isolate the database for each test
    db_fd, db_path = tempfile.mkstemp()
    
    app = create_app({
        'TESTING': True,
        'DATABASE': db_path,
        'SECRET_KEY': 'test-secret-key',
        'WTF_CSRF_ENABLED': False  # Disable CSRF for testing
    })
    
    with app.app_context():
        # Initialize the database
        with app.open_resource('schema.sql') as f:
            conn = sqlite3.connect(app.config['DATABASE'])
            conn.executescript(f.read().decode('utf8'))
            conn.commit()
            conn.close()
    
    yield app
    
    # Clean up
    os.close(db_fd)
    os.unlink(db_path)

@pytest.fixture
def client(app):
    """A test client for the app."""
    return app.test_client()

@pytest.fixture
def runner(app):
    """A test runner for the app's Click commands."""
    return app.test_cli_runner()

@pytest.fixture
def auth(client):
    """Authentication helper class."""
    class AuthActions:
        def __init__(self, client):
            self._client = client
        
        def register(self, email='<EMAIL>', username='testuser', 
                    password='testpass123', confirm_password=None):
            """Register a new user."""
            if confirm_password is None:
                confirm_password = password
            
            return self._client.post('/auth/register', data={
                'email': email,
                'username': username,
                'password': password,
                'confirm_password': confirm_password
            })
        
        def login(self, username='testuser', password='testpass123', remember_me=False):
            """Log in a user."""
            return self._client.post('/auth/login', data={
                'username': username,
                'password': password,
                'remember_me': remember_me
            })
        
        def logout(self):
            """Log out the current user."""
            return self._client.get('/auth/logout')
    
    return AuthActions(client)

@pytest.fixture
def admin_user(app):
    """Create an admin user for testing."""
    with app.app_context():
        from app import bcrypt
        password_hash = bcrypt.generate_password_hash('admin123').decode('utf-8')
        
        execute_db(
            'INSERT INTO user (email, username, password, plan_id, is_admin) VALUES (?, ?, ?, ?, ?)',
            ('<EMAIL>', 'admin', password_hash, 3, 1)  # Military plan, admin
        )
        
        return {
            'email': '<EMAIL>',
            'username': 'admin',
            'password': 'admin123',
            'plan_id': 3,
            'is_admin': True
        }

@pytest.fixture
def regular_user(app):
    """Create a regular user for testing."""
    with app.app_context():
        from app import bcrypt
        password_hash = bcrypt.generate_password_hash('user123').decode('utf-8')
        
        user_id = execute_db(
            'INSERT INTO user (email, username, password, plan_id, is_admin) VALUES (?, ?, ?, ?, ?)',
            ('<EMAIL>', 'user', password_hash, 1, 0)  # Free plan, regular user
        )
        
        return {
            'id': user_id,
            'email': '<EMAIL>',
            'username': 'user',
            'password': 'user123',
            'plan_id': 1,
            'is_admin': False
        }

@pytest.fixture
def test_app(app, regular_user):
    """Create a test app for testing."""
    with app.app_context():
        import secrets
        secret = secrets.token_urlsafe(32)
        
        app_id = execute_db(
            'INSERT INTO app (name, owner_id, secret, hwid_lock, expiry_days) VALUES (?, ?, ?, ?, ?)',
            ('TestApp', regular_user['id'], secret, 0, 30)
        )
        
        return {
            'id': app_id,
            'name': 'TestApp',
            'owner_id': regular_user['id'],
            'secret': secret,
            'hwid_lock': False,
            'expiry_days': 30
        }

@pytest.fixture
def test_license_key(app, test_app):
    """Create a test license key."""
    with app.app_context():
        import uuid
        key = str(uuid.uuid4())
        
        key_id = execute_db(
            'INSERT INTO license_key (key, app_id) VALUES (?, ?)',
            (key, test_app['id'])
        )
        
        return {
            'id': key_id,
            'key': key,
            'app_id': test_app['id'],
            'used': False
        }
