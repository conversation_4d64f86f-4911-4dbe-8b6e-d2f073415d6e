"""
Tests for authentication functionality
"""

import pytest
from flask import session
from app import query_db

class TestRegistration:
    """Test user registration functionality."""
    
    def test_register_page_loads(self, client):
        """Test that the registration page loads correctly."""
        response = client.get('/auth/register')
        assert response.status_code == 200
        assert b'Create your account' in response.data
    
    def test_successful_registration(self, client, app):
        """Test successful user registration."""
        response = client.post('/auth/register', data={
            'email': '<EMAIL>',
            'username': 'newuser',
            'password': 'password123',
            'confirm_password': 'password123'
        })
        
        # Should redirect to login page
        assert response.status_code == 302
        assert '/auth/login' in response.location
        
        # Check user was created in database
        with app.app_context():
            user = query_db(
                'SELECT * FROM user WHERE email = ?',
                ('<EMAIL>',),
                one=True
            )
            assert user is not None
            assert user['username'] == 'newuser'
            assert user['plan_id'] == 1  # Default to Free plan
            assert user['is_admin'] == 0
    
    def test_registration_duplicate_email(self, client, regular_user):
        """Test registration with duplicate email."""
        response = client.post('/auth/register', data={
            'email': regular_user['email'],
            'username': 'differentuser',
            'password': 'password123',
            'confirm_password': 'password123'
        })
        
        assert response.status_code == 200
        assert b'Email or username already exists' in response.data
    
    def test_registration_duplicate_username(self, client, regular_user):
        """Test registration with duplicate username."""
        response = client.post('/auth/register', data={
            'email': '<EMAIL>',
            'username': regular_user['username'],
            'password': 'password123',
            'confirm_password': 'password123'
        })
        
        assert response.status_code == 200
        assert b'Email or username already exists' in response.data
    
    def test_registration_password_mismatch(self, client):
        """Test registration with password mismatch."""
        response = client.post('/auth/register', data={
            'email': '<EMAIL>',
            'username': 'testuser',
            'password': 'password123',
            'confirm_password': 'different123'
        })
        
        assert response.status_code == 200
        assert b'Passwords must match' in response.data
    
    def test_registration_invalid_email(self, client):
        """Test registration with invalid email."""
        response = client.post('/auth/register', data={
            'email': 'invalid-email',
            'username': 'testuser',
            'password': 'password123',
            'confirm_password': 'password123'
        })
        
        assert response.status_code == 200
        assert b'Please enter a valid email address' in response.data

class TestLogin:
    """Test user login functionality."""
    
    def test_login_page_loads(self, client):
        """Test that the login page loads correctly."""
        response = client.get('/auth/login')
        assert response.status_code == 200
        assert b'Welcome back' in response.data
    
    def test_successful_login_with_username(self, client, regular_user):
        """Test successful login with username."""
        response = client.post('/auth/login', data={
            'username': regular_user['username'],
            'password': regular_user['password']
        })
        
        # Should redirect to dashboard
        assert response.status_code == 302
        assert '/dashboard' in response.location
    
    def test_successful_login_with_email(self, client, regular_user):
        """Test successful login with email."""
        response = client.post('/auth/login', data={
            'username': regular_user['email'],
            'password': regular_user['password']
        })
        
        # Should redirect to dashboard
        assert response.status_code == 302
        assert '/dashboard' in response.location
    
    def test_login_invalid_username(self, client):
        """Test login with invalid username."""
        response = client.post('/auth/login', data={
            'username': 'nonexistent',
            'password': 'password123'
        })
        
        assert response.status_code == 200
        assert b'Invalid username/email or password' in response.data
    
    def test_login_invalid_password(self, client, regular_user):
        """Test login with invalid password."""
        response = client.post('/auth/login', data={
            'username': regular_user['username'],
            'password': 'wrongpassword'
        })
        
        assert response.status_code == 200
        assert b'Invalid username/email or password' in response.data
    
    def test_login_remember_me(self, client, regular_user):
        """Test login with remember me option."""
        response = client.post('/auth/login', data={
            'username': regular_user['username'],
            'password': regular_user['password'],
            'remember_me': True
        })
        
        assert response.status_code == 302

class TestLogout:
    """Test user logout functionality."""
    
    def test_logout_redirects_to_index(self, client, auth, regular_user):
        """Test that logout redirects to index page."""
        # First login
        auth.login(regular_user['username'], regular_user['password'])
        
        # Then logout
        response = client.get('/auth/logout')
        assert response.status_code == 302
        assert response.location == '/'
    
    def test_logout_without_login(self, client):
        """Test logout without being logged in."""
        response = client.get('/auth/logout')
        # Should redirect to login page due to @login_required
        assert response.status_code == 302
        assert '/auth/login' in response.location

class TestUserModel:
    """Test User model functionality."""
    
    def test_user_can_create_app(self, app, regular_user):
        """Test that user can create app within plan limits."""
        with app.app_context():
            from app.auth import User
            
            # Get user data
            user_data = query_db(
                'SELECT * FROM user WHERE id = ?',
                (regular_user['id'],),
                one=True
            )
            user = User(user_data)
            
            # Free plan allows 1 app, user has 0 apps
            assert user.can_create_app() == True
    
    def test_user_cannot_create_app_over_limit(self, app, regular_user, test_app):
        """Test that user cannot create app over plan limit."""
        with app.app_context():
            from app.auth import User
            
            # Get user data
            user_data = query_db(
                'SELECT * FROM user WHERE id = ?',
                (regular_user['id'],),
                one=True
            )
            user = User(user_data)
            
            # Free plan allows 1 app, user already has 1 app (test_app)
            assert user.can_create_app() == False
    
    def test_user_can_create_key(self, app, regular_user, test_app):
        """Test that user can create license key within plan limits."""
        with app.app_context():
            from app.auth import User
            
            # Get user data
            user_data = query_db(
                'SELECT * FROM user WHERE id = ?',
                (regular_user['id'],),
                one=True
            )
            user = User(user_data)
            
            # Free plan allows 50 keys, user has 0 keys
            assert user.can_create_key(test_app['id']) == True

class TestProtectedRoutes:
    """Test that protected routes require authentication."""
    
    def test_dashboard_requires_login(self, client):
        """Test that dashboard requires login."""
        response = client.get('/dashboard/')
        assert response.status_code == 302
        assert '/auth/login' in response.location
    
    def test_admin_requires_login(self, client):
        """Test that admin panel requires login."""
        response = client.get('/admin/')
        assert response.status_code == 302
        assert '/auth/login' in response.location
    
    def test_admin_requires_admin_privileges(self, client, auth, regular_user):
        """Test that admin panel requires admin privileges."""
        # Login as regular user
        auth.login(regular_user['username'], regular_user['password'])
        
        # Try to access admin panel
        response = client.get('/admin/')
        assert response.status_code == 302
        assert '/dashboard' in response.location
