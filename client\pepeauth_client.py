"""
PepeAuth Python Client Library
A comprehensive client library for integrating with PepeAuth KeyAuth-like system
"""

import requests
import json
import hashlib
import platform
import uuid
import hmac
import time
from typing import Optional, Dict, Any
from dataclasses import dataclass


@dataclass
class UserInfo:
    """User information returned from authentication"""
    username: str
    email: str
    plan: str
    hwid: str
    app_name: str
    expires_in_days: int
    expires_at: Optional[str]
    created_at: str
    used_at: Optional[str]


class PepeAuthClient:
    """
    PepeAuth Client for license key authentication and management
    
    Example usage:
        client = PepeAuthClient("https://your-pepeauth-server.com", "your-app-secret")
        
        # Authenticate with license key
        if client.authenticate("LICENSE-KEY-HERE"):
            print(f"Welcome {client.user_info.username}!")
            print(f"Plan: {client.user_info.plan}")
            print(f"Days remaining: {client.user_info.expires_in_days}")
        else:
            print(f"Authentication failed: {client.last_error}")
    """
    
    def __init__(self, base_url: str, app_secret: str, timeout: int = 10):
        """
        Initialize PepeAuth client
        
        Args:
            base_url: Base URL of your PepeAuth server (e.g., "https://auth.yoursite.com")
            app_secret: Your application secret key
            timeout: Request timeout in seconds
        """
        self.base_url = base_url.rstrip('/')
        self.app_secret = app_secret
        self.timeout = timeout
        
        # Client state
        self.authenticated = False
        self.license_key: Optional[str] = None
        self.session_token: Optional[str] = None
        self.user_info: Optional[UserInfo] = None
        self.last_error: Optional[str] = None
        
        # Generate hardware ID
        self.hwid = self._generate_hwid()
    
    def _generate_hwid(self) -> str:
        """Generate a unique hardware ID for this machine"""
        try:
            # Combine multiple system identifiers
            machine_id = platform.machine()
            processor = platform.processor()
            system = platform.system()
            node = platform.node()
            
            # Get MAC address
            mac = ':'.join(['{:02x}'.format((uuid.getnode() >> elements) & 0xff) 
                           for elements in range(0, 2*6, 2)][::-1])
            
            # Create unique string
            unique_string = f"{machine_id}-{processor}-{system}-{node}-{mac}"
            
            # Hash it to create a consistent HWID
            return hashlib.sha256(unique_string.encode()).hexdigest()[:32]
        except Exception:
            # Fallback to a simpler method
            return hashlib.sha256(str(uuid.getnode()).encode()).hexdigest()[:32]
    
    def _make_request(self, endpoint: str, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Make a request to the PepeAuth API"""
        try:
            url = f"{self.base_url}/api/v1/{endpoint}"
            
            # Add common data
            data.update({
                'app_secret': self.app_secret,
                'hwid': self.hwid
            })
            
            headers = {
                'Content-Type': 'application/json',
                'User-Agent': 'PepeAuth-Python-Client/1.0'
            }
            
            response = requests.post(
                url,
                json=data,
                headers=headers,
                timeout=self.timeout
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                try:
                    error_data = response.json()
                    self.last_error = error_data.get('message', f'HTTP {response.status_code}')
                except:
                    self.last_error = f'HTTP {response.status_code}: {response.text}'
                return None
                
        except requests.RequestException as e:
            self.last_error = f'Network error: {str(e)}'
            return None
        except Exception as e:
            self.last_error = f'Unexpected error: {str(e)}'
            return None
    
    def authenticate(self, license_key: str) -> bool:
        """
        Authenticate with a license key
        
        Args:
            license_key: The license key to authenticate with
            
        Returns:
            True if authentication successful, False otherwise
        """
        data = {
            'key': license_key
        }
        
        response = self._make_request('auth', data)
        
        if response and response.get('status'):
            self.authenticated = True
            self.license_key = license_key
            self.session_token = response.get('session_token')
            
            # Parse user info
            user_data = response.get('user_info', {})
            self.user_info = UserInfo(
                username=user_data.get('username', 'User'),
                email=user_data.get('email', 'N/A'),
                plan=user_data.get('plan', 'Unknown'),
                hwid=user_data.get('hwid', self.hwid),
                app_name=user_data.get('app_name', 'Unknown'),
                expires_in_days=user_data.get('expires_in_days', 0),
                expires_at=user_data.get('expires_at'),
                created_at=user_data.get('created_at', ''),
                used_at=user_data.get('used_at')
            )
            
            return True
        else:
            self.authenticated = False
            self.license_key = None
            self.session_token = None
            self.user_info = None
            return False
    
    def get_user_info(self) -> bool:
        """
        Refresh user information
        
        Returns:
            True if successful, False otherwise
        """
        if not self.authenticated or not self.license_key or not self.session_token:
            self.last_error = "Not authenticated"
            return False
        
        data = {
            'key': self.license_key,
            'session_token': self.session_token
        }
        
        response = self._make_request('user_info', data)
        
        if response and response.get('status'):
            # Update user info
            user_data = response.get('user_info', {})
            self.user_info = UserInfo(
                username=user_data.get('username', 'User'),
                email=user_data.get('email', 'N/A'),
                plan=user_data.get('plan', 'Unknown'),
                hwid=user_data.get('hwid', self.hwid),
                app_name=user_data.get('app_name', 'Unknown'),
                expires_in_days=user_data.get('expires_in_days', 0),
                expires_at=user_data.get('expires_at'),
                created_at=user_data.get('created_at', ''),
                used_at=user_data.get('used_at')
            )
            return True
        else:
            return False
    
    def reset_hwid(self, new_hwid: Optional[str] = None) -> bool:
        """
        Reset hardware ID for the current license key
        
        Args:
            new_hwid: New hardware ID (optional, will use current machine's HWID if not provided)
            
        Returns:
            True if successful, False otherwise
        """
        if not self.authenticated or not self.license_key:
            self.last_error = "Not authenticated"
            return False
        
        data = {
            'key': self.license_key
        }
        
        if new_hwid:
            data['hwid'] = new_hwid
        
        response = self._make_request('reset_hwid', data)
        
        if response and response.get('status'):
            # Update local HWID if reset was successful
            if new_hwid:
                self.hwid = new_hwid
            return True
        else:
            return False
    
    def log_activity(self, action: str, details: str = "") -> bool:
        """
        Log custom activity
        
        Args:
            action: Action name/type
            details: Optional details about the action
            
        Returns:
            True if successful, False otherwise
        """
        if not self.authenticated or not self.license_key:
            self.last_error = "Not authenticated"
            return False
        
        data = {
            'key': self.license_key,
            'action': action,
            'details': details
        }
        
        response = self._make_request('log', data)
        return response and response.get('status', False)
    
    def trigger_webhook(self, event: str = "test", event_data: Optional[Dict[str, Any]] = None) -> bool:
        """
        Trigger a webhook for testing
        
        Args:
            event: Event type
            event_data: Optional event data
            
        Returns:
            True if successful, False otherwise
        """
        data = {
            'event': event,
            'data': event_data or {}
        }
        
        response = self._make_request('webhook', data)
        return response and response.get('status', False)
    
    def is_authenticated(self) -> bool:
        """Check if client is currently authenticated"""
        return self.authenticated and self.license_key is not None
    
    def is_expired(self) -> bool:
        """Check if the license is expired"""
        if not self.user_info:
            return True
        
        return self.user_info.expires_in_days == 0
    
    def days_remaining(self) -> int:
        """Get days remaining on license (-1 for lifetime)"""
        if not self.user_info:
            return 0
        
        return self.user_info.expires_in_days
    
    def logout(self):
        """Clear authentication state"""
        self.authenticated = False
        self.license_key = None
        self.session_token = None
        self.user_info = None
        self.last_error = None


# Example usage and testing
if __name__ == "__main__":
    import sys
    
    def main():
        print("PepeAuth Python Client - Example Usage")
        print("=" * 50)
        
        # Configuration
        BASE_URL = "http://localhost:5000"  # Change to your server URL
        APP_SECRET = "your-app-secret-here"  # Change to your app secret
        
        # Initialize client
        client = PepeAuthClient(BASE_URL, APP_SECRET)
        print(f"Hardware ID: {client.hwid}")
        print()
        
        # Get license key from user
        license_key = input("Enter your license key: ").strip()
        
        if not license_key:
            print("No license key provided!")
            return
        
        # Authenticate
        print("Authenticating...")
        if client.authenticate(license_key):
            print("✅ Authentication successful!")
            print(f"Welcome, {client.user_info.username}!")
            print(f"Plan: {client.user_info.plan}")
            print(f"App: {client.user_info.app_name}")
            
            if client.user_info.expires_in_days == -1:
                print("License: Lifetime")
            else:
                print(f"Days remaining: {client.user_info.expires_in_days}")
            
            print()
            
            # Test user info refresh
            print("Refreshing user info...")
            if client.get_user_info():
                print("✅ User info refreshed successfully!")
            else:
                print(f"❌ Failed to refresh user info: {client.last_error}")
            
            # Test activity logging
            print("Logging test activity...")
            if client.log_activity("app_start", "Application started successfully"):
                print("✅ Activity logged successfully!")
            else:
                print(f"❌ Failed to log activity: {client.last_error}")
            
            # Test webhook trigger
            print("Testing webhook...")
            if client.trigger_webhook("test", {"message": "Test from Python client"}):
                print("✅ Webhook triggered successfully!")
            else:
                print(f"❌ Failed to trigger webhook: {client.last_error}")
            
        else:
            print(f"❌ Authentication failed: {client.last_error}")
    
    if __name__ == "__main__":
        main()
