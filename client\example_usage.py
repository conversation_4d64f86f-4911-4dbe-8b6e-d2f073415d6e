#!/usr/bin/env python3
"""
PepeAuth Client Example Usage
Demonstrates how to integrate PepeAuth into your Python application
"""

import sys
import os
import time
from pepeauth_client import PepeAuthClient

# Configuration - Update these values for your setup
CONFIG = {
    'BASE_URL': 'http://localhost:5000',  # Your PepeAuth server URL
    'APP_SECRET': 'your-app-secret-here',  # Your application secret
    'LICENSE_KEY': None  # Will be prompted or set via command line
}

def print_banner():
    """Print application banner"""
    print("=" * 60)
    print("🐸 PepeAuth Protected Application Example")
    print("=" * 60)
    print()

def authenticate_user(client: PepeAuthClient, license_key: str = None) -> bool:
    """
    Authenticate user with license key
    
    Args:
        client: PepeAuth client instance
        license_key: License key (optional, will prompt if not provided)
        
    Returns:
        True if authentication successful
    """
    if not license_key:
        license_key = input("Please enter your license key: ").strip()
    
    if not license_key:
        print("❌ No license key provided!")
        return False
    
    print("🔐 Authenticating...")
    
    if client.authenticate(license_key):
        print("✅ Authentication successful!")
        print()
        
        # Display user information
        user = client.user_info
        print(f"👤 Welcome, {user.username}!")
        print(f"📧 Email: {user.email}")
        print(f"📦 Plan: {user.plan}")
        print(f"🖥️  App: {user.app_name}")
        print(f"🔧 Hardware ID: {user.hwid[:16]}...")
        
        if user.expires_in_days == -1:
            print("⏰ License: Lifetime")
        elif user.expires_in_days > 0:
            print(f"⏰ Days remaining: {user.expires_in_days}")
        else:
            print("⚠️  License: Expired")
            return False
        
        print()
        return True
    else:
        print(f"❌ Authentication failed: {client.last_error}")
        return False

def show_main_menu():
    """Display main application menu"""
    print("📋 Main Menu:")
    print("1. View User Information")
    print("2. Test Application Feature")
    print("3. Reset Hardware ID")
    print("4. Log Custom Activity")
    print("5. Test Webhook")
    print("6. Refresh License Info")
    print("0. Exit")
    print()

def handle_user_info(client: PepeAuthClient):
    """Display detailed user information"""
    print("👤 User Information:")
    print("-" * 30)
    
    if client.get_user_info():
        user = client.user_info
        print(f"Username: {user.username}")
        print(f"Email: {user.email}")
        print(f"Plan: {user.plan}")
        print(f"Application: {user.app_name}")
        print(f"Hardware ID: {user.hwid}")
        print(f"License Created: {user.created_at}")
        print(f"First Used: {user.used_at or 'Never'}")
        print(f"Expires At: {user.expires_at or 'Never'}")
        
        if user.expires_in_days == -1:
            print("Status: Lifetime License")
        elif user.expires_in_days > 0:
            print(f"Status: Active ({user.expires_in_days} days remaining)")
        else:
            print("Status: Expired")
    else:
        print(f"❌ Failed to get user info: {client.last_error}")
    
    print()

def handle_app_feature(client: PepeAuthClient):
    """Simulate a protected application feature"""
    print("🚀 Testing Protected Feature...")
    
    # Log the feature usage
    if client.log_activity("feature_test", "User accessed protected feature"):
        print("✅ Feature access logged")
    
    # Simulate some work
    print("Processing...")
    time.sleep(2)
    
    print("✅ Protected feature executed successfully!")
    print("This feature is only available to authenticated users.")
    print()

def handle_hwid_reset(client: PepeAuthClient):
    """Handle hardware ID reset"""
    print("🔧 Hardware ID Reset")
    print("-" * 30)
    print(f"Current HWID: {client.hwid}")
    print()
    
    confirm = input("Are you sure you want to reset your Hardware ID? (y/N): ").strip().lower()
    
    if confirm == 'y':
        print("Resetting Hardware ID...")
        
        if client.reset_hwid():
            print("✅ Hardware ID reset successfully!")
            print(f"New HWID: {client.hwid}")
        else:
            print(f"❌ Failed to reset HWID: {client.last_error}")
    else:
        print("Hardware ID reset cancelled.")
    
    print()

def handle_log_activity(client: PepeAuthClient):
    """Handle custom activity logging"""
    print("📝 Log Custom Activity")
    print("-" * 30)
    
    action = input("Enter action name: ").strip()
    if not action:
        print("No action specified.")
        return
    
    details = input("Enter details (optional): ").strip()
    
    print("Logging activity...")
    
    if client.log_activity(action, details):
        print("✅ Activity logged successfully!")
    else:
        print(f"❌ Failed to log activity: {client.last_error}")
    
    print()

def handle_webhook_test(client: PepeAuthClient):
    """Handle webhook testing"""
    print("🔗 Test Webhook")
    print("-" * 30)
    
    event = input("Enter event type (default: test): ").strip() or "test"
    message = input("Enter test message: ").strip() or "Test from example app"
    
    event_data = {
        "message": message,
        "timestamp": time.time(),
        "source": "example_app"
    }
    
    print("Triggering webhook...")
    
    if client.trigger_webhook(event, event_data):
        print("✅ Webhook triggered successfully!")
    else:
        print(f"❌ Failed to trigger webhook: {client.last_error}")
    
    print()

def main():
    """Main application entry point"""
    print_banner()
    
    # Check for license key in command line arguments
    license_key = None
    if len(sys.argv) > 1:
        license_key = sys.argv[1]
    
    # Initialize PepeAuth client
    client = PepeAuthClient(CONFIG['BASE_URL'], CONFIG['APP_SECRET'])
    
    # Authenticate user
    if not authenticate_user(client, license_key):
        print("Authentication required to continue.")
        sys.exit(1)
    
    # Log application start
    client.log_activity("app_start", "Example application started")
    
    # Main application loop
    while True:
        show_main_menu()
        
        try:
            choice = input("Select an option: ").strip()
            print()
            
            if choice == '0':
                print("👋 Goodbye!")
                client.log_activity("app_exit", "User exited application")
                break
            elif choice == '1':
                handle_user_info(client)
            elif choice == '2':
                handle_app_feature(client)
            elif choice == '3':
                handle_hwid_reset(client)
            elif choice == '4':
                handle_log_activity(client)
            elif choice == '5':
                handle_webhook_test(client)
            elif choice == '6':
                print("🔄 Refreshing license information...")
                if client.get_user_info():
                    print("✅ License information refreshed!")
                    user = client.user_info
                    if user.expires_in_days == -1:
                        print("Status: Lifetime License")
                    elif user.expires_in_days > 0:
                        print(f"Status: Active ({user.expires_in_days} days remaining)")
                    else:
                        print("⚠️  Status: Expired")
                        print("Your license has expired. Please renew to continue using this application.")
                        break
                else:
                    print(f"❌ Failed to refresh: {client.last_error}")
                print()
            else:
                print("❌ Invalid option. Please try again.")
                print()
                
        except KeyboardInterrupt:
            print("\n\n👋 Goodbye!")
            client.log_activity("app_exit", "User exited application (Ctrl+C)")
            break
        except Exception as e:
            print(f"❌ An error occurred: {str(e)}")
            print()

if __name__ == "__main__":
    main()
