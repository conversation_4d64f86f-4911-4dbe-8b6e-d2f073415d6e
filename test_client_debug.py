#!/usr/bin/env python3
"""
Debug the client connection
"""

import requests
import json

def test_client_connection():
    """Test direct connection to API"""
    print("🔍 Testing Client Connection")
    print("=" * 50)
    
    # Test with unused key
    data = {
        "key": "GLBHKHB46D0KWASI",
        "hwid": "CLIENT-TEST-HWID"
    }
    
    # Test the existing API endpoint first
    print("Testing existing API endpoint...")
    try:
        response = requests.post(
            "http://127.0.0.1:5000/api/v1/auth",
            json={"app_name": "test", "key": "test"},
            timeout=10
        )
        print(f"Existing API Status: {response.status_code}")
        print(f"Existing API Response: {response.text[:200]}")
    except Exception as e:
        print(f"Existing API Error: {e}")

    print("\nTesting KeyAuth API endpoint...")
    try:
        response = requests.post(
            "http://127.0.0.1:5000/api/v1/login_key",
            json=data,
            timeout=10
        )
        
        print(f"Status Code: {response.status_code}")
        print(f"Headers: {dict(response.headers)}")
        
        try:
            result = response.json()
            print(f"JSON Response: {json.dumps(result, indent=2)}")
        except:
            print(f"Raw Response: {response.text}")
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    test_client_connection()
