{% extends "base.html" %}

{% block title %}Generated Keys - {{ app.name }} - PepeAuth{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center space-x-4 mb-4">
            <a href="{{ url_for('dashboard.app_detail', app_id=app.id) }}" class="text-gray-400 hover:text-white">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
            </a>
            <h1 class="text-3xl font-bold text-white">Keys Generated Successfully!</h1>
        </div>
        <p class="text-gray-300">{{ keys|length }} license key{{ 's' if keys|length != 1 else '' }} generated for <strong>{{ app.name }}</strong></p>
    </div>
    
    <!-- Success Message -->
    <div class="glass-card p-6 rounded-xl mb-8 border-l-4 border-pepe-green">
        <div class="flex items-center">
            <svg class="w-6 h-6 text-pepe-green mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <div>
                <h3 class="text-lg font-semibold text-white">License Keys Generated</h3>
                <p class="text-gray-300">Your license keys are ready to use. Make sure to save them securely.</p>
            </div>
        </div>
    </div>
    
    <!-- Note -->
    {% if note %}
        <div class="glass-card p-4 rounded-xl mb-6">
            <h4 class="text-sm font-medium text-white mb-2">Note:</h4>
            <p class="text-gray-300 text-sm">{{ note }}</p>
        </div>
    {% endif %}
    
    <!-- Actions -->
    <div class="flex flex-col sm:flex-row gap-4 mb-8">
        <button onclick="copyAllKeys()" class="btn-primary">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
            </svg>
            Copy All Keys
        </button>
        <button onclick="downloadKeys()" class="btn-secondary">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            Download as TXT
        </button>
        <a href="{{ url_for('dashboard.generate_keys', app_id=app.id) }}" class="btn-secondary text-center">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            Generate More
        </a>
    </div>
    
    <!-- Keys List -->
    <div class="glass-card p-6 rounded-xl">
        <h3 class="text-lg font-semibold text-white mb-4">Generated License Keys</h3>
        
        <div class="space-y-3">
            {% for key in keys %}
                <div class="flex items-center justify-between p-4 bg-pepe-darker/50 rounded-lg border border-pepe-light-gray/20">
                    <div class="flex-1">
                        <code class="text-sm text-gray-300 font-mono break-all">{{ key }}</code>
                    </div>
                    <button onclick="copyKey('{{ key }}', this)" 
                            class="ml-4 text-gray-400 hover:text-white transition-colors">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                        </svg>
                    </button>
                </div>
            {% endfor %}
        </div>
    </div>
    
    <!-- Warning -->
    <div class="glass-card p-6 rounded-xl mt-8 border-l-4 border-yellow-500">
        <div class="flex items-start">
            <svg class="w-6 h-6 text-yellow-400 mr-3 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
            </svg>
            <div>
                <h4 class="text-lg font-semibold text-yellow-400 mb-2">Important Security Notice</h4>
                <ul class="text-yellow-300 text-sm space-y-1">
                    <li>• Save these keys securely - they won't be shown again in full</li>
                    <li>• Each key can only be activated once (unless HWID lock is disabled)</li>
                    <li>• Keys expire {{ app.expiry_days }} days after activation</li>
                    <li>• Monitor key usage in your app dashboard</li>
                </ul>
            </div>
        </div>
    </div>
    
    <!-- Navigation -->
    <div class="flex justify-center mt-8">
        <a href="{{ url_for('dashboard.app_detail', app_id=app.id) }}" class="btn-primary">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
            </svg>
            Back to App Dashboard
        </a>
    </div>
</div>

<script>
const keys = {{ keys | tojson }};

function copyKey(key, button) {
    navigator.clipboard.writeText(key).then(() => {
        const originalHTML = button.innerHTML;
        button.innerHTML = '<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>';
        setTimeout(() => {
            button.innerHTML = originalHTML;
        }, 1000);
    });
}

function copyAllKeys() {
    const allKeys = keys.join('\n');
    navigator.clipboard.writeText(allKeys).then(() => {
        // Show success message
        const button = event.target;
        const originalText = button.textContent;
        button.textContent = 'Copied!';
        setTimeout(() => {
            button.innerHTML = '<svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path></svg>Copy All Keys';
        }, 1000);
    });
}

function downloadKeys() {
    const content = keys.join('\n');
    const blob = new Blob([content], { type: 'text/plain' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `{{ app.name }}_license_keys_${new Date().toISOString().split('T')[0]}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
}
</script>
{% endblock %}
