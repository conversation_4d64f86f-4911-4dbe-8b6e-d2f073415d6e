{% extends "base.html" %}

{% block title %}User Dashboard - PepeAuth{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div>
                <h1 class="text-3xl font-bold text-white">Dashboard</h1>
                <p class="mt-1 text-gray-300">Welcome back, {{ current_user.username }}!</p>
                <div class="mt-2 flex items-center space-x-4">
                    <span class="badge badge-success">{{ current_user.plan.name }} Plan</span>
                    {% if current_user.last_login %}
                        <span class="text-sm text-gray-400">Last login: {{ current_user.last_login.strftime('%Y-%m-%d %H:%M') }}</span>
                    {% endif %}
                </div>
            </div>
            <div class="mt-4 sm:mt-0 flex space-x-3">
                <a href="{{ url_for('dashboard.create_app') }}" class="btn-primary">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Create App
                </a>
                <a href="{{ url_for('dashboard.generate_keys') }}" class="btn-secondary">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m0 0a2 2 0 012 2m-2-2a2 2 0 00-2 2m2-2V5a2 2 0 00-2-2m0 0H9a2 2 0 00-2 2v0a2 2 0 002 2h2m-4 4v6a2 2 0 002 2h2a2 2 0 002-2v-6m-4 0a2 2 0 012-2h0a2 2 0 012 2v0"></path>
                    </svg>
                    Generate Keys
                </a>
            </div>
        </div>
    </div>
    
    <!-- Quick Stats -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="glass-card p-6 rounded-xl card-hover">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-10 h-10 bg-blue-500/20 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-300">Total Apps</p>
                    <p class="text-2xl font-semibold text-white">{{ stats.total_apps }}</p>
                    <p class="text-xs text-gray-400">
                        {% if current_user.plan.max_apps == -1 %}Unlimited{% else %}{{ current_user.plan.max_apps }} max{% endif %}
                    </p>
                </div>
            </div>
        </div>
        
        <div class="glass-card p-6 rounded-xl card-hover">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-10 h-10 bg-pepe-green/20 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-pepe-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m0 0a2 2 0 012 2m-2-2a2 2 0 00-2 2m2-2V5a2 2 0 00-2-2m0 0H9a2 2 0 00-2 2v0a2 2 0 002 2h2m-4 4v6a2 2 0 002 2h2a2 2 0 002-2v-6m-4 0a2 2 0 012-2h0a2 2 0 012 2v0"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-300">License Keys</p>
                    <p class="text-2xl font-semibold text-white">{{ stats.total_keys }}</p>
                    <p class="text-xs text-gray-400">
                        {% if current_user.plan.max_keys == -1 %}Unlimited{% else %}{{ current_user.plan.max_keys }} max{% endif %}
                    </p>
                </div>
            </div>
        </div>
        
        <div class="glass-card p-6 rounded-xl card-hover">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-10 h-10 bg-green-500/20 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-300">Active Keys</p>
                    <p class="text-2xl font-semibold text-white">{{ stats.active_keys }}</p>
                    <p class="text-xs text-gray-400">{{ stats.used_keys }} used</p>
                </div>
            </div>
        </div>
        
        <div class="glass-card p-6 rounded-xl card-hover">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-10 h-10 bg-purple-500/20 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-300">HWID Resets</p>
                    <p class="text-2xl font-semibold text-white">{{ current_user.hwid_reset_count }}</p>
                    <p class="text-xs text-gray-400">{{ current_user.max_hwid_resets }} max</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Plan Usage Progress -->
    <div class="glass-card p-6 rounded-xl mb-8">
        <h3 class="text-lg font-semibold text-white mb-6">Plan Usage</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
            <!-- Apps Usage -->
            <div>
                <div class="flex justify-between items-center mb-3">
                    <span class="text-sm font-medium text-gray-300">Applications</span>
                    <span class="text-sm text-gray-300">
                        {{ stats.total_apps }}{% if current_user.plan.max_apps != -1 %} / {{ current_user.plan.max_apps }}{% else %} / ∞{% endif %}
                    </span>
                </div>
                <div class="progress-bar">
                    {% if current_user.plan.max_apps == -1 %}
                        <div class="progress-fill" style="width: 15%"></div>
                    {% else %}
                        {% set app_percentage = (stats.total_apps / current_user.plan.max_apps * 100) if current_user.plan.max_apps > 0 else 0 %}
                        <div class="progress-fill" style="width: {{ [app_percentage, 100] | min }}%"></div>
                    {% endif %}
                </div>
            </div>
            
            <!-- Keys Usage -->
            <div>
                <div class="flex justify-between items-center mb-3">
                    <span class="text-sm font-medium text-gray-300">License Keys</span>
                    <span class="text-sm text-gray-300">
                        {{ stats.total_keys }}{% if current_user.plan.max_keys != -1 %} / {{ current_user.plan.max_keys }}{% else %} / ∞{% endif %}
                    </span>
                </div>
                <div class="progress-bar">
                    {% if current_user.plan.max_keys == -1 %}
                        <div class="progress-fill" style="width: 15%"></div>
                    {% else %}
                        {% set key_percentage = (stats.total_keys / current_user.plan.max_keys * 100) if current_user.plan.max_keys > 0 else 0 %}
                        <div class="progress-fill" style="width: {{ [key_percentage, 100] | min }}%"></div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    
    <!-- Quick Actions -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <!-- Recent Activity -->
        <div class="glass-card p-6 rounded-xl">
            <h3 class="text-lg font-semibold text-white mb-4">Recent Activity</h3>
            {% if recent_activity %}
                <div class="space-y-4">
                    {% for activity in recent_activity %}
                        <div class="flex items-center space-x-3 p-3 rounded-lg bg-pepe-gray/30">
                            <div class="flex-shrink-0">
                                {% if activity.type == 'key_created' %}
                                    <div class="w-8 h-8 bg-green-500/20 rounded-full flex items-center justify-center">
                                        <svg class="w-4 h-4 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                        </svg>
                                    </div>
                                {% elif activity.type == 'key_used' %}
                                    <div class="w-8 h-8 bg-blue-500/20 rounded-full flex items-center justify-center">
                                        <svg class="w-4 h-4 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                    </div>
                                {% elif activity.type == 'app_created' %}
                                    <div class="w-8 h-8 bg-purple-500/20 rounded-full flex items-center justify-center">
                                        <svg class="w-4 h-4 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                                        </svg>
                                    </div>
                                {% endif %}
                            </div>
                            <div class="flex-1 min-w-0">
                                <p class="text-sm text-white">{{ activity.message }}</p>
                                <p class="text-xs text-gray-400">{{ activity.created_at.strftime('%Y-%m-%d %H:%M') }}</p>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="text-center py-8">
                    <div class="w-12 h-12 bg-pepe-gray rounded-full flex items-center justify-center mx-auto mb-3">
                        <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <p class="text-gray-400 text-sm">No recent activity</p>
                </div>
            {% endif %}
        </div>
        
        <!-- Quick Actions -->
        <div class="glass-card p-6 rounded-xl">
            <h3 class="text-lg font-semibold text-white mb-4">Quick Actions</h3>
            <div class="space-y-3">
                <a href="{{ url_for('dashboard.create_app') }}" 
                   class="flex items-center p-3 rounded-lg bg-pepe-gray/30 hover:bg-pepe-green/20 transition-colors group">
                    <div class="w-8 h-8 bg-blue-500/20 rounded-lg flex items-center justify-center mr-3 group-hover:bg-blue-500/30">
                        <svg class="w-4 h-4 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-white">Create New App</p>
                        <p class="text-xs text-gray-400">Set up a new application</p>
                    </div>
                </a>
                
                <a href="{{ url_for('dashboard.generate_keys') }}" 
                   class="flex items-center p-3 rounded-lg bg-pepe-gray/30 hover:bg-pepe-green/20 transition-colors group">
                    <div class="w-8 h-8 bg-pepe-green/20 rounded-lg flex items-center justify-center mr-3 group-hover:bg-pepe-green/30">
                        <svg class="w-4 h-4 text-pepe-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m0 0a2 2 0 012 2m-2-2a2 2 0 00-2 2m2-2V5a2 2 0 00-2-2m0 0H9a2 2 0 00-2 2v0a2 2 0 002 2h2m-4 4v6a2 2 0 002 2h2a2 2 0 002-2v-6m-4 0a2 2 0 012-2h0a2 2 0 012 2v0"></path>
                        </svg>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-white">Generate License Keys</p>
                        <p class="text-xs text-gray-400">Create new license keys</p>
                    </div>
                </a>
                
                <a href="{{ url_for('dashboard.keys') }}" 
                   class="flex items-center p-3 rounded-lg bg-pepe-gray/30 hover:bg-pepe-green/20 transition-colors group">
                    <div class="w-8 h-8 bg-purple-500/20 rounded-lg flex items-center justify-center mr-3 group-hover:bg-purple-500/30">
                        <svg class="w-4 h-4 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                        </svg>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-white">Manage Keys</p>
                        <p class="text-xs text-gray-400">View and manage all keys</p>
                    </div>
                </a>
                
                <a href="{{ url_for('dashboard.webhooks') }}" 
                   class="flex items-center p-3 rounded-lg bg-pepe-gray/30 hover:bg-pepe-green/20 transition-colors group">
                    <div class="w-8 h-8 bg-yellow-500/20 rounded-lg flex items-center justify-center mr-3 group-hover:bg-yellow-500/30">
                        <svg class="w-4 h-4 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
                        </svg>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-white">Configure Webhooks</p>
                        <p class="text-xs text-gray-400">Set up webhook notifications</p>
                    </div>
                </a>
            </div>
        </div>
    </div>
    
    <!-- Apps Overview -->
    <div class="glass-card p-6 rounded-xl">
        <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold text-white">Your Applications</h3>
            <div class="flex items-center space-x-3">
                {% if apps %}
                    <span class="text-sm text-gray-300">{{ apps|length }} app{{ 's' if apps|length != 1 else '' }}</span>
                {% endif %}
                <a href="{{ url_for('dashboard.apps') }}" class="text-sm text-pepe-green hover:text-pepe-green/80">View All</a>
            </div>
        </div>
        
        {% if apps %}
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {% for app in apps[:6] %}  <!-- Show only first 6 apps -->
                    <div class="glass-card p-4 rounded-lg card-hover">
                        <div class="flex items-center justify-between mb-3">
                            <h4 class="text-base font-semibold text-white truncate">{{ app.name }}</h4>
                            <div class="flex items-center space-x-1">
                                {% if app.hwid_lock %}
                                    <div class="w-2 h-2 bg-yellow-500 rounded-full tooltip" data-tooltip="HWID Lock Enabled"></div>
                                {% endif %}
                                {% if app.is_active %}
                                    <div class="w-2 h-2 bg-pepe-green rounded-full tooltip" data-tooltip="Active"></div>
                                {% else %}
                                    <div class="w-2 h-2 bg-red-500 rounded-full tooltip" data-tooltip="Inactive"></div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="space-y-1 mb-4 text-sm">
                            <div class="flex justify-between">
                                <span class="text-gray-400">Keys:</span>
                                <span class="text-white">{{ app.key_count }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-400">Used:</span>
                                <span class="text-white">{{ app.used_keys }}</span>
                            </div>
                        </div>
                        
                        <div class="flex space-x-2">
                            <a href="{{ url_for('dashboard.app_detail', app_id=app.id) }}" 
                               class="btn-primary text-xs flex-1 text-center py-2">
                                Manage
                            </a>
                            <a href="{{ url_for('dashboard.generate_keys', app_id=app.id) }}" 
                               class="btn-secondary text-xs px-3 py-2">
                                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                            </a>
                        </div>
                    </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="text-center py-12">
                <div class="w-16 h-16 bg-pepe-gray rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-medium text-white mb-2">No applications yet</h3>
                <p class="text-gray-300 mb-6">Create your first application to start managing license keys.</p>
                <a href="{{ url_for('dashboard.create_app') }}" class="btn-primary">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Create Your First App
                </a>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Auto-refresh stats every 30 seconds
setInterval(function() {
    // You can implement AJAX refresh here if needed
}, 30000);

// Add some interactive features
document.addEventListener('DOMContentLoaded', function() {
    // Animate progress bars on load
    const progressBars = document.querySelectorAll('.progress-fill');
    progressBars.forEach(bar => {
        const width = bar.style.width;
        bar.style.width = '0%';
        setTimeout(() => {
            bar.style.width = width;
        }, 500);
    });
});
</script>
{% endblock %}
