"""
API Utilities for KeyAuth-like system
Common functions for validation, security, and data processing
"""

import hmac
import hashlib
import re
from datetime import datetime
from flask import request, jsonify

def validate_email(email: str) -> bool:
    """Validate email format"""
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

def validate_username(username: str) -> bool:
    """Validate username format (alphanumeric, 3-20 chars)"""
    pattern = r'^[a-zA-Z0-9_]{3,20}$'
    return re.match(pattern, username) is not None

def validate_license_key(key: str) -> bool:
    """Validate license key format"""
    # Allow alphanumeric keys, 8-32 characters
    pattern = r'^[A-Z0-9]{8,32}$'
    return re.match(pattern, key) is not None

def sanitize_input(data: str, max_length: int = 255) -> str:
    """Sanitize user input"""
    if not isinstance(data, str):
        return str(data)[:max_length]
    return data.strip()[:max_length]

def generate_api_response(status: bool, message: str, data: dict = None, status_code: int = 200) -> tuple:
    """Generate standardized API response"""
    response = {
        'status': status,
        'message': message,
        'timestamp': datetime.now().isoformat()
    }
    
    if data:
        response.update(data)
    
    return jsonify(response), status_code

def verify_request_signature(secret: str, payload: str, signature: str) -> bool:
    """Verify HMAC signature for API requests"""
    if not signature:
        return False
    
    expected = hmac.new(
        secret.encode(),
        payload.encode(),
        hashlib.sha256
    ).hexdigest()
    
    return hmac.compare_digest(expected, signature)

def get_client_ip() -> str:
    """Get client IP address from request"""
    # Check for forwarded IP first (in case of proxy)
    forwarded_ip = request.headers.get('X-Forwarded-For')
    if forwarded_ip:
        return forwarded_ip.split(',')[0].strip()
    
    # Check for real IP header
    real_ip = request.headers.get('X-Real-IP')
    if real_ip:
        return real_ip
    
    # Fall back to remote address
    return request.remote_addr or 'unknown'

def get_user_agent() -> str:
    """Get user agent from request"""
    return request.headers.get('User-Agent', 'unknown')

def log_api_request(endpoint: str, success: bool, message: str = None):
    """Log API request for monitoring"""
    import logging
    
    client_ip = get_client_ip()
    user_agent = get_user_agent()
    
    log_message = f"API {endpoint} - IP: {client_ip} - Success: {success}"
    if message:
        log_message += f" - {message}"
    
    if success:
        logging.info(log_message)
    else:
        logging.warning(log_message)

def check_rate_limit_exceeded(key: str, limit: int, window: int = 3600) -> bool:
    """
    Simple rate limiting check
    Returns True if rate limit is exceeded
    """
    # This is a simple implementation
    # In production, you'd use Redis or similar
    from flask import g
    
    if not hasattr(g, 'rate_limits'):
        g.rate_limits = {}
    
    now = datetime.now().timestamp()
    
    if key not in g.rate_limits:
        g.rate_limits[key] = []
    
    # Clean old entries
    g.rate_limits[key] = [
        timestamp for timestamp in g.rate_limits[key]
        if now - timestamp < window
    ]
    
    # Check if limit exceeded
    if len(g.rate_limits[key]) >= limit:
        return True
    
    # Add current request
    g.rate_limits[key].append(now)
    return False

def format_datetime(dt_string: str) -> str:
    """Format datetime string for display"""
    try:
        dt = datetime.fromisoformat(dt_string)
        return dt.strftime('%Y-%m-%d %H:%M:%S')
    except:
        return dt_string

def calculate_days_remaining(expires_at: str) -> int:
    """Calculate days remaining until expiration"""
    try:
        expires = datetime.fromisoformat(expires_at)
        now = datetime.now()
        delta = expires - now
        return max(0, delta.days)
    except:
        return 0

def mask_sensitive_data(data: str, visible_chars: int = 4) -> str:
    """Mask sensitive data like license keys"""
    if not data or len(data) <= visible_chars:
        return data
    
    return data[:visible_chars] + '*' * (len(data) - visible_chars)

class APIError(Exception):
    """Custom API exception"""
    def __init__(self, message: str, status_code: int = 400):
        self.message = message
        self.status_code = status_code
        super().__init__(self.message)

def handle_api_error(error: APIError):
    """Handle custom API errors"""
    return generate_api_response(
        status=False,
        message=error.message,
        status_code=error.status_code
    )
