2025-07-25 02:49:06,597 INFO werkzeug [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-07-25 02:49:06,598 INFO werkzeug [33mPress CTRL+C to quit[0m
2025-07-25 02:49:11,490 INFO werkzeug 127.0.0.1 - - [25/Jul/2025 02:49:11] "GET / HTTP/1.1" 200 -
2025-07-25 02:49:12,056 INFO werkzeug 127.0.0.1 - - [25/Jul/2025 02:49:12] "GET /static/css/pepe.css HTTP/1.1" 200 -
2025-07-25 02:49:13,117 INFO werkzeug 127.0.0.1 - - [25/Jul/2025 02:49:13] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-07-25 02:49:22,343 INFO werkzeug 127.0.0.1 - - [25/Jul/2025 02:49:22] "GET /auth/login HTTP/1.1" 200 -
2025-07-25 02:49:22,575 INFO werkzeug 127.0.0.1 - - [25/Jul/2025 02:49:22] "[36mGET /static/css/pepe.css HTTP/1.1[0m" 304 -
2025-07-25 02:49:26,370 INFO werkzeug 127.0.0.1 - - [25/Jul/2025 02:49:26] "GET /auth/register HTTP/1.1" 200 -
2025-07-25 02:49:26,581 INFO werkzeug 127.0.0.1 - - [25/Jul/2025 02:49:26] "[36mGET /static/css/pepe.css HTTP/1.1[0m" 304 -
2025-07-25 02:49:48,570 ERROR app Exception on /auth/register [POST]
Traceback (most recent call last):
  File "C:\Program Files\Python313\Lib\site-packages\flask\app.py", line 1455, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Program Files\Python313\Lib\site-packages\flask\app.py", line 869, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Program Files\Python313\Lib\site-packages\flask\app.py", line 867, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Program Files\Python313\Lib\site-packages\flask\app.py", line 852, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\sell project\pepeauth\app\auth.py", line 70, in register
    existing_user = query_db(
        'SELECT id FROM user WHERE email = ? OR username = ?',
        (email, username),
        one=True
    )
  File "C:\Users\<USER>\Desktop\sell project\pepeauth\app\__init__.py", line 122, in query_db
    db = get_db()
         ^^^^^^
NameError: name 'get_db' is not defined
2025-07-25 02:49:48,575 INFO werkzeug 127.0.0.1 - - [25/Jul/2025 02:49:48] "[35m[1mPOST /auth/register HTTP/1.1[0m" 500 -
2025-07-25 02:49:59,870 INFO werkzeug [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-07-25 02:49:59,870 INFO werkzeug [33mPress CTRL+C to quit[0m
2025-07-25 02:50:32,202 INFO werkzeug [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-07-25 02:50:32,204 INFO werkzeug [33mPress CTRL+C to quit[0m
2025-07-25 02:55:20,545 INFO werkzeug [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-07-25 02:55:20,546 INFO werkzeug [33mPress CTRL+C to quit[0m
2025-07-25 02:55:38,334 INFO werkzeug 127.0.0.1 - - [25/Jul/2025 02:55:38] "GET / HTTP/1.1" 200 -
2025-07-25 02:55:38,594 INFO werkzeug 127.0.0.1 - - [25/Jul/2025 02:55:38] "GET /static/css/pepe.css HTTP/1.1" 200 -
2025-07-25 02:55:39,837 INFO werkzeug 127.0.0.1 - - [25/Jul/2025 02:55:39] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-07-25 02:55:43,779 INFO werkzeug 127.0.0.1 - - [25/Jul/2025 02:55:43] "GET /auth/register HTTP/1.1" 200 -
2025-07-25 02:55:43,807 INFO werkzeug 127.0.0.1 - - [25/Jul/2025 02:55:43] "[36mGET /static/css/pepe.css HTTP/1.1[0m" 304 -
2025-07-25 02:55:51,485 ERROR app Exception on /auth/register [POST]
Traceback (most recent call last):
  File "C:\Program Files\Python313\Lib\site-packages\flask\app.py", line 1455, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Program Files\Python313\Lib\site-packages\flask\app.py", line 869, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Program Files\Python313\Lib\site-packages\flask\app.py", line 867, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Program Files\Python313\Lib\site-packages\flask\app.py", line 852, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\sell project\pepeauth\app\auth.py", line 70, in register
    existing_user = query_db(
        'SELECT id FROM user WHERE email = ? OR username = ?',
        (email, username),
        one=True
    )
  File "C:\Users\<USER>\Desktop\sell project\pepeauth\app\__init__.py", line 122, in query_db
    db = get_db()
         ^^^^^^
NameError: name 'get_db' is not defined
2025-07-25 02:55:51,501 INFO werkzeug 127.0.0.1 - - [25/Jul/2025 02:55:51] "[35m[1mPOST /auth/register HTTP/1.1[0m" 500 -
2025-07-25 02:56:35,591 INFO flask_wtf.csrf The CSRF token is missing.
2025-07-25 02:56:35,592 INFO werkzeug 127.0.0.1 - - [25/Jul/2025 02:56:35] "[31m[1mPOST /api/v1/auth HTTP/1.1[0m" 400 -
2025-07-25 02:57:35,037 INFO flask_wtf.csrf The CSRF token is missing.
2025-07-25 02:57:35,060 INFO flask_wtf.csrf The CSRF token is missing.
2025-07-25 02:58:33,050 ERROR app Exception on /auth/register [POST]
Traceback (most recent call last):
  File "C:\Program Files\Python313\Lib\site-packages\flask\app.py", line 1455, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Program Files\Python313\Lib\site-packages\flask\app.py", line 869, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Program Files\Python313\Lib\site-packages\flask\app.py", line 867, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Program Files\Python313\Lib\site-packages\flask\app.py", line 852, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\sell project\pepeauth\app\auth.py", line 70, in register
    existing_user = query_db(
        'SELECT id FROM user WHERE email = ? OR username = ?',
        (email, username),
        one=True
    )
  File "C:\Users\<USER>\Desktop\sell project\pepeauth\app\__init__.py", line 122, in query_db
    db = get_db()
         ^^^^^^
NameError: name 'get_db' is not defined
2025-07-25 02:58:33,052 INFO werkzeug 127.0.0.1 - - [25/Jul/2025 02:58:33] "[35m[1mPOST /auth/register HTTP/1.1[0m" 500 -
2025-07-25 03:00:15,897 INFO werkzeug [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-07-25 03:00:15,897 INFO werkzeug [33mPress CTRL+C to quit[0m
2025-07-25 03:01:57,999 INFO root New user registered: newtest (<EMAIL>)
2025-07-25 03:01:58,266 INFO root User logged in: demo
2025-07-25 03:02:21,287 INFO flask_wtf.csrf The CSRF token is missing.
2025-07-25 03:03:45,324 INFO flask_wtf.csrf The CSRF token is missing.
2025-07-25 03:03:45,325 INFO flask_wtf.csrf The CSRF token is missing.
2025-07-25 03:03:45,326 INFO flask_wtf.csrf The CSRF token is missing.
2025-07-25 03:03:45,328 INFO flask_wtf.csrf The CSRF token is missing.
2025-07-25 03:03:45,329 INFO flask_wtf.csrf The CSRF token is missing.
2025-07-25 03:08:44,927 INFO werkzeug [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-07-25 03:08:44,927 INFO werkzeug [33mPress CTRL+C to quit[0m
2025-07-25 03:10:59,443 INFO root User logged in: demo
2025-07-25 03:10:59,533 WARNING root API auth attempt for non-existent app: TestApp
2025-07-31 17:13:18,087 INFO werkzeug [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-07-31 17:13:18,088 INFO werkzeug [33mPress CTRL+C to quit[0m
2025-07-31 17:13:24,596 INFO werkzeug 127.0.0.1 - - [31/Jul/2025 17:13:24] "GET / HTTP/1.1" 200 -
2025-07-31 17:13:25,021 INFO werkzeug 127.0.0.1 - - [31/Jul/2025 17:13:25] "GET /static/css/pepe.css HTTP/1.1" 200 -
2025-07-31 17:13:44,983 INFO werkzeug [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-07-31 17:13:44,983 INFO werkzeug [33mPress CTRL+C to quit[0m
2025-07-31 17:13:45,149 INFO werkzeug 127.0.0.1 - - [31/Jul/2025 17:13:45] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-07-31 17:13:54,583 INFO werkzeug 127.0.0.1 - - [31/Jul/2025 17:13:54] "GET /dashboard/ HTTP/1.1" 200 -
2025-07-31 17:13:54,643 INFO werkzeug 127.0.0.1 - - [31/Jul/2025 17:13:54] "[36mGET /static/css/pepe.css HTTP/1.1[0m" 304 -
2025-07-31 17:13:57,701 INFO werkzeug 127.0.0.1 - - [31/Jul/2025 17:13:57] "GET /dashboard/create-app HTTP/1.1" 200 -
2025-07-31 17:13:57,723 INFO werkzeug 127.0.0.1 - - [31/Jul/2025 17:13:57] "[36mGET /static/css/pepe.css HTTP/1.1[0m" 304 -
2025-07-31 17:15:22,051 INFO werkzeug 127.0.0.1 - - [31/Jul/2025 17:15:22] "GET /dashboard/ HTTP/1.1" 200 -
2025-07-31 17:15:22,103 INFO werkzeug 127.0.0.1 - - [31/Jul/2025 17:15:22] "[36mGET /static/css/pepe.css HTTP/1.1[0m" 304 -
2025-07-31 17:15:24,536 INFO werkzeug 127.0.0.1 - - [31/Jul/2025 17:15:24] "GET /dashboard/create-app HTTP/1.1" 200 -
2025-07-31 17:15:24,590 INFO werkzeug 127.0.0.1 - - [31/Jul/2025 17:15:24] "[36mGET /static/css/pepe.css HTTP/1.1[0m" 304 -
2025-07-31 17:15:29,427 INFO werkzeug 127.0.0.1 - - [31/Jul/2025 17:15:29] "[32mPOST /dashboard/create-app HTTP/1.1[0m" 302 -
2025-07-31 17:15:32,078 INFO werkzeug 127.0.0.1 - - [31/Jul/2025 17:15:32] "GET /dashboard/app/3 HTTP/1.1" 200 -
2025-07-31 17:15:32,721 INFO werkzeug 127.0.0.1 - - [31/Jul/2025 17:15:32] "[36mGET /static/css/pepe.css HTTP/1.1[0m" 304 -
2025-07-31 17:15:46,716 INFO werkzeug 127.0.0.1 - - [31/Jul/2025 17:15:46] "GET /dashboard/ HTTP/1.1" 200 -
2025-07-31 17:15:48,547 INFO werkzeug 127.0.0.1 - - [31/Jul/2025 17:15:48] "[36mGET /static/css/pepe.css HTTP/1.1[0m" 304 -
2025-07-31 17:27:22,328 INFO werkzeug 127.0.0.1 - - [31/Jul/2025 17:27:22] "GET / HTTP/1.1" 200 -
2025-07-31 17:27:22,334 INFO werkzeug 127.0.0.1 - - [31/Jul/2025 17:27:22] "[33mPOST /api/v1/login_key HTTP/1.1[0m" 404 -
2025-07-31 17:28:59,915 INFO werkzeug 127.0.0.1 - - [31/Jul/2025 17:28:59] "GET / HTTP/1.1" 200 -
2025-07-31 17:28:59,924 INFO werkzeug 127.0.0.1 - - [31/Jul/2025 17:28:59] "[33mPOST /api/v1/login_key HTTP/1.1[0m" 404 -
2025-07-31 17:30:04,785 INFO werkzeug 127.0.0.1 - - [31/Jul/2025 17:30:04] "GET / HTTP/1.1" 200 -
2025-07-31 17:30:04,790 INFO werkzeug 127.0.0.1 - - [31/Jul/2025 17:30:04] "[33mPOST /api/v1/login_key HTTP/1.1[0m" 404 -
2025-07-31 17:30:04,797 INFO werkzeug 127.0.0.1 - - [31/Jul/2025 17:30:04] "[33mPOST /api/v1/user_info HTTP/1.1[0m" 404 -
2025-07-31 17:30:04,806 INFO werkzeug 127.0.0.1 - - [31/Jul/2025 17:30:04] "[33mPOST /api/v1/admin/create_key HTTP/1.1[0m" 404 -
2025-07-31 17:31:13,002 INFO werkzeug 127.0.0.1 - - [31/Jul/2025 17:31:13] "[32mGET /auth/profile HTTP/1.1[0m" 302 -
2025-07-31 17:31:13,025 INFO werkzeug 127.0.0.1 - - [31/Jul/2025 17:31:13] "GET /auth/login?next=/auth/profile HTTP/1.1" 200 -
2025-07-31 17:31:13,077 INFO werkzeug 127.0.0.1 - - [31/Jul/2025 17:31:13] "[36mGET /static/css/pepe.css HTTP/1.1[0m" 304 -
2025-07-31 17:31:17,815 INFO werkzeug 127.0.0.1 - - [31/Jul/2025 17:31:17] "[32mGET /auth/profile HTTP/1.1[0m" 302 -
2025-07-31 17:31:17,826 INFO werkzeug 127.0.0.1 - - [31/Jul/2025 17:31:17] "GET /auth/login?next=/auth/profile HTTP/1.1" 200 -
2025-07-31 17:31:17,897 INFO werkzeug 127.0.0.1 - - [31/Jul/2025 17:31:17] "[36mGET /static/css/pepe.css HTTP/1.1[0m" 304 -
2025-07-31 17:31:29,038 WARNING root Failed login attempt for: <EMAIL>
2025-07-31 17:31:29,040 INFO werkzeug 127.0.0.1 - - [31/Jul/2025 17:31:29] "POST /auth/login?next=/auth/profile HTTP/1.1" 200 -
2025-07-31 17:31:29,068 INFO werkzeug 127.0.0.1 - - [31/Jul/2025 17:31:29] "[36mGET /static/css/pepe.css HTTP/1.1[0m" 304 -
2025-07-31 17:31:40,983 WARNING root Failed login attempt for: <EMAIL>
2025-07-31 17:31:40,985 INFO werkzeug 127.0.0.1 - - [31/Jul/2025 17:31:40] "POST /auth/login?next=/auth/profile HTTP/1.1" 200 -
2025-07-31 17:31:41,014 INFO werkzeug 127.0.0.1 - - [31/Jul/2025 17:31:41] "[36mGET /static/css/pepe.css HTTP/1.1[0m" 304 -
2025-07-31 17:59:42,986 INFO werkzeug 127.0.0.1 - - [31/Jul/2025 17:59:42] "[33mPOST /api/v1/login_key HTTP/1.1[0m" 404 -
2025-07-31 17:59:42,992 INFO werkzeug 127.0.0.1 - - [31/Jul/2025 17:59:42] "[33mPOST /api/v1/login_key HTTP/1.1[0m" 404 -
2025-07-31 17:59:42,997 INFO werkzeug 127.0.0.1 - - [31/Jul/2025 17:59:42] "[33mPOST /api/v1/login_key HTTP/1.1[0m" 404 -
2025-07-31 17:59:43,001 INFO werkzeug 127.0.0.1 - - [31/Jul/2025 17:59:43] "[33mPOST /api/v1/login_key HTTP/1.1[0m" 404 -
2025-07-31 17:59:52,720 INFO werkzeug 127.0.0.1 - - [31/Jul/2025 17:59:52] "GET / HTTP/1.1" 200 -
2025-07-31 18:01:11,405 INFO werkzeug 127.0.0.1 - - [31/Jul/2025 18:01:11] "[33mPOST /api/v1/login_key HTTP/1.1[0m" 404 -
2025-07-31 18:01:11,411 INFO werkzeug 127.0.0.1 - - [31/Jul/2025 18:01:11] "[33mPOST /api/v1/login_key HTTP/1.1[0m" 404 -
2025-07-31 18:01:11,419 INFO werkzeug 127.0.0.1 - - [31/Jul/2025 18:01:11] "[33mPOST /api/v1/login_key HTTP/1.1[0m" 404 -
2025-07-31 18:01:11,449 INFO werkzeug 127.0.0.1 - - [31/Jul/2025 18:01:11] "[33mPOST /api/v1/login_key HTTP/1.1[0m" 404 -
2025-07-31 18:01:34,987 INFO werkzeug 127.0.0.1 - - [31/Jul/2025 18:01:34] "[33mPOST /api/v1/login_key HTTP/1.1[0m" 404 -
2025-07-31 18:01:34,996 INFO werkzeug 127.0.0.1 - - [31/Jul/2025 18:01:34] "[33mPOST /api/v1/login_key HTTP/1.1[0m" 404 -
2025-07-31 18:01:35,010 INFO werkzeug 127.0.0.1 - - [31/Jul/2025 18:01:35] "[33mPOST /api/v1/login_key HTTP/1.1[0m" 404 -
2025-07-31 18:01:35,017 INFO werkzeug 127.0.0.1 - - [31/Jul/2025 18:01:35] "[33mPOST /api/v1/login_key HTTP/1.1[0m" 404 -
2025-07-31 18:01:57,809 INFO werkzeug 127.0.0.1 - - [31/Jul/2025 18:01:57] "[33mPOST /api/v1/login_key HTTP/1.1[0m" 404 -
2025-07-31 18:02:33,210 INFO werkzeug 127.0.0.1 - - [31/Jul/2025 18:02:33] "[33mPOST /api/v1/login_key HTTP/1.1[0m" 404 -
2025-07-31 18:03:23,063 INFO werkzeug 127.0.0.1 - - [31/Jul/2025 18:03:23] "[33mPOST /api/v1/login_key HTTP/1.1[0m" 404 -
2025-07-31 18:03:57,061 INFO werkzeug 127.0.0.1 - - [31/Jul/2025 18:03:57] "GET / HTTP/1.1" 200 -
2025-07-31 18:03:57,064 INFO werkzeug 127.0.0.1 - - [31/Jul/2025 18:03:57] "[33mPOST /api/v1/login_key HTTP/1.1[0m" 404 -
2025-07-31 18:03:57,069 INFO werkzeug 127.0.0.1 - - [31/Jul/2025 18:03:57] "[33mPOST /api/v1/user_info HTTP/1.1[0m" 404 -
2025-07-31 18:03:57,075 INFO werkzeug 127.0.0.1 - - [31/Jul/2025 18:03:57] "[33mPOST /api/v1/admin/create_key HTTP/1.1[0m" 404 -
2025-07-31 18:04:43,902 INFO flask_wtf.csrf The CSRF token is missing.
2025-07-31 18:04:43,902 INFO werkzeug 127.0.0.1 - - [31/Jul/2025 18:04:43] "[31m[1mPOST /api/v1/auth HTTP/1.1[0m" 400 -
2025-07-31 18:04:43,906 INFO werkzeug 127.0.0.1 - - [31/Jul/2025 18:04:43] "[33mPOST /api/v1/login_key HTTP/1.1[0m" 404 -
2025-07-31 18:05:31,520 INFO flask_wtf.csrf The CSRF token is missing.
2025-07-31 18:05:31,521 INFO werkzeug 127.0.0.1 - - [31/Jul/2025 18:05:31] "[31m[1mPOST /api/v1/auth HTTP/1.1[0m" 400 -
2025-07-31 18:05:31,525 INFO werkzeug 127.0.0.1 - - [31/Jul/2025 18:05:31] "[33mPOST /api/v1/login_key HTTP/1.1[0m" 404 -
2025-07-31 18:21:12,350 INFO werkzeug 127.0.0.1 - - [31/Jul/2025 18:21:12] "GET / HTTP/1.1" 200 -
2025-07-31 18:21:12,403 INFO werkzeug 127.0.0.1 - - [31/Jul/2025 18:21:12] "[33mPOST /api/v1/login_key HTTP/1.1[0m" 404 -
2025-07-31 18:21:12,520 INFO werkzeug 127.0.0.1 - - [31/Jul/2025 18:21:12] "GET /auth/login HTTP/1.1" 200 -
2025-07-31 18:21:12,526 INFO werkzeug 127.0.0.1 - - [31/Jul/2025 18:21:12] "[32mGET /admin/ HTTP/1.1[0m" 302 -
2025-07-31 18:21:12,533 INFO werkzeug 127.0.0.1 - - [31/Jul/2025 18:21:12] "GET /auth/login?next=/admin/ HTTP/1.1" 200 -
2025-07-31 18:21:12,538 INFO werkzeug 127.0.0.1 - - [31/Jul/2025 18:21:12] "[33mGET /admin/keyauth-dashboard HTTP/1.1[0m" 404 -
