# 🚀 PepeAuth Quick Start Guide

## ⚡ Immediate Fix for Current Issues

You're seeing database errors because the database schema needs to be updated. Here's how to fix it:

### Step 1: Fix the Database
```bash
python fix_database.py
```

This will update your existing database with the missing columns and tables.

### Step 2: Start the Application
```bash
python start.py
```

This will check dependencies, fix any remaining issues, and start the application.

**OR** use the simple startup script:
```bash
python run.py
```

## 🔧 Manual Fix (if needed)

If you're still having issues, try these steps:

1. **Delete the old database** (if you don't mind losing data):
   ```bash
   rm pepeauth.db
   ```

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Run the application**:
   ```bash
   python run.py
   ```

## 📋 What's Included

- **Web Dashboard**: http://localhost:5000
- **API Endpoints**: http://localhost:5000/api/v1/
- **Admin Panel**: Login as admin to access admin features
- **Python Client**: See `client/` folder for integration examples

## 🔑 Default Login

After first run, you can create an admin account through the web interface, or use the setup script:

```bash
python setup.py
```

## 📚 API Testing

Test the API with curl:

```bash
# Test authentication
curl -X POST http://localhost:5000/api/v1/login_key \
  -H "Content-Type: application/json" \
  -d '{
    "key": "your-license-key",
    "app_secret": "your-app-secret",
    "hwid": "test-hardware-id"
  }'
```

## 🐛 Common Issues

### "ModuleNotFoundError: No module named 'app.database'"
- **Fix**: Run `python fix_database.py` first

### "table plan has no column named price"
- **Fix**: Run `python fix_database.py` to update schema

### "Database is locked"
- **Fix**: Make sure no other instances are running, then restart

### Missing dependencies
- **Fix**: Run `pip install -r requirements.txt`

## 📁 Project Structure

```
pepeauth/
├── app/                 # Main Flask application
├── api/                 # API endpoints
├── client/              # Python client library
├── templates/           # HTML templates
├── static/              # CSS, JS, images
├── fix_database.py      # Database fix script
├── start.py            # Smart startup script
└── run.py              # Simple run script
```

## 🎯 Next Steps

1. **Create an admin account** through the web interface
2. **Create your first application** in the dashboard
3. **Generate license keys** for testing
4. **Test the Python client** in the `client/` folder
5. **Set up webhooks** for real-time notifications

## 💡 Tips

- Use `DEBUG=True` in `.env` for development
- Check the `logs/` folder for detailed error information
- The database file is `pepeauth.db` (SQLite)
- All configuration is in `.env` file

## 🆘 Need Help?

If you're still having issues:

1. Check the console output for specific error messages
2. Look in the `logs/` folder for detailed logs
3. Make sure all dependencies are installed
4. Try deleting `pepeauth.db` and starting fresh

---

**Happy coding! 🐸**
