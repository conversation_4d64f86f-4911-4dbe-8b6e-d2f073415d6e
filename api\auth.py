"""
KeyAuth-like API Authentication Module
Handles license key validation, HWID binding, and user authentication
"""

import logging
import hmac
import hashlib
import secrets
import json
from datetime import datetime, timedelta
from flask import Blueprint, request, jsonify, current_app, g
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(__file__)))
from app import query_db, execute_db, limiter
from app.security import (
    SecurityManager, RateLimiter, BanSystem, HWIDManager, SecurityLogger,
    require_rate_limit, require_not_banned
)
from app.logging_config import log_api_call, log_performance

bp = Blueprint('api_auth', __name__, url_prefix='/api/v1')

def generate_hmac(secret: str, message: str) -> str:
    """Generate HMAC-SHA256 signature for API security"""
    return hmac.new(secret.encode(), message.encode(), hashlib.sha256).hexdigest()

def verify_hmac(secret: str, message: str, signature: str) -> bool:
    """Verify HMAC-SHA256 signature"""
    expected = generate_hmac(secret, message)
    return hmac.compare_digest(expected, signature)

def get_hardware_id(request_data: dict) -> str:
    """Extract or generate hardware ID from request"""
    hwid = request_data.get('hwid')
    if not hwid:
        # Generate a simple HWID based on user agent and IP (for demo purposes)
        user_agent = request.headers.get('User-Agent', 'unknown')
        ip_address = request.remote_addr or 'unknown'
        hwid = hashlib.md5(f"{user_agent}:{ip_address}".encode()).hexdigest()[:16]
    return hwid

@bp.route('/login_key', methods=['POST'])
@require_not_banned()
@require_rate_limit('auth', limit=10, window=60)
@log_api_call()
@log_performance()
def login_key():
    """
    KeyAuth-style license key authentication
    
    Expected JSON payload:
    {
        "key": "license-key-here",
        "hwid": "hardware-id-here" (optional),
        "app_name": "MyApp" (optional, for multi-app support)
    }
    
    Returns:
    - 200: Success with user info and expiry
    - 400: Bad request (missing parameters)
    - 403: Invalid key, expired, banned, or HWID mismatch
    - 429: Rate limit exceeded
    """
    try:
        # Parse request data
        if not request.is_json:
            return jsonify({
                'status': False,
                'message': 'Content-Type must be application/json'
            }), 400
        
        data = request.get_json()
        license_key = data.get('key')
        app_secret = data.get('app_secret')
        hwid = get_hardware_id(data)

        # Validate required parameters
        if not license_key or not app_secret:
            return jsonify({
                'status': False,
                'message': 'License key and app secret are required'
            }), 400
        
        # Find the license key and verify app secret
        key_data = query_db(
            '''SELECT lk.*, a.name as app_name, a.secret as app_secret, a.is_active as app_active,
                      p.name as plan_name, u.username, u.email
               FROM license_key lk
               JOIN app a ON lk.app_id = a.id
               JOIN plan p ON lk.plan_id = p.id
               LEFT JOIN user u ON lk.user_id = u.id
               WHERE lk.key = ? AND a.secret = ?''',
            (license_key, app_secret),
            one=True
        )

        if not key_data:
            # Log failed authentication attempt
            execute_db(
                '''INSERT INTO auth_log (license_key_id, app_id, hwid, ip_address, user_agent, success, failure_reason, created_at)
                   VALUES (NULL, NULL, ?, ?, ?, ?, ?, ?)''',
                (hwid, request.remote_addr, request.headers.get('User-Agent', ''), 0,
                 'Invalid license key or app secret', datetime.now().isoformat())
            )
            logging.warning(f'Login attempt with invalid key/secret: {license_key[:8]}...')
            return jsonify({
                'status': False,
                'message': 'Invalid license key or app secret'
            }), 403

        # Check if app is active
        if not key_data['app_active']:
            return jsonify({
                'status': False,
                'message': 'Application is currently inactive'
            }), 403

        # Check if key is banned
        if key_data['is_banned']:
            return jsonify({
                'status': False,
                'message': f'License key is banned: {key_data["ban_reason"] or "No reason provided"}'
            }), 403
        
        # Check if key is expired
        if key_data['expires_at']:
            expires_at = datetime.fromisoformat(key_data['expires_at'])
            if expires_at < datetime.now():
                return jsonify({
                    'status': False,
                    'message': 'License key has expired'
                }), 403
        
        # Handle HWID binding
        if key_data['used'] and key_data['hwid']:
            # Key is already bound to a HWID
            if key_data['hwid'] != hwid:
                logging.warning(f'HWID mismatch for key: {license_key}')
                return jsonify({
                    'status': False,
                    'message': 'Hardware ID mismatch - key is bound to another device'
                }), 403
        elif not key_data['used']:
            # First time use - bind to current HWID and activate
            expires_at = datetime.now() + timedelta(days=current_app.config.get('DEFAULT_EXPIRY_DAYS', 30))
            
            execute_db(
                'UPDATE license_key SET used = 1, used_at = ?, hwid = ?, expires_at = ? WHERE id = ?',
                (datetime.now().isoformat(), hwid, expires_at.isoformat(), key_data['id'])
            )
            
            logging.info(f'License key activated: {license_key} for HWID: {hwid}')
        else:
            # Key is used but no HWID stored - bind to current HWID
            execute_db(
                'UPDATE license_key SET hwid = ? WHERE id = ?',
                (hwid, key_data['id'])
            )
        
        # Log successful authentication
        execute_db(
            '''INSERT INTO auth_log (license_key_id, app_id, user_id, hwid, ip_address, user_agent, success, created_at)
               VALUES (?, ?, ?, ?, ?, ?, ?, ?)''',
            (key_data['id'], key_data['app_id'], key_data['user_id'], hwid,
             request.remote_addr, request.headers.get('User-Agent', ''), 1, datetime.now().isoformat())
        )

        # Generate session token (simple implementation)
        session_token = secrets.token_urlsafe(32)

        # Calculate days until expiry
        days_left = -1  # -1 means lifetime
        if key_data['expires_at']:
            expires_at = datetime.fromisoformat(key_data['expires_at'])
            days_left = max(0, (expires_at - datetime.now()).days)

        # Trigger webhook if configured
        if key_data['app_id']:
            try:
                app_webhook = query_db(
                    'SELECT webhook_url, webhook_secret FROM app WHERE id = ? AND webhook_url IS NOT NULL',
                    (key_data['app_id'],),
                    one=True
                )

                if app_webhook:
                    webhook_payload = {
                        'event': 'auth_success',
                        'timestamp': datetime.now().isoformat(),
                        'app_id': str(key_data['app_id']),
                        'app_name': key_data['app_name'],
                        'data': {
                            'license_key': license_key[:8] + '...',
                            'hwid': hwid,
                            'ip_address': request.remote_addr,
                            'user_agent': request.headers.get('User-Agent', ''),
                            'plan': key_data['plan_name']
                        }
                    }

                    # Send webhook asynchronously (simplified)
                    import threading
                    def send_webhook():
                        try:
                            import requests
                            import json
                            payload_json = json.dumps(webhook_payload)
                            signature = generate_hmac(app_webhook['webhook_secret'] or '', payload_json)

                            headers = {
                                'Content-Type': 'application/json',
                                'X-Signature': signature,
                                'User-Agent': 'PepeAuth-Webhook/1.0'
                            }

                            response = requests.post(
                                app_webhook['webhook_url'],
                                data=payload_json,
                                headers=headers,
                                timeout=10
                            )

                            # Log webhook delivery
                            execute_db(
                                '''INSERT INTO webhook_log (app_id, event_type, payload, response_code, response_body, success, created_at)
                                   VALUES (?, ?, ?, ?, ?, ?, ?)''',
                                (key_data['app_id'], 'auth_success', payload_json, response.status_code,
                                 response.text[:1000], 1 if response.status_code == 200 else 0, datetime.now().isoformat())
                            )
                        except Exception as e:
                            logging.error(f'Webhook delivery failed: {str(e)}')

                    threading.Thread(target=send_webhook, daemon=True).start()
            except Exception as e:
                logging.error(f'Webhook trigger error: {str(e)}')

        return jsonify({
            'status': True,
            'message': f'Welcome, {key_data["plan_name"]} user!',
            'user_info': {
                'username': key_data['username'] or 'User',
                'email': key_data['email'] or 'N/A',
                'plan': key_data['plan_name'],
                'hwid': hwid,
                'app_name': key_data['app_name'],
                'expires_in_days': days_left,
                'expires_at': key_data['expires_at'],
                'created_at': key_data['created_at'],
                'used_at': key_data['used_at']
            },
            'session_token': session_token
        }), 200
        
    except Exception as e:
        logging.error(f'Login key error: {str(e)}')
        return jsonify({
            'status': False,
            'message': 'Internal server error'
        }), 500

@bp.route('/user_info', methods=['POST'])
@limiter.limit("20 per minute")
def user_info():
    """
    Get user information with valid session token
    
    Expected JSON payload:
    {
        "session_token": "token-here",
        "key": "license-key-here"
    }
    """
    try:
        if not request.is_json:
            return jsonify({
                'status': False,
                'message': 'Content-Type must be application/json'
            }), 400
        
        data = request.get_json()
        session_token = data.get('session_token')
        license_key = data.get('key')
        
        if not session_token or not license_key:
            return jsonify({
                'status': False,
                'message': 'Session token and license key are required'
            }), 400
        
        # Find the license key and user info
        key_data = query_db(
            'SELECT * FROM license_key WHERE key = ? AND used = 1',
            (license_key,),
            one=True
        )
        
        if not key_data:
            return jsonify({
                'status': False,
                'message': 'Invalid session or license key'
            }), 403
        
        # Calculate days until expiry
        days_left = 0
        if key_data['expires_at']:
            expires_at = datetime.fromisoformat(key_data['expires_at'])
            days_left = max(0, (expires_at - datetime.now()).days)
        
        return jsonify({
            'status': True,
            'user_info': {
                'username': 'User',
                'email': 'N/A',
                'plan': 'Premium',
                'hwid': key_data['hwid'],
                'created_at': key_data['created_at'],
                'key_created_at': key_data['created_at'],
                'key_used_at': key_data['used_at'],
                'expires_in_days': days_left,
                'expires_at': key_data['expires_at']
            }
        }), 200
        
    except Exception as e:
        logging.error(f'User info error: {str(e)}')
        return jsonify({
            'status': False,
            'message': 'Internal server error'
        }), 500

@bp.route('/reset_hwid', methods=['POST'])
@limiter.limit("5 per hour")
def reset_hwid():
    """
    Reset hardware ID for a license key

    Expected JSON payload:
    {
        "key": "license-key-here",
        "hwid": "new-hardware-id-here" (optional)
    }
    """
    try:
        if not request.is_json:
            return jsonify({
                'status': False,
                'message': 'Content-Type must be application/json'
            }), 400

        data = request.get_json()
        license_key = data.get('key')
        new_hwid = data.get('hwid') or get_hardware_id(data)

        if not license_key:
            return jsonify({
                'status': False,
                'message': 'License key is required'
            }), 400

        # Find the license key
        key_data = query_db(
            '''SELECT lk.*, u.hwid_reset_count, u.max_hwid_resets, u.id as user_id
               FROM license_key lk
               LEFT JOIN user u ON lk.user_id = u.id
               WHERE lk.key = ? AND lk.used = 1''',
            (license_key,),
            one=True
        )

        if not key_data:
            return jsonify({
                'status': False,
                'message': 'Invalid or unused license key'
            }), 403

        # Check HWID reset limit
        if key_data['user_id'] and key_data['hwid_reset_count'] >= key_data['max_hwid_resets']:
            return jsonify({
                'status': False,
                'message': 'HWID reset limit exceeded'
            }), 403

        # Log the HWID reset
        execute_db(
            '''INSERT INTO hwid_reset_log (user_id, license_key_id, old_hwid, new_hwid, reason, created_at)
               VALUES (?, ?, ?, ?, ?, ?)''',
            (key_data['user_id'], key_data['id'], key_data['hwid'], new_hwid, 'User requested', datetime.now().isoformat())
        )

        # Update the license key HWID
        execute_db(
            'UPDATE license_key SET hwid = ? WHERE id = ?',
            (new_hwid, key_data['id'])
        )

        # Increment user's HWID reset count
        if key_data['user_id']:
            execute_db(
                'UPDATE user SET hwid_reset_count = hwid_reset_count + 1 WHERE id = ?',
                (key_data['user_id'],)
            )

        # Log the activity
        execute_db(
            '''INSERT INTO system_log (level, category, message, user_id, ip_address, created_at)
               VALUES (?, ?, ?, ?, ?, ?)''',
            ('INFO', 'HWID_RESET', f'HWID reset for key {license_key[:8]}...',
             key_data['user_id'], request.remote_addr, datetime.now().isoformat())
        )

        logging.info(f'HWID reset for key: {license_key} from {key_data["hwid"]} to {new_hwid}')

        return jsonify({
            'status': True,
            'message': 'Hardware ID reset successfully',
            'new_hwid': new_hwid,
            'resets_remaining': key_data['max_hwid_resets'] - key_data['hwid_reset_count'] - 1
        }), 200

    except Exception as e:
        logging.error(f'HWID reset error: {str(e)}')
        return jsonify({
            'status': False,
            'message': 'Internal server error'
        }), 500

@bp.route('/webhook', methods=['POST'])
@limiter.limit("100 per hour")
def webhook_trigger():
    """
    Trigger webhook for testing purposes

    Expected JSON payload:
    {
        "app_secret": "app-secret-here",
        "event": "test",
        "data": {...}
    }
    """
    try:
        if not request.is_json:
            return jsonify({
                'status': False,
                'message': 'Content-Type must be application/json'
            }), 400

        data = request.get_json()
        app_secret = data.get('app_secret')
        event = data.get('event', 'test')
        event_data = data.get('data', {})

        if not app_secret:
            return jsonify({
                'status': False,
                'message': 'App secret is required'
            }), 400

        # Find the app
        app_data = query_db(
            'SELECT * FROM app WHERE secret = ? AND is_active = 1',
            (app_secret,),
            one=True
        )

        if not app_data:
            return jsonify({
                'status': False,
                'message': 'Invalid app secret'
            }), 403

        # Check if webhook is configured
        if not app_data['webhook_url']:
            return jsonify({
                'status': False,
                'message': 'No webhook configured for this app'
            }), 400

        # Prepare webhook payload
        webhook_payload = {
            'event': event,
            'timestamp': datetime.now().isoformat(),
            'app_id': str(app_data['id']),
            'app_name': app_data['name'],
            'data': event_data
        }

        # Send webhook (simplified - in production, use a queue)
        import requests
        import json

        try:
            # Generate HMAC signature
            payload_json = json.dumps(webhook_payload)
            signature = generate_hmac(app_data['webhook_secret'] or '', payload_json)

            headers = {
                'Content-Type': 'application/json',
                'X-Signature': signature,
                'User-Agent': 'PepeAuth-Webhook/1.0'
            }

            response = requests.post(
                app_data['webhook_url'],
                data=payload_json,
                headers=headers,
                timeout=10
            )

            # Log webhook delivery
            execute_db(
                '''INSERT INTO webhook_log (app_id, event_type, payload, response_code, response_body, success, created_at)
                   VALUES (?, ?, ?, ?, ?, ?, ?)''',
                (app_data['id'], event, payload_json, response.status_code,
                 response.text[:1000], 1 if response.status_code == 200 else 0, datetime.now().isoformat())
            )

            return jsonify({
                'status': True,
                'message': 'Webhook triggered successfully',
                'response_code': response.status_code
            }), 200

        except requests.RequestException as e:
            # Log failed webhook delivery
            execute_db(
                '''INSERT INTO webhook_log (app_id, event_type, payload, response_code, response_body, success, created_at)
                   VALUES (?, ?, ?, ?, ?, ?, ?)''',
                (app_data['id'], event, payload_json, 0, str(e)[:1000], 0, datetime.now().isoformat())
            )

            return jsonify({
                'status': False,
                'message': f'Webhook delivery failed: {str(e)}'
            }), 500

    except Exception as e:
        logging.error(f'Webhook trigger error: {str(e)}')
        return jsonify({
            'status': False,
            'message': 'Internal server error'
        }), 500

@bp.route('/log', methods=['POST'])
@limiter.limit("50 per hour")
def log_activity():
    """
    Log custom activity from client applications

    Expected JSON payload:
    {
        "app_secret": "app-secret-here",
        "key": "license-key-here",
        "action": "action-name",
        "details": "optional details"
    }
    """
    try:
        if not request.is_json:
            return jsonify({
                'status': False,
                'message': 'Content-Type must be application/json'
            }), 400

        data = request.get_json()
        app_secret = data.get('app_secret')
        license_key = data.get('key')
        action = data.get('action')
        details = data.get('details', '')

        if not all([app_secret, license_key, action]):
            return jsonify({
                'status': False,
                'message': 'App secret, license key, and action are required'
            }), 400

        # Verify app and key
        result = query_db(
            '''SELECT a.id as app_id, a.name as app_name, lk.id as key_id, lk.user_id
               FROM app a
               JOIN license_key lk ON lk.app_id = a.id
               WHERE a.secret = ? AND lk.key = ? AND a.is_active = 1 AND lk.used = 1''',
            (app_secret, license_key),
            one=True
        )

        if not result:
            return jsonify({
                'status': False,
                'message': 'Invalid app secret or license key'
            }), 403

        # Log the activity
        execute_db(
            '''INSERT INTO auth_log (license_key_id, app_id, user_id, ip_address, user_agent, success, failure_reason, created_at)
               VALUES (?, ?, ?, ?, ?, ?, ?, ?)''',
            (result['key_id'], result['app_id'], result['user_id'], request.remote_addr,
             request.headers.get('User-Agent', ''), 1, f'Custom action: {action} - {details}', datetime.now().isoformat())
        )

        return jsonify({
            'status': True,
            'message': 'Activity logged successfully'
        }), 200

    except Exception as e:
        logging.error(f'Log activity error: {str(e)}')
        return jsonify({
            'status': False,
            'message': 'Internal server error'
        }), 500

@bp.errorhandler(429)
def ratelimit_handler(e):
    """Handle rate limit exceeded"""
    return jsonify({
        'status': False,
        'message': 'Rate limit exceeded. Please try again later.'
    }), 429
