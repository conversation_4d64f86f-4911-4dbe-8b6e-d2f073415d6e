"""
KeyAuth-like API Authentication Module
Handles license key validation, HWID binding, and user authentication
"""

import logging
import hmac
import hashlib
import secrets
from datetime import datetime, timedelta
from flask import Blueprint, request, jsonify, current_app
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(__file__)))
from app import query_db, execute_db, limiter

bp = Blueprint('api_auth', __name__, url_prefix='/api/v1')

def generate_hmac(secret: str, message: str) -> str:
    """Generate HMAC-SHA256 signature for API security"""
    return hmac.new(secret.encode(), message.encode(), hashlib.sha256).hexdigest()

def verify_hmac(secret: str, message: str, signature: str) -> bool:
    """Verify HMAC-SHA256 signature"""
    expected = generate_hmac(secret, message)
    return hmac.compare_digest(expected, signature)

def get_hardware_id(request_data: dict) -> str:
    """Extract or generate hardware ID from request"""
    hwid = request_data.get('hwid')
    if not hwid:
        # Generate a simple HWID based on user agent and IP (for demo purposes)
        user_agent = request.headers.get('User-Agent', 'unknown')
        ip_address = request.remote_addr or 'unknown'
        hwid = hashlib.md5(f"{user_agent}:{ip_address}".encode()).hexdigest()[:16]
    return hwid

@bp.route('/login_key', methods=['POST'])
@limiter.limit("10 per minute")
def login_key():
    """
    KeyAuth-style license key authentication
    
    Expected JSON payload:
    {
        "key": "license-key-here",
        "hwid": "hardware-id-here" (optional),
        "app_name": "MyApp" (optional, for multi-app support)
    }
    
    Returns:
    - 200: Success with user info and expiry
    - 400: Bad request (missing parameters)
    - 403: Invalid key, expired, banned, or HWID mismatch
    - 429: Rate limit exceeded
    """
    try:
        # Parse request data
        if not request.is_json:
            return jsonify({
                'status': False,
                'message': 'Content-Type must be application/json'
            }), 400
        
        data = request.get_json()
        license_key = data.get('key')
        hwid = get_hardware_id(data)
        app_name = data.get('app_name', 'default')
        
        # Validate required parameters
        if not license_key:
            return jsonify({
                'status': False,
                'message': 'License key is required'
            }), 400
        
        # Find the license key in database
        key_data = query_db(
            'SELECT * FROM license_key WHERE key = ?',
            (license_key,),
            one=True
        )
        
        if not key_data:
            logging.warning(f'Login attempt with invalid key: {license_key}')
            return jsonify({
                'status': False,
                'message': 'Invalid license key'
            }), 403
        
        # Check if key is expired
        if key_data['expires_at']:
            expires_at = datetime.fromisoformat(key_data['expires_at'])
            if expires_at < datetime.now():
                return jsonify({
                    'status': False,
                    'message': 'License key has expired'
                }), 403
        
        # Handle HWID binding
        if key_data['used'] and key_data['hwid']:
            # Key is already bound to a HWID
            if key_data['hwid'] != hwid:
                logging.warning(f'HWID mismatch for key: {license_key}')
                return jsonify({
                    'status': False,
                    'message': 'Hardware ID mismatch - key is bound to another device'
                }), 403
        elif not key_data['used']:
            # First time use - bind to current HWID and activate
            expires_at = datetime.now() + timedelta(days=current_app.config.get('DEFAULT_EXPIRY_DAYS', 30))
            
            execute_db(
                'UPDATE license_key SET used = 1, used_at = ?, hwid = ?, expires_at = ? WHERE id = ?',
                (datetime.now().isoformat(), hwid, expires_at.isoformat(), key_data['id'])
            )
            
            logging.info(f'License key activated: {license_key} for HWID: {hwid}')
        else:
            # Key is used but no HWID stored - bind to current HWID
            execute_db(
                'UPDATE license_key SET hwid = ? WHERE id = ?',
                (hwid, key_data['id'])
            )
        
        # Generate session token (simple implementation)
        session_token = secrets.token_urlsafe(32)
        
        # Calculate days until expiry
        days_left = 0
        if key_data['expires_at']:
            expires_at = datetime.fromisoformat(key_data['expires_at'])
            days_left = max(0, (expires_at - datetime.now()).days)
        
        return jsonify({
            'status': True,
            'message': 'Welcome, Premium user!',
            'user_info': {
                'username': 'User',
                'email': 'N/A',
                'plan': 'Premium',
                'hwid': hwid,
                'expires_in_days': days_left,
                'expires_at': key_data['expires_at']
            },
            'session_token': session_token
        }), 200
        
    except Exception as e:
        logging.error(f'Login key error: {str(e)}')
        return jsonify({
            'status': False,
            'message': 'Internal server error'
        }), 500

@bp.route('/user_info', methods=['POST'])
@limiter.limit("20 per minute")
def user_info():
    """
    Get user information with valid session token
    
    Expected JSON payload:
    {
        "session_token": "token-here",
        "key": "license-key-here"
    }
    """
    try:
        if not request.is_json:
            return jsonify({
                'status': False,
                'message': 'Content-Type must be application/json'
            }), 400
        
        data = request.get_json()
        session_token = data.get('session_token')
        license_key = data.get('key')
        
        if not session_token or not license_key:
            return jsonify({
                'status': False,
                'message': 'Session token and license key are required'
            }), 400
        
        # Find the license key and user info
        key_data = query_db(
            'SELECT * FROM license_key WHERE key = ? AND used = 1',
            (license_key,),
            one=True
        )
        
        if not key_data:
            return jsonify({
                'status': False,
                'message': 'Invalid session or license key'
            }), 403
        
        # Calculate days until expiry
        days_left = 0
        if key_data['expires_at']:
            expires_at = datetime.fromisoformat(key_data['expires_at'])
            days_left = max(0, (expires_at - datetime.now()).days)
        
        return jsonify({
            'status': True,
            'user_info': {
                'username': 'User',
                'email': 'N/A',
                'plan': 'Premium',
                'hwid': key_data['hwid'],
                'created_at': key_data['created_at'],
                'key_created_at': key_data['created_at'],
                'key_used_at': key_data['used_at'],
                'expires_in_days': days_left,
                'expires_at': key_data['expires_at']
            }
        }), 200
        
    except Exception as e:
        logging.error(f'User info error: {str(e)}')
        return jsonify({
            'status': False,
            'message': 'Internal server error'
        }), 500

@bp.errorhandler(429)
def ratelimit_handler(e):
    """Handle rate limit exceeded"""
    return jsonify({
        'status': False,
        'message': 'Rate limit exceeded. Please try again later.'
    }), 429
