{% extends "base.html" %}

{% block title %}KeyAuth Admin Dashboard - PepeAuth{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-white">🔑 KeyAuth Admin Dashboard</h1>
        <p class="mt-1 text-gray-300">Premium License Management System</p>
    </div>
    
    <!-- Quick Actions -->
    <div class="mb-8">
        <div class="flex flex-wrap gap-4">
            <button onclick="createPremiumKeys()" class="btn-primary">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                Create Premium Keys
            </button>
            <button onclick="viewUsers()" class="btn-secondary">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                </svg>
                Manage Users
            </button>
            <button onclick="viewSecurityLogs()" class="btn-secondary">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                </svg>
                Security Logs
            </button>
        </div>
    </div>
    
    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- Premium Users -->
        <div class="glass-card p-6 rounded-xl border border-yellow-500/20">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-yellow-500/20 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-300">Premium Users</p>
                    <p class="text-2xl font-semibold text-yellow-400" id="premium-users">{{ stats.premium_users or 0 }}</p>
                </div>
            </div>
        </div>
        
        <!-- Total License Keys -->
        <div class="glass-card p-6 rounded-xl">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-blue-500/20 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-300">Total Keys</p>
                    <p class="text-2xl font-semibold text-white" id="total-keys">{{ stats.total_keys or 0 }}</p>
                </div>
            </div>
        </div>
        
        <!-- Active Keys -->
        <div class="glass-card p-6 rounded-xl">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-green-500/20 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-300">Active Keys</p>
                    <p class="text-2xl font-semibold text-green-400" id="active-keys">{{ stats.used_keys or 0 }}</p>
                </div>
            </div>
        </div>
        
        <!-- Expired Keys -->
        <div class="glass-card p-6 rounded-xl">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-red-500/20 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-300">Expired Keys</p>
                    <p class="text-2xl font-semibold text-red-400" id="expired-keys">{{ stats.expired_keys or 0 }}</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Main Content Tabs -->
    <div class="mb-6">
        <nav class="flex space-x-8" aria-label="Tabs">
            <button onclick="showTab('keys')" id="tab-keys" class="tab-button active">
                License Keys
            </button>
            <button onclick="showTab('users')" id="tab-users" class="tab-button">
                Users
            </button>
            <button onclick="showTab('security')" id="tab-security" class="tab-button">
                Security
            </button>
            <button onclick="showTab('analytics')" id="tab-analytics" class="tab-button">
                Analytics
            </button>
        </nav>
    </div>
    
    <!-- Tab Content -->
    <div id="content-keys" class="tab-content">
        <div class="glass-card p-6 rounded-xl">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-xl font-semibold text-white">Premium License Keys</h2>
                <button onclick="createPremiumKeys()" class="btn-primary">Create New Keys</button>
            </div>
            
            <!-- Key Generation Form -->
            <div id="key-form" class="mb-6 p-4 bg-gray-800/50 rounded-lg" style="display: none;">
                <h3 class="text-lg font-medium text-white mb-4">Generate Premium Keys</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">Number of Keys</label>
                        <input type="number" id="key-count" value="1" min="1" max="100" class="form-input">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">Expiry Days</label>
                        <input type="number" id="expiry-days" value="30" min="1" max="365" class="form-input">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">Plan</label>
                        <select id="plan-select" class="form-input">
                            <option value="Premium">Premium</option>
                            <option value="Pro">Pro</option>
                            <option value="Military">Military</option>
                        </select>
                    </div>
                </div>
                <div class="mt-4 flex gap-4">
                    <button onclick="generateKeys()" class="btn-primary">Generate Keys</button>
                    <button onclick="hideKeyForm()" class="btn-secondary">Cancel</button>
                </div>
            </div>
            
            <!-- Keys Table -->
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-700">
                    <thead class="bg-gray-800/50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Key</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Plan</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Created</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Expires</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody id="keys-table" class="bg-gray-800/20 divide-y divide-gray-700">
                        <!-- Keys will be loaded here -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    
    <div id="content-users" class="tab-content" style="display: none;">
        <div class="glass-card p-6 rounded-xl">
            <h2 class="text-xl font-semibold text-white mb-6">User Management</h2>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-700">
                    <thead class="bg-gray-800/50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">User</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Plan</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Joined</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody id="users-table" class="bg-gray-800/20 divide-y divide-gray-700">
                        <!-- Users will be loaded here -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    
    <div id="content-security" class="tab-content" style="display: none;">
        <div class="glass-card p-6 rounded-xl">
            <h2 class="text-xl font-semibold text-white mb-6">Security Dashboard</h2>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                    <h3 class="text-lg font-medium text-white mb-4">Recent Security Events</h3>
                    <div id="security-events" class="space-y-3">
                        <!-- Security events will be loaded here -->
                    </div>
                </div>
                <div>
                    <h3 class="text-lg font-medium text-white mb-4">Failed Login Attempts</h3>
                    <div id="failed-logins" class="space-y-3">
                        <!-- Failed logins will be loaded here -->
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div id="content-analytics" class="tab-content" style="display: none;">
        <div class="glass-card p-6 rounded-xl">
            <h2 class="text-xl font-semibold text-white mb-6">Analytics & Reports</h2>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                    <h3 class="text-lg font-medium text-white mb-4">License Usage</h3>
                    <canvas id="usage-chart" width="400" height="200"></canvas>
                </div>
                <div>
                    <h3 class="text-lg font-medium text-white mb-4">User Growth</h3>
                    <canvas id="growth-chart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript for KeyAuth Admin Dashboard -->
<script>
// Tab Management
function showTab(tabName) {
    // Hide all tab contents
    document.querySelectorAll('.tab-content').forEach(content => {
        content.style.display = 'none';
    });
    
    // Remove active class from all tabs
    document.querySelectorAll('.tab-button').forEach(button => {
        button.classList.remove('active');
    });
    
    // Show selected tab content
    document.getElementById(`content-${tabName}`).style.display = 'block';
    document.getElementById(`tab-${tabName}`).classList.add('active');
    
    // Load data for the selected tab
    loadTabData(tabName);
}

// Load data for specific tab
function loadTabData(tabName) {
    switch(tabName) {
        case 'keys':
            loadLicenseKeys();
            break;
        case 'users':
            loadUsers();
            break;
        case 'security':
            loadSecurityEvents();
            break;
        case 'analytics':
            loadAnalytics();
            break;
    }
}

// Key Management Functions
function createPremiumKeys() {
    document.getElementById('key-form').style.display = 'block';
}

function hideKeyForm() {
    document.getElementById('key-form').style.display = 'none';
}

function generateKeys() {
    const count = document.getElementById('key-count').value;
    const expiryDays = document.getElementById('expiry-days').value;
    const plan = document.getElementById('plan-select').value;
    
    // Show loading
    showNotification('Generating keys...', 'info');
    
    // Simulate key generation (replace with actual API call)
    setTimeout(() => {
        showNotification(`Successfully generated ${count} ${plan} license keys!`, 'success');
        hideKeyForm();
        loadLicenseKeys();
        updateStats();
    }, 1000);
}

// Data Loading Functions
function loadLicenseKeys() {
    // Simulate loading license keys
    const keysTable = document.getElementById('keys-table');
    keysTable.innerHTML = `
        <tr>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-mono text-white">GLBHKHB46D0KWASI</td>
            <td class="px-6 py-4 whitespace-nowrap"><span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-500/20 text-green-400">Active</span></td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-yellow-400">Premium</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">2025-07-31</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">2025-08-30</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <button class="text-red-400 hover:text-red-300">Revoke</button>
            </td>
        </tr>
        <tr>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-mono text-white">VDNFEORB6GPPFAZL</td>
            <td class="px-6 py-4 whitespace-nowrap"><span class="px-2 py-1 text-xs font-semibold rounded-full bg-gray-500/20 text-gray-400">Unused</span></td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-yellow-400">Premium</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">2025-07-31</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">-</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <button class="text-red-400 hover:text-red-300">Delete</button>
            </td>
        </tr>
    `;
}

function loadUsers() {
    const usersTable = document.getElementById('users-table');
    usersTable.innerHTML = `
        <tr>
            <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm font-medium text-white">admin</div>
                <div class="text-sm text-gray-300"><EMAIL></div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-yellow-400">Premium</td>
            <td class="px-6 py-4 whitespace-nowrap"><span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-500/20 text-green-400">Active</span></td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">2025-07-31</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <button class="text-blue-400 hover:text-blue-300 mr-3">Edit</button>
                <button class="text-red-400 hover:text-red-300">Ban</button>
            </td>
        </tr>
    `;
}

function loadSecurityEvents() {
    const securityEvents = document.getElementById('security-events');
    securityEvents.innerHTML = `
        <div class="p-3 bg-gray-800/50 rounded-lg">
            <div class="flex justify-between items-start">
                <div>
                    <p class="text-sm font-medium text-white">Successful Login</p>
                    <p class="text-xs text-gray-400">User: admin from 127.0.0.1</p>
                </div>
                <span class="text-xs text-gray-500">2 min ago</span>
            </div>
        </div>
        <div class="p-3 bg-gray-800/50 rounded-lg">
            <div class="flex justify-between items-start">
                <div>
                    <p class="text-sm font-medium text-yellow-400">License Key Activated</p>
                    <p class="text-xs text-gray-400">Key: GLBH...WASI</p>
                </div>
                <span class="text-xs text-gray-500">5 min ago</span>
            </div>
        </div>
    `;
}

function loadAnalytics() {
    // Placeholder for analytics charts
    showNotification('Analytics charts would be implemented here', 'info');
}

// Utility Functions
function updateStats() {
    // Update dashboard statistics
    document.getElementById('premium-users').textContent = '2';
    document.getElementById('total-keys').textContent = '5';
    document.getElementById('active-keys').textContent = '1';
    document.getElementById('expired-keys').textContent = '0';
}

function showNotification(message, type) {
    // Simple notification system
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 p-4 rounded-lg z-50 ${
        type === 'success' ? 'bg-green-500/20 text-green-400 border border-green-500/30' :
        type === 'error' ? 'bg-red-500/20 text-red-400 border border-red-500/30' :
        'bg-blue-500/20 text-blue-400 border border-blue-500/30'
    }`;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.remove();
    }, 3000);
}

// Initialize dashboard
document.addEventListener('DOMContentLoaded', function() {
    showTab('keys');
    updateStats();
});
</script>

<style>
.tab-button {
    @apply py-2 px-1 border-b-2 border-transparent font-medium text-sm text-gray-400 hover:text-white hover:border-gray-300 transition-colors;
}

.tab-button.active {
    @apply border-pepe-green text-pepe-green;
}

.form-input {
    @apply w-full px-3 py-2 bg-gray-800/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-pepe-green focus:border-transparent;
}

.btn-primary {
    @apply inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-pepe-green hover:bg-pepe-green/80 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-pepe-green transition-colors;
}

.btn-secondary {
    @apply inline-flex items-center px-4 py-2 border border-gray-600 text-sm font-medium rounded-lg text-gray-300 bg-gray-800/50 hover:bg-gray-700/50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors;
}
</style>
{% endblock %}
