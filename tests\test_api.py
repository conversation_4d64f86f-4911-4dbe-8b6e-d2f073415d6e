"""
Tests for API endpoints
"""

import pytest
import json
from datetime import datetime, timedelta
from app import execute_db

class TestAuthAPI:
    """Test the /api/v1/auth endpoint."""
    
    def test_auth_missing_parameters(self, client):
        """Test auth endpoint with missing parameters."""
        response = client.post('/api/v1/auth', json={})
        assert response.status_code == 400
        
        data = json.loads(response.data)
        assert data['status'] == False
        assert 'required' in data['message']
    
    def test_auth_app_not_found(self, client):
        """Test auth endpoint with non-existent app."""
        response = client.post('/api/v1/auth', json={
            'app_name': 'NonExistentApp',
            'key': 'some-key',
            'hwid': 'some-hwid'
        })
        
        assert response.status_code == 404
        data = json.loads(response.data)
        assert data['status'] == False
        assert data['message'] == 'App not found'
    
    def test_auth_invalid_key(self, client, test_app):
        """Test auth endpoint with invalid license key."""
        response = client.post('/api/v1/auth', json={
            'app_name': test_app['name'],
            'key': 'invalid-key',
            'hwid': 'some-hwid'
        })
        
        assert response.status_code == 403
        data = json.loads(response.data)
        assert data['status'] == False
        assert data['message'] == 'Invalid license key'
    
    def test_auth_successful_activation(self, client, app, test_app, test_license_key):
        """Test successful license key activation."""
        response = client.post('/api/v1/auth', json={
            'app_name': test_app['name'],
            'key': test_license_key['key'],
            'hwid': 'test-hwid-123'
        })
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['status'] == True
        assert data['message'] == 'License activated successfully'
        assert 'expires' in data
        
        # Verify key was marked as used in database
        with app.app_context():
            from app import query_db
            key_data = query_db(
                'SELECT * FROM license_key WHERE key = ?',
                (test_license_key['key'],),
                one=True
            )
            assert key_data['used'] == 1
            assert key_data['hwid'] == 'test-hwid-123'
            assert key_data['expires_at'] is not None
    
    def test_auth_hwid_lock_enabled(self, client, app, test_app, test_license_key):
        """Test auth with HWID lock enabled."""
        # Enable HWID lock for the app
        with app.app_context():
            execute_db(
                'UPDATE app SET hwid_lock = 1 WHERE id = ?',
                (test_app['id'],)
            )
        
        # First activation
        response = client.post('/api/v1/auth', json={
            'app_name': test_app['name'],
            'key': test_license_key['key'],
            'hwid': 'hwid-123'
        })
        assert response.status_code == 200
        
        # Second activation with different HWID should fail
        response = client.post('/api/v1/auth', json={
            'app_name': test_app['name'],
            'key': test_license_key['key'],
            'hwid': 'different-hwid'
        })
        assert response.status_code == 403
        data = json.loads(response.data)
        assert data['message'] == 'Hardware ID mismatch'
        
        # Second activation with same HWID should succeed
        response = client.post('/api/v1/auth', json={
            'app_name': test_app['name'],
            'key': test_license_key['key'],
            'hwid': 'hwid-123'
        })
        assert response.status_code == 200
    
    def test_auth_expired_key(self, client, app, test_app, test_license_key):
        """Test auth with expired license key."""
        # Mark key as used and expired
        expired_date = (datetime.now() - timedelta(days=1)).isoformat()
        with app.app_context():
            execute_db(
                'UPDATE license_key SET used = 1, expires_at = ? WHERE id = ?',
                (expired_date, test_license_key['id'])
            )
        
        response = client.post('/api/v1/auth', json={
            'app_name': test_app['name'],
            'key': test_license_key['key'],
            'hwid': 'test-hwid'
        })
        
        assert response.status_code == 403
        data = json.loads(response.data)
        assert data['message'] == 'License key has expired'

class TestCheckKeyAPI:
    """Test the /api/v1/check_key endpoint."""
    
    def test_check_key_missing_parameters(self, client):
        """Test check_key endpoint with missing parameters."""
        response = client.post('/api/v1/check_key', json={})
        assert response.status_code == 400
        
        data = json.loads(response.data)
        assert data['status'] == False
        assert 'required' in data['message']
    
    def test_check_key_app_not_found(self, client):
        """Test check_key endpoint with non-existent app."""
        response = client.post('/api/v1/check_key', json={
            'app_name': 'NonExistentApp',
            'key': 'some-key'
        })
        
        assert response.status_code == 404
        data = json.loads(response.data)
        assert data['status'] == False
        assert data['message'] == 'App not found'
    
    def test_check_key_not_found(self, client, test_app):
        """Test check_key endpoint with non-existent key."""
        response = client.post('/api/v1/check_key', json={
            'app_name': test_app['name'],
            'key': 'invalid-key'
        })
        
        assert response.status_code == 404
        data = json.loads(response.data)
        assert data['status'] == False
        assert data['message'] == 'License key not found'
    
    def test_check_key_unused(self, client, test_app, test_license_key):
        """Test check_key endpoint with unused key."""
        response = client.post('/api/v1/check_key', json={
            'app_name': test_app['name'],
            'key': test_license_key['key']
        })
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['status'] == True
        assert data['key_info']['used'] == False
        assert data['key_info']['expired'] == False
        assert data['key_info']['hwid'] is None
    
    def test_check_key_used(self, client, app, test_app, test_license_key):
        """Test check_key endpoint with used key."""
        # Mark key as used
        expires_at = (datetime.now() + timedelta(days=30)).isoformat()
        with app.app_context():
            execute_db(
                'UPDATE license_key SET used = 1, used_at = ?, expires_at = ?, hwid = ? WHERE id = ?',
                (datetime.now().isoformat(), expires_at, 'test-hwid', test_license_key['id'])
            )
        
        response = client.post('/api/v1/check_key', json={
            'app_name': test_app['name'],
            'key': test_license_key['key']
        })
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['status'] == True
        assert data['key_info']['used'] == True
        assert data['key_info']['expired'] == False
        assert data['key_info']['hwid'] == 'test-hwid'

class TestAppInfoAPI:
    """Test the /api/v1/app_info endpoint."""
    
    def test_app_info_missing_app_name(self, client):
        """Test app_info endpoint with missing app name."""
        response = client.post('/api/v1/app_info', json={})
        assert response.status_code == 400
        
        data = json.loads(response.data)
        assert data['status'] == False
        assert 'app_name is required' in data['message']
    
    def test_app_info_app_not_found(self, client):
        """Test app_info endpoint with non-existent app."""
        response = client.post('/api/v1/app_info', json={
            'app_name': 'NonExistentApp'
        })
        
        assert response.status_code == 404
        data = json.loads(response.data)
        assert data['status'] == False
        assert data['message'] == 'App not found'
    
    def test_app_info_public(self, client, test_app):
        """Test app_info endpoint without secret (public info)."""
        response = client.post('/api/v1/app_info', json={
            'app_name': test_app['name']
        })
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['status'] == True
        assert data['app_info']['name'] == test_app['name']
        assert data['app_info']['hwid_lock'] == test_app['hwid_lock']
        assert data['app_info']['expiry_days'] == test_app['expiry_days']
        assert 'statistics' not in data['app_info']  # Should not include stats without secret
    
    def test_app_info_with_secret(self, client, app, test_app, test_license_key):
        """Test app_info endpoint with correct secret (includes statistics)."""
        response = client.post('/api/v1/app_info', json={
            'app_name': test_app['name'],
            'secret': test_app['secret']
        })
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['status'] == True
        assert data['app_info']['name'] == test_app['name']
        assert 'statistics' in data['app_info']
        assert data['app_info']['statistics']['total_keys'] == 1
        assert data['app_info']['statistics']['used_keys'] == 0
        assert data['app_info']['statistics']['unused_keys'] == 1
    
    def test_app_info_wrong_secret(self, client, test_app):
        """Test app_info endpoint with wrong secret."""
        response = client.post('/api/v1/app_info', json={
            'app_name': test_app['name'],
            'secret': 'wrong-secret'
        })
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['status'] == True
        assert 'statistics' not in data['app_info']  # Should not include stats with wrong secret

class TestAPIRateLimit:
    """Test API rate limiting functionality."""
    
    def test_rate_limit_headers(self, client, test_app, test_license_key):
        """Test that rate limit headers are present."""
        response = client.post('/api/v1/auth', json={
            'app_name': test_app['name'],
            'key': test_license_key['key'],
            'hwid': 'test-hwid'
        })
        
        # Check for rate limit headers (Flask-Limiter adds these)
        assert response.status_code in [200, 403]  # Could be 403 if already used
        # Note: Rate limit headers might not be present in test environment
    
    def test_api_with_form_data(self, client, test_app, test_license_key):
        """Test API endpoints accept form data as well as JSON."""
        response = client.post('/api/v1/check_key', data={
            'app_name': test_app['name'],
            'key': test_license_key['key']
        })
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['status'] == True
