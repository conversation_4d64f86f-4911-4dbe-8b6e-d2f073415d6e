#!/usr/bin/env python3
"""
Debug API endpoints to see what's happening
"""

from app import create_app
import json

def debug_api():
    """Debug API endpoints"""
    app = create_app()
    
    with app.test_client() as client:
        print("🔍 Debugging API Endpoints")
        print("=" * 50)
        
        # Test login_key endpoint
        print("🔑 Testing /api/v1/login_key...")
        response = client.post('/api/v1/login_key', 
                             json={'key': 'INVALID-KEY-123'},
                             content_type='application/json')
        
        print(f"Status: {response.status_code}")
        print(f"Headers: {dict(response.headers)}")
        print(f"Data: {response.data}")
        print(f"Text: {response.get_data(as_text=True)}")
        
        try:
            json_data = response.get_json()
            print(f"JSON: {json_data}")
        except Exception as e:
            print(f"JSON Error: {e}")
        
        print("\n" + "=" * 50)

if __name__ == "__main__":
    debug_api()
