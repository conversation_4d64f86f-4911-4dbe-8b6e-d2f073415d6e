"""
Tests for dashboard functionality
"""

import pytest
import json
from app import query_db, execute_db

class TestDashboard:
    """Test dashboard main page."""
    
    def test_dashboard_requires_login(self, client):
        """Test that dashboard requires authentication."""
        response = client.get('/dashboard/')
        assert response.status_code == 302
        assert '/auth/login' in response.location
    
    def test_dashboard_loads_for_authenticated_user(self, client, auth, regular_user):
        """Test that dashboard loads for authenticated user."""
        auth.login(regular_user['username'], regular_user['password'])
        response = client.get('/dashboard/')
        assert response.status_code == 200
        assert b'Dashboard' in response.data
        assert regular_user['username'].encode() in response.data
    
    def test_dashboard_shows_user_stats(self, client, auth, regular_user, test_app):
        """Test that dashboard shows correct user statistics."""
        auth.login(regular_user['username'], regular_user['password'])
        response = client.get('/dashboard/')
        assert response.status_code == 200
        
        # Should show 1 app (test_app)
        assert b'1' in response.data  # Total apps count
        assert test_app['name'].encode() in response.data

class TestAppCreation:
    """Test app creation functionality."""
    
    def test_create_app_page_loads(self, client, auth, regular_user):
        """Test that create app page loads."""
        auth.login(regular_user['username'], regular_user['password'])
        response = client.get('/dashboard/create-app')
        assert response.status_code == 200
        assert b'Create App' in response.data
    
    def test_create_app_successful(self, client, auth, regular_user, app):
        """Test successful app creation."""
        auth.login(regular_user['username'], regular_user['password'])
        
        response = client.post('/dashboard/create-app', data={
            'name': 'NewTestApp',
            'hwid_lock': False,
            'expiry_days': 60
        })
        
        # Should redirect to app detail page
        assert response.status_code == 302
        assert '/dashboard/app/' in response.location
        
        # Verify app was created in database
        with app.app_context():
            app_data = query_db(
                'SELECT * FROM app WHERE name = ? AND owner_id = ?',
                ('NewTestApp', regular_user['id']),
                one=True
            )
            assert app_data is not None
            assert app_data['expiry_days'] == 60
            assert app_data['hwid_lock'] == 0
    
    def test_create_app_duplicate_name(self, client, auth, regular_user, test_app):
        """Test creating app with duplicate name."""
        auth.login(regular_user['username'], regular_user['password'])
        
        response = client.post('/dashboard/create-app', data={
            'name': test_app['name'],
            'hwid_lock': False,
            'expiry_days': 30
        })
        
        assert response.status_code == 200
        assert b'already have an app with this name' in response.data
    
    def test_create_app_over_plan_limit(self, client, auth, regular_user, test_app):
        """Test creating app when over plan limit."""
        auth.login(regular_user['username'], regular_user['password'])
        
        # Regular user has Free plan (1 app max) and already has test_app
        response = client.get('/dashboard/create-app')
        assert response.status_code == 302  # Should redirect
        
        # Follow redirect
        response = client.get('/dashboard/create-app', follow_redirects=True)
        assert b'reached your app limit' in response.data

class TestAppDetail:
    """Test app detail page functionality."""
    
    def test_app_detail_loads(self, client, auth, regular_user, test_app):
        """Test that app detail page loads."""
        auth.login(regular_user['username'], regular_user['password'])
        response = client.get(f'/dashboard/app/{test_app["id"]}')
        assert response.status_code == 200
        assert test_app['name'].encode() in response.data
        assert test_app['secret'].encode() in response.data
    
    def test_app_detail_wrong_owner(self, client, auth, admin_user, test_app):
        """Test that users can't access other users' apps."""
        auth.login(admin_user['username'], admin_user['password'])
        response = client.get(f'/dashboard/app/{test_app["id"]}')
        assert response.status_code == 302  # Should redirect
        
        # Follow redirect
        response = client.get(f'/dashboard/app/{test_app["id"]}', follow_redirects=True)
        assert b'App not found' in response.data
    
    def test_app_detail_nonexistent(self, client, auth, regular_user):
        """Test accessing non-existent app."""
        auth.login(regular_user['username'], regular_user['password'])
        response = client.get('/dashboard/app/99999')
        assert response.status_code == 302
        
        response = client.get('/dashboard/app/99999', follow_redirects=True)
        assert b'App not found' in response.data

class TestKeyGeneration:
    """Test license key generation functionality."""
    
    def test_generate_keys_page_loads(self, client, auth, regular_user, test_app):
        """Test that generate keys page loads."""
        auth.login(regular_user['username'], regular_user['password'])
        response = client.get(f'/dashboard/app/{test_app["id"]}/generate-keys')
        assert response.status_code == 200
        assert b'Generate Keys' in response.data
    
    def test_generate_single_key(self, client, auth, regular_user, test_app, app):
        """Test generating a single license key."""
        auth.login(regular_user['username'], regular_user['password'])
        
        response = client.post(f'/dashboard/app/{test_app["id"]}/generate-keys', data={
            'quantity': 1,
            'note': 'Test key'
        })
        
        assert response.status_code == 200
        assert b'Successfully generated 1 license key' in response.data
        
        # Verify key was created in database
        with app.app_context():
            keys = query_db(
                'SELECT * FROM license_key WHERE app_id = ?',
                (test_app['id'],)
            )
            assert len(keys) == 2  # Original test_license_key + new one
    
    def test_generate_multiple_keys(self, client, auth, regular_user, test_app, app):
        """Test generating multiple license keys."""
        auth.login(regular_user['username'], regular_user['password'])
        
        response = client.post(f'/dashboard/app/{test_app["id"]}/generate-keys', data={
            'quantity': 5,
            'note': 'Bulk test keys'
        })
        
        assert response.status_code == 200
        assert b'Successfully generated 5 license key' in response.data
        
        # Verify keys were created in database
        with app.app_context():
            keys = query_db(
                'SELECT * FROM license_key WHERE app_id = ?',
                (test_app['id'],)
            )
            assert len(keys) == 6  # Original test_license_key + 5 new ones
    
    def test_generate_keys_over_limit(self, client, auth, regular_user, test_app, app):
        """Test generating keys over plan limit."""
        # Create keys up to the limit (Free plan = 50 keys)
        with app.app_context():
            import uuid
            for i in range(49):  # 49 + existing test_license_key = 50 (limit)
                key = str(uuid.uuid4())
                execute_db(
                    'INSERT INTO license_key (key, app_id) VALUES (?, ?)',
                    (key, test_app['id'])
                )
        
        auth.login(regular_user['username'], regular_user['password'])
        
        # Try to generate one more key (should fail)
        response = client.post(f'/dashboard/app/{test_app["id"]}/generate-keys', data={
            'quantity': 1,
            'note': 'Over limit key'
        })
        
        assert response.status_code == 302
        response = client.post(f'/dashboard/app/{test_app["id"]}/generate-keys', 
                             data={'quantity': 1, 'note': 'Over limit key'}, 
                             follow_redirects=True)
        assert b'reached your license key limit' in response.data

class TestAppEdit:
    """Test app editing functionality."""
    
    def test_edit_app_page_loads(self, client, auth, regular_user, test_app):
        """Test that edit app page loads."""
        auth.login(regular_user['username'], regular_user['password'])
        response = client.get(f'/dashboard/app/{test_app["id"]}/edit')
        assert response.status_code == 200
        assert b'Edit App' in response.data
        assert test_app['name'].encode() in response.data
    
    def test_edit_app_successful(self, client, auth, regular_user, test_app, app):
        """Test successful app editing."""
        auth.login(regular_user['username'], regular_user['password'])
        
        response = client.post(f'/dashboard/app/{test_app["id"]}/edit', data={
            'name': 'UpdatedTestApp',
            'hwid_lock': True,
            'expiry_days': 90
        })
        
        assert response.status_code == 302
        assert f'/dashboard/app/{test_app["id"]}' in response.location
        
        # Verify app was updated in database
        with app.app_context():
            app_data = query_db(
                'SELECT * FROM app WHERE id = ?',
                (test_app['id'],),
                one=True
            )
            assert app_data['name'] == 'UpdatedTestApp'
            assert app_data['hwid_lock'] == 1
            assert app_data['expiry_days'] == 90

class TestAppDeletion:
    """Test app deletion functionality."""
    
    def test_delete_app(self, client, auth, regular_user, test_app, test_license_key, app):
        """Test app deletion."""
        auth.login(regular_user['username'], regular_user['password'])
        
        response = client.post(f'/dashboard/app/{test_app["id"]}/delete')
        assert response.status_code == 302
        assert '/dashboard' in response.location
        
        # Verify app and its keys were deleted
        with app.app_context():
            app_data = query_db(
                'SELECT * FROM app WHERE id = ?',
                (test_app['id'],),
                one=True
            )
            assert app_data is None
            
            key_data = query_db(
                'SELECT * FROM license_key WHERE id = ?',
                (test_license_key['id'],),
                one=True
            )
            assert key_data is None

class TestKeyDeletion:
    """Test license key deletion functionality."""
    
    def test_delete_key(self, client, auth, regular_user, test_app, test_license_key, app):
        """Test license key deletion."""
        auth.login(regular_user['username'], regular_user['password'])
        
        response = client.post(f'/dashboard/key/{test_license_key["id"]}/delete')
        assert response.status_code == 302
        assert f'/dashboard/app/{test_app["id"]}' in response.location
        
        # Verify key was deleted
        with app.app_context():
            key_data = query_db(
                'SELECT * FROM license_key WHERE id = ?',
                (test_license_key['id'],),
                one=True
            )
            assert key_data is None

class TestDashboardAPI:
    """Test dashboard API endpoints."""
    
    def test_api_stats(self, client, auth, regular_user, test_app, test_license_key):
        """Test dashboard stats API endpoint."""
        auth.login(regular_user['username'], regular_user['password'])
        
        response = client.get('/dashboard/api/stats')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert data['apps'] == 1
        assert data['total_keys'] == 1
        assert data['used_keys'] == 0
        assert data['unused_keys'] == 1
