{% extends "base.html" %}

{% block title %}{{ app.name }} - PepeAuth{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center space-x-4 mb-4">
            <a href="{{ url_for('dashboard.index') }}" class="text-gray-400 hover:text-white">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
            </a>
            <h1 class="text-3xl font-bold text-white">{{ app.name }}</h1>
            <div class="flex items-center space-x-2">
                {% if app.hwid_lock %}
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-500/20 text-yellow-400">
                        HWID Lock
                    </span>
                {% endif %}
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-pepe-green/20 text-pepe-green">
                    Active
                </span>
            </div>
        </div>
        
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <p class="text-gray-300">Manage your app settings and license keys</p>
            <div class="mt-4 sm:mt-0 flex space-x-3">
                <a href="{{ url_for('dashboard.generate_keys', app_id=app.id) }}" class="btn-primary">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Generate Keys
                </a>
                <a href="{{ url_for('dashboard.edit_app', app_id=app.id) }}" class="btn-secondary">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                    </svg>
                    Edit App
                </a>
            </div>
        </div>
    </div>
    
    <!-- App Info & Stats -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
        <!-- App Details -->
        <div class="lg:col-span-2">
            <div class="glass-card p-6 rounded-xl">
                <h3 class="text-lg font-semibold text-white mb-4">App Details</h3>
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-1">App Secret</label>
                        <div class="flex items-center space-x-2">
                            <input type="text" value="{{ app.secret }}" readonly 
                                   class="input-field font-mono text-sm flex-1" id="app-secret">
                            <button onclick="copyToClipboard('app-secret')" 
                                    class="btn-secondary px-3 py-2">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                </svg>
                            </button>
                        </div>
                        <p class="text-xs text-gray-400 mt-1">Keep this secret safe. It's used for API authentication.</p>
                    </div>
                    
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-1">HWID Lock</label>
                            <p class="text-white">{{ 'Enabled' if app.hwid_lock else 'Disabled' }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-1">License Expiry</label>
                            <p class="text-white">{{ app.expiry_days }} days</p>
                        </div>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-1">Created</label>
                        <p class="text-white">{{ app.created_at }}</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Stats -->
        <div class="space-y-6">
            <div class="glass-card p-6 rounded-xl">
                <h3 class="text-lg font-semibold text-white mb-4">Statistics</h3>
                <div class="space-y-4">
                    <div class="flex justify-between items-center">
                        <span class="text-gray-300">Total Keys</span>
                        <span class="text-2xl font-bold text-white">{{ key_stats.total }}</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-gray-300">Used Keys</span>
                        <span class="text-2xl font-bold text-pepe-green">{{ key_stats.used }}</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-gray-300">Unused Keys</span>
                        <span class="text-2xl font-bold text-blue-400">{{ key_stats.unused }}</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-gray-300">Expired Keys</span>
                        <span class="text-2xl font-bold text-red-400">{{ key_stats.expired }}</span>
                    </div>
                </div>
            </div>
            
            <!-- Usage Chart -->
            <div class="glass-card p-6 rounded-xl">
                <h3 class="text-lg font-semibold text-white mb-4">Usage Overview</h3>
                <div class="space-y-3">
                    <div>
                        <div class="flex justify-between text-sm mb-1">
                            <span class="text-gray-300">Used</span>
                            <span class="text-gray-300">{{ key_stats.used }}/{{ key_stats.total }}</span>
                        </div>
                        <div class="w-full bg-pepe-gray rounded-full h-2">
                            {% set usage_percentage = (key_stats.used / key_stats.total * 100) if key_stats.total > 0 else 0 %}
                            <div class="bg-pepe-green h-2 rounded-full" style="width: {{ usage_percentage }}%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- License Keys Table -->
    <div class="glass-card p-6 rounded-xl">
        <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold text-white">License Keys</h3>
            <div class="flex items-center space-x-4">
                <span class="text-sm text-gray-300">{{ keys|length }} key{{ 's' if keys|length != 1 else '' }}</span>
                <div class="flex space-x-2">
                    <button class="btn-secondary text-sm">Filter</button>
                    <button class="btn-secondary text-sm">Export</button>
                </div>
            </div>
        </div>
        
        {% if keys %}
            <div class="overflow-x-auto">
                <table class="min-w-full">
                    <thead>
                        <tr class="border-b border-pepe-light-gray/20">
                            <th class="text-left py-3 px-4 text-sm font-medium text-gray-300">License Key</th>
                            <th class="text-left py-3 px-4 text-sm font-medium text-gray-300">Status</th>
                            <th class="text-left py-3 px-4 text-sm font-medium text-gray-300">Created</th>
                            <th class="text-left py-3 px-4 text-sm font-medium text-gray-300">Used At</th>
                            <th class="text-left py-3 px-4 text-sm font-medium text-gray-300">Expires</th>
                            <th class="text-left py-3 px-4 text-sm font-medium text-gray-300">HWID</th>
                            <th class="text-right py-3 px-4 text-sm font-medium text-gray-300">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-pepe-light-gray/20">
                        {% for key in keys %}
                            <tr class="hover:bg-pepe-light-gray/10">
                                <td class="py-3 px-4">
                                    <div class="flex items-center space-x-2">
                                        <code class="text-sm text-gray-300 font-mono">{{ key.key[:8] }}...{{ key.key[-8:] }}</code>
                                        <button onclick="copyToClipboard('key-{{ loop.index }}')" 
                                                class="text-gray-400 hover:text-white">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                            </svg>
                                        </button>
                                        <input type="hidden" id="key-{{ loop.index }}" value="{{ key.key }}">
                                    </div>
                                </td>
                                <td class="py-3 px-4">
                                    {% if key.used %}
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-pepe-green/20 text-pepe-green">
                                            Used
                                        </span>
                                    {% else %}
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-500/20 text-blue-400">
                                            Unused
                                        </span>
                                    {% endif %}
                                </td>
                                <td class="py-3 px-4 text-sm text-gray-300">{{ key.created_at }}</td>
                                <td class="py-3 px-4 text-sm text-gray-300">{{ key.used_at or '-' }}</td>
                                <td class="py-3 px-4 text-sm text-gray-300">{{ key.expires_at or '-' }}</td>
                                <td class="py-3 px-4 text-sm text-gray-300 font-mono">{{ key.hwid or '-' }}</td>
                                <td class="py-3 px-4 text-right">
                                    <form method="POST" action="{{ url_for('dashboard.delete_key', key_id=key.id) }}" 
                                          class="inline" onsubmit="return confirm('Are you sure you want to delete this key?')">
                                        <button type="submit" class="text-red-400 hover:text-red-300">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                            </svg>
                                        </button>
                                    </form>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="text-center py-12">
                <div class="w-16 h-16 bg-pepe-gray rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m0 0a2 2 0 012 2m-2-2a2 2 0 00-2 2m2-2V5a2 2 0 00-2-2m0 0H9a2 2 0 00-2 2v0a2 2 0 002 2h2m-4 4v6a2 2 0 002 2h2a2 2 0 002-2v-6m-4 0a2 2 0 012-2h0a2 2 0 012 2v0"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-medium text-white mb-2">No license keys yet</h3>
                <p class="text-gray-300 mb-6">Generate your first license keys to get started.</p>
                <a href="{{ url_for('dashboard.generate_keys', app_id=app.id) }}" class="btn-primary">
                    Generate License Keys
                </a>
            </div>
        {% endif %}
    </div>
    
    <!-- Danger Zone -->
    <div class="glass-card p-6 rounded-xl border border-red-500/20 mt-8">
        <h3 class="text-lg font-semibold text-red-400 mb-4">Danger Zone</h3>
        <div class="flex items-center justify-between">
            <div>
                <h4 class="text-white font-medium">Delete App</h4>
                <p class="text-gray-300 text-sm">Permanently delete this app and all its license keys. This action cannot be undone.</p>
            </div>
            <form method="POST" action="{{ url_for('dashboard.delete_app', app_id=app.id) }}" 
                  onsubmit="return confirm('Are you sure you want to delete this app? This will also delete all license keys and cannot be undone.')">
                <button type="submit" class="btn-danger">Delete App</button>
            </form>
        </div>
    </div>
</div>

<script>
function copyToClipboard(elementId) {
    const element = document.getElementById(elementId);
    element.select();
    element.setSelectionRange(0, 99999);
    document.execCommand('copy');
    
    // Show feedback
    const button = event.target.closest('button');
    const originalHTML = button.innerHTML;
    button.innerHTML = '<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>';
    setTimeout(() => {
        button.innerHTML = originalHTML;
    }, 1000);
}
</script>
{% endblock %}
