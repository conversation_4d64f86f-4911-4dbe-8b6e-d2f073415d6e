/* PepeAuth Custom Styles - Modern Glassmorphism Theme */

/* Glass Card Effect */
.glass-card {
    background: rgba(17, 24, 39, 0.5);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

/* Navigation Links */
.nav-link {
    @apply text-gray-300 hover:text-white px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-200;
}

.nav-link.active {
    @apply text-white bg-pepe-green/20;
}

/* Button Styles */
.btn-primary {
    @apply inline-flex items-center justify-center px-6 py-2 border border-transparent text-sm font-medium rounded-full text-white bg-pepe-green hover:bg-pepe-green/80 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-pepe-green transition-all duration-200 hover:scale-105;
}

.btn-secondary {
    @apply inline-flex items-center justify-center px-6 py-2 border border-pepe-light-gray text-sm font-medium rounded-full text-white bg-transparent hover:bg-pepe-light-gray/20 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-pepe-green transition-all duration-200 hover:scale-105;
}

.btn-danger {
    @apply inline-flex items-center justify-center px-6 py-2 border border-transparent text-sm font-medium rounded-full text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-all duration-200 hover:scale-105;
}

/* Input Fields */
.input-field {
    @apply block w-full px-4 py-3 border border-pepe-light-gray rounded-lg bg-pepe-gray/50 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-pepe-green focus:border-transparent transition-all duration-200;
}

.input-field:focus {
    background: rgba(31, 41, 55, 0.8);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

/* Form Elements */
.form-group {
    @apply mb-6;
}

.form-label {
    @apply block text-sm font-medium text-white mb-2;
}

.form-error {
    @apply mt-1 text-sm text-red-400;
}

/* Card Hover Effects */
.card-hover {
    @apply transition-all duration-300 hover:scale-105 hover:shadow-2xl;
}

.card-hover:hover {
    background: rgba(17, 24, 39, 0.7);
    border-color: rgba(46, 204, 113, 0.3);
}

/* Status Badges */
.badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.badge-success {
    @apply bg-pepe-green/20 text-pepe-green;
}

.badge-warning {
    @apply bg-yellow-500/20 text-yellow-400;
}

.badge-error {
    @apply bg-red-500/20 text-red-400;
}

.badge-info {
    @apply bg-blue-500/20 text-blue-400;
}

/* Loading Spinner */
.spinner {
    @apply inline-block w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin;
}

/* Progress Bars */
.progress-bar {
    @apply w-full bg-pepe-gray rounded-full h-2 overflow-hidden;
}

.progress-fill {
    @apply h-full bg-gradient-to-r from-pepe-green to-pepe-green/80 rounded-full transition-all duration-500;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes pulse-green {
    0%, 100% {
        box-shadow: 0 0 0 0 rgba(46, 204, 113, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(46, 204, 113, 0);
    }
}

.pulse-green {
    animation: pulse-green 2s infinite;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(31, 41, 55, 0.5);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: rgba(46, 204, 113, 0.5);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(46, 204, 113, 0.7);
}

/* Table Styles */
.table-glass {
    @apply w-full;
}

.table-glass th {
    @apply text-left py-3 px-4 text-sm font-medium text-gray-300 border-b border-pepe-light-gray/20;
}

.table-glass td {
    @apply py-3 px-4 text-sm text-gray-300 border-b border-pepe-light-gray/10;
}

.table-glass tr:hover {
    background: rgba(46, 204, 113, 0.05);
}

/* Code Blocks */
.code-block {
    @apply bg-pepe-darker rounded-lg p-4 font-mono text-sm text-gray-300 overflow-x-auto;
    background: rgba(15, 23, 42, 0.8);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.05);
}

/* Tooltip */
.tooltip {
    @apply relative;
}

.tooltip:hover::after {
    content: attr(data-tooltip);
    @apply absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 text-xs text-white bg-pepe-darker rounded shadow-lg whitespace-nowrap z-50;
}

/* Mobile Responsive Adjustments */
@media (max-width: 768px) {
    .glass-card {
        backdrop-filter: blur(5px);
        -webkit-backdrop-filter: blur(5px);
    }
    
    .btn-primary,
    .btn-secondary,
    .btn-danger {
        @apply text-xs px-4 py-2;
    }
    
    .nav-link {
        @apply text-xs px-2 py-1;
    }
}

/* Focus States */
.focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-pepe-green focus:ring-offset-2 focus:ring-offset-pepe-dark;
}

/* Selection */
::selection {
    background: rgba(46, 204, 113, 0.3);
    color: white;
}

::-moz-selection {
    background: rgba(46, 204, 113, 0.3);
    color: white;
}

/* Custom Checkbox/Radio */
input[type="checkbox"]:checked,
input[type="radio"]:checked {
    background-color: #2ecc71;
    border-color: #2ecc71;
}

/* Notification Styles */
.notification {
    @apply fixed top-4 right-4 z-50 max-w-sm;
}

.notification-enter {
    @apply transform translate-x-full opacity-0;
}

.notification-enter-active {
    @apply transform translate-x-0 opacity-100 transition-all duration-300;
}

.notification-exit {
    @apply transform translate-x-0 opacity-100;
}

.notification-exit-active {
    @apply transform translate-x-full opacity-0 transition-all duration-300;
}

/* Pepe Theme Specific */
.pepe-gradient {
    background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
}

.pepe-gradient-text {
    background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Dark mode enhancements */
@media (prefers-color-scheme: dark) {
    .glass-card {
        background: rgba(17, 24, 39, 0.6);
    }
}

/* Print styles */
@media print {
    .glass-card {
        background: white !important;
        color: black !important;
        border: 1px solid #ccc !important;
        backdrop-filter: none !important;
        -webkit-backdrop-filter: none !important;
    }
}
