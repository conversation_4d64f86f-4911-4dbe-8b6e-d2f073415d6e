from flask_wtf import <PERSON><PERSON>kForm
from wtforms import <PERSON>F<PERSON>, PasswordField, BooleanField, IntegerField, SelectField, TextAreaField
from wtforms.validators import <PERSON>Required, Email, Length, EqualTo, NumberRange, Optional
from wtforms.widgets import TextArea

class LoginForm(FlaskForm):
    """Login form"""
    username = StringField('Username or Email', validators=[
        DataRequired(message='Username or email is required.'),
        Length(min=3, max=50, message='Username must be between 3 and 50 characters.')
    ])
    password = PasswordField('Password', validators=[
        DataRequired(message='Password is required.')
    ])
    remember_me = BooleanField('Remember Me')

class RegisterForm(FlaskForm):
    """Registration form"""
    email = StringField('Email', validators=[
        DataRequired(message='Email is required.'),
        Email(message='Please enter a valid email address.'),
        Length(max=120, message='Email must be less than 120 characters.')
    ])
    username = <PERSON><PERSON>ield('Username', validators=[
        DataRequired(message='Username is required.'),
        Length(min=3, max=30, message='Username must be between 3 and 30 characters.')
    ])
    password = PasswordField('Password', validators=[
        DataRequired(message='Password is required.'),
        Length(min=6, max=128, message='Password must be between 6 and 128 characters.')
    ])
    confirm_password = PasswordField('Confirm Password', validators=[
        DataRequired(message='Please confirm your password.'),
        EqualTo('password', message='Passwords must match.')
    ])

class AppForm(FlaskForm):
    """App creation/edit form"""
    name = StringField('App Name', validators=[
        DataRequired(message='App name is required.'),
        Length(min=3, max=50, message='App name must be between 3 and 50 characters.')
    ])
    hwid_lock = BooleanField('Enable HWID Lock', 
        description='Bind license keys to specific hardware IDs')
    expiry_days = IntegerField('License Expiry (Days)', validators=[
        DataRequired(message='Expiry days is required.'),
        NumberRange(min=1, max=3650, message='Expiry must be between 1 and 3650 days.')
    ], default=30)

class KeyGenerationForm(FlaskForm):
    """License key generation form"""
    quantity = IntegerField('Number of Keys', validators=[
        DataRequired(message='Quantity is required.'),
        NumberRange(min=1, max=100, message='You can generate 1-100 keys at once.')
    ], default=1)
    note = TextAreaField('Note (Optional)', validators=[
        Optional(),
        Length(max=200, message='Note must be less than 200 characters.')
    ], description='Optional note for these keys')

class AdminUserForm(FlaskForm):
    """Admin form for managing users"""
    plan_id = SelectField('Plan', coerce=int, validators=[
        DataRequired(message='Plan is required.')
    ])
    is_admin = BooleanField('Admin User')

class AdminPlanForm(FlaskForm):
    """Admin form for managing plans"""
    name = StringField('Plan Name', validators=[
        DataRequired(message='Plan name is required.'),
        Length(min=2, max=30, message='Plan name must be between 2 and 30 characters.')
    ])
    max_apps = IntegerField('Max Apps', validators=[
        DataRequired(message='Max apps is required.')
    ], description='Use -1 for unlimited')
    max_keys = IntegerField('Max Keys', validators=[
        DataRequired(message='Max keys is required.')
    ], description='Use -1 for unlimited')
    rate_limit = IntegerField('Rate Limit (per day)', validators=[
        DataRequired(message='Rate limit is required.')
    ], description='Use -1 for unlimited')

class SearchForm(FlaskForm):
    """Search form for admin panel"""
    query = StringField('Search', validators=[
        Optional(),
        Length(max=100, message='Search query must be less than 100 characters.')
    ])
    filter_type = SelectField('Filter', choices=[
        ('all', 'All'),
        ('users', 'Users'),
        ('apps', 'Apps'),
        ('keys', 'Keys')
    ], default='all')

class BulkActionForm(FlaskForm):
    """Bulk action form for admin operations"""
    action = SelectField('Action', choices=[
        ('', 'Select Action'),
        ('delete', 'Delete'),
        ('activate', 'Activate'),
        ('deactivate', 'Deactivate')
    ], validators=[
        DataRequired(message='Please select an action.')
    ])
    selected_items = StringField('Selected Items', validators=[
        DataRequired(message='Please select items to perform action on.')
    ])

class APITestForm(FlaskForm):
    """Form for testing API endpoints"""
    app_name = StringField('App Name', validators=[
        DataRequired(message='App name is required.')
    ])
    license_key = StringField('License Key', validators=[
        DataRequired(message='License key is required.')
    ])
    hwid = StringField('Hardware ID', validators=[
        Optional(),
        Length(max=100, message='HWID must be less than 100 characters.')
    ])

class PasswordChangeForm(FlaskForm):
    """Password change form"""
    current_password = PasswordField('Current Password', validators=[
        DataRequired(message='Current password is required.')
    ])
    new_password = PasswordField('New Password', validators=[
        DataRequired(message='New password is required.'),
        Length(min=6, max=128, message='Password must be between 6 and 128 characters.')
    ])
    confirm_new_password = PasswordField('Confirm New Password', validators=[
        DataRequired(message='Please confirm your new password.'),
        EqualTo('new_password', message='Passwords must match.')
    ])
