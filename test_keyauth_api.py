#!/usr/bin/env python3
"""
Test script for KeyAuth-like API endpoints
Tests the /api/v1/login_key, /api/v1/user_info, and admin endpoints
"""

import requests
import json
import sys

BASE_URL = "http://127.0.0.1:5000"

def test_login_key_endpoint():
    """Test the /api/v1/login_key endpoint"""
    print("🔑 Testing /api/v1/login_key endpoint...")
    
    # Test with invalid key
    response = requests.post(f"{BASE_URL}/api/v1/login_key", 
                           json={"key": "INVALID-KEY-123"})
    
    print(f"Invalid key test: {response.status_code}")

    try:
        response_data = response.json()
        print(f"Response: {response_data}")
    except:
        print(f"Response text: {response.text}")

    if response.status_code == 403:
        print("✅ Invalid key properly rejected")
    else:
        print("❌ Invalid key test failed")

    return response.status_code == 403

def test_user_info_endpoint():
    """Test the /api/v1/user_info endpoint"""
    print("\n👤 Testing /api/v1/user_info endpoint...")
    
    # Test with invalid session
    response = requests.post(f"{BASE_URL}/api/v1/user_info", 
                           json={"session_token": "invalid", "key": "invalid"})
    
    print(f"Invalid session test: {response.status_code}")

    try:
        response_data = response.json()
        print(f"Response: {response_data}")
    except:
        print(f"Response text: {response.text}")

    if response.status_code == 403:
        print("✅ Invalid session properly rejected")
    else:
        print("❌ Invalid session test failed")

    return response.status_code == 403

def test_admin_endpoints():
    """Test admin endpoints (should require authentication)"""
    print("\n👑 Testing admin endpoints...")
    
    # Test create_key without authentication
    response = requests.post(f"{BASE_URL}/api/v1/admin/create_key", 
                           json={"count": 1, "plan": "Premium"})
    
    print(f"Unauthenticated admin test: {response.status_code}")

    try:
        response_data = response.json()
        print(f"Response: {response_data}")
    except:
        print(f"Response text: {response.text}")

    if response.status_code == 401:
        print("✅ Admin endpoint properly protected")
    else:
        print("❌ Admin endpoint security test failed")

    return response.status_code == 401

def test_server_running():
    """Test if server is running"""
    print("🚀 Testing if server is running...")
    
    try:
        response = requests.get(f"{BASE_URL}/", timeout=5)
        print(f"Server status: {response.status_code}")
        if response.status_code == 200:
            print("✅ Server is running")
            return True
        else:
            print("❌ Server returned unexpected status")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Server is not running: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 KeyAuth API Testing Suite")
    print("=" * 50)
    
    # Test if server is running
    if not test_server_running():
        print("\n❌ Server is not running. Please start the server first:")
        print("   python run.py")
        sys.exit(1)
    
    # Run API tests
    tests_passed = 0
    total_tests = 3
    
    if test_login_key_endpoint():
        tests_passed += 1
    
    if test_user_info_endpoint():
        tests_passed += 1
    
    if test_admin_endpoints():
        tests_passed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("🎉 All API endpoints are working correctly!")
        return True
    else:
        print("⚠️  Some tests failed. Check the output above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
