{% extends "base.html" %}

{% block title %}Manage Plans - Admin - PepeAuth{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <a href="{{ url_for('admin.index') }}" class="text-gray-400 hover:text-white">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                    </svg>
                </a>
                <h1 class="text-3xl font-bold text-white">Manage Plans</h1>
            </div>
            <a href="{{ url_for('admin.create_plan') }}" class="btn-primary">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                Create Plan
            </a>
        </div>
        <p class="text-gray-300 mt-1">Configure subscription plans and limits</p>
    </div>
    
    <!-- Plans Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {% for plan in plans %}
            <div class="glass-card p-6 rounded-xl">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-xl font-bold text-white">{{ plan.name }}</h3>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-pepe-green/20 text-pepe-green">
                        {{ plan.user_count }} user{{ 's' if plan.user_count != 1 else '' }}
                    </span>
                </div>
                
                <div class="space-y-3 mb-6">
                    <div class="flex justify-between">
                        <span class="text-gray-300">Max Apps:</span>
                        <span class="text-white">{{ plan.max_apps if plan.max_apps != -1 else 'Unlimited' }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-300">Max Keys:</span>
                        <span class="text-white">{{ plan.max_keys if plan.max_keys != -1 else 'Unlimited' }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-300">Rate Limit:</span>
                        <span class="text-white">{{ plan.rate_limit if plan.rate_limit != -1 else 'Unlimited' }}/day</span>
                    </div>
                </div>
                
                <div class="flex space-x-2">
                    <a href="{{ url_for('admin.edit_plan', plan_id=plan.id) }}" 
                       class="btn-secondary flex-1 text-center text-sm">
                        Edit
                    </a>
                    {% if plan.id > 3 %}
                        <form method="POST" action="{{ url_for('admin.delete_plan', plan_id=plan.id) }}" 
                              class="flex-1" onsubmit="return confirm('Are you sure you want to delete this plan?')">
                            <button type="submit" class="btn-danger w-full text-sm">Delete</button>
                        </form>
                    {% endif %}
                </div>
            </div>
        {% endfor %}
    </div>
    
    {% if not plans %}
        <div class="text-center py-12">
            <div class="w-16 h-16 bg-pepe-gray rounded-full flex items-center justify-center mx-auto mb-4">
                <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
            </div>
            <h3 class="text-lg font-medium text-white mb-2">No plans found</h3>
            <p class="text-gray-300 mb-6">Create your first subscription plan to get started.</p>
            <a href="{{ url_for('admin.create_plan') }}" class="btn-primary">
                Create Plan
            </a>
        </div>
    {% endif %}
</div>
{% endblock %}
