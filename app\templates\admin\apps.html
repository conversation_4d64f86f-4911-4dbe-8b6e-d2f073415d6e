{% extends "base.html" %}

{% block title %}App Management - Admin - PepeAuth{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div>
                <h1 class="text-3xl font-bold text-white">Application Management</h1>
                <p class="mt-1 text-gray-300">Manage all user applications and settings</p>
            </div>
            <div class="mt-4 sm:mt-0 flex space-x-3">
                <button onclick="exportApps()" class="btn-secondary">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    Export
                </button>
                <button onclick="bulkSecretRegen()" class="btn-danger">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    Bulk Regen Secrets
                </button>
            </div>
        </div>
    </div>
    
    <!-- Stats Overview -->
    <div class="grid grid-cols-1 md:grid-cols-5 gap-6 mb-8">
        <div class="glass-card p-6 rounded-xl">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-blue-500/20 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-300">Total Apps</p>
                    <p class="text-2xl font-semibold text-white">{{ stats.total_apps }}</p>
                </div>
            </div>
        </div>
        
        <div class="glass-card p-6 rounded-xl">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-green-500/20 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-300">Active</p>
                    <p class="text-2xl font-semibold text-white">{{ stats.active_apps }}</p>
                </div>
            </div>
        </div>
        
        <div class="glass-card p-6 rounded-xl">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-yellow-500/20 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-300">With Webhooks</p>
                    <p class="text-2xl font-semibold text-white">{{ stats.webhook_apps }}</p>
                </div>
            </div>
        </div>
        
        <div class="glass-card p-6 rounded-xl">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-purple-500/20 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m0 0a2 2 0 012 2m-2-2a2 2 0 00-2 2m2-2V5a2 2 0 00-2-2m0 0H9a2 2 0 00-2 2v0a2 2 0 002 2h2m-4 4v6a2 2 0 002 2h2a2 2 0 002-2v-6m-4 0a2 2 0 012-2h0a2 2 0 012 2v0"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-300">Total Keys</p>
                    <p class="text-2xl font-semibold text-white">{{ stats.total_keys }}</p>
                </div>
            </div>
        </div>
        
        <div class="glass-card p-6 rounded-xl">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-red-500/20 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-300">Inactive</p>
                    <p class="text-2xl font-semibold text-white">{{ stats.inactive_apps }}</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Filters and Search -->
    <div class="glass-card p-6 rounded-xl mb-8">
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            <div class="flex-1 max-w-lg">
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </div>
                    <input type="text" 
                           class="input-field pl-10" 
                           placeholder="Search applications..."
                           id="search-input">
                </div>
            </div>
            <div class="flex flex-wrap items-center gap-4">
                <select class="input-field w-auto" id="owner-filter">
                    <option value="">All Owners</option>
                    {% for user in users %}
                        <option value="{{ user.id }}">{{ user.username }}</option>
                    {% endfor %}
                </select>
                <select class="input-field w-auto" id="status-filter">
                    <option value="">All Status</option>
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                    <option value="webhook">Has Webhook</option>
                    <option value="hwid_lock">HWID Lock</option>
                </select>
                <select class="input-field w-auto" id="sort-filter">
                    <option value="created_desc">Newest First</option>
                    <option value="created_asc">Oldest First</option>
                    <option value="name_asc">Name A-Z</option>
                    <option value="name_desc">Name Z-A</option>
                    <option value="keys_desc">Most Keys</option>
                </select>
                <button onclick="resetFilters()" class="btn-secondary text-sm">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    Reset
                </button>
            </div>
        </div>
    </div>
    
    <!-- Apps Table -->
    <div class="glass-card rounded-xl overflow-hidden">
        <div class="overflow-x-auto">
            <table class="table-glass" id="apps-table">
                <thead>
                    <tr>
                        <th class="w-4">
                            <input type="checkbox" id="select-all" class="w-4 h-4 text-pepe-green bg-pepe-gray border-pepe-light-gray rounded focus:ring-pepe-green focus:ring-2">
                        </th>
                        <th>Application</th>
                        <th>Owner</th>
                        <th>Status</th>
                        <th>Keys</th>
                        <th>Success Rate</th>
                        <th>Last Activity</th>
                        <th>Created</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for app in apps %}
                        <tr class="app-row" 
                            data-owner="{{ app.owner_id }}" 
                            data-status="{{ 'active' if app.is_active else 'inactive' }}"
                            data-name="{{ app.name.lower() }}"
                            data-webhook="{{ 'true' if app.webhook_url else 'false' }}"
                            data-hwid="{{ 'true' if app.hwid_lock else 'false' }}">
                            <td>
                                <input type="checkbox" class="app-checkbox w-4 h-4 text-pepe-green bg-pepe-gray border-pepe-light-gray rounded focus:ring-pepe-green focus:ring-2" value="{{ app.id }}">
                            </td>
                            <td>
                                <div class="flex items-center space-x-3">
                                    <div class="flex-shrink-0">
                                        <div class="w-8 h-8 bg-blue-500/20 rounded-lg flex items-center justify-center">
                                            <svg class="w-4 h-4 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                                            </svg>
                                        </div>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-white">{{ app.name }}</p>
                                        <div class="flex items-center space-x-1 mt-1">
                                            {% if app.hwid_lock %}
                                                <div class="w-2 h-2 bg-yellow-500 rounded-full tooltip" data-tooltip="HWID Lock Enabled"></div>
                                            {% endif %}
                                            {% if app.webhook_url %}
                                                <div class="w-2 h-2 bg-blue-500 rounded-full tooltip" data-tooltip="Webhook Configured"></div>
                                            {% endif %}
                                            {% if app.is_active %}
                                                <div class="w-2 h-2 bg-pepe-green rounded-full tooltip" data-tooltip="Active"></div>
                                            {% else %}
                                                <div class="w-2 h-2 bg-red-500 rounded-full tooltip" data-tooltip="Inactive"></div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="flex items-center space-x-2">
                                    <div class="w-6 h-6 bg-pepe-green rounded-full flex items-center justify-center">
                                        <span class="text-white font-medium text-xs">{{ app.owner.username[0].upper() }}</span>
                                    </div>
                                    <span class="text-sm text-white">{{ app.owner.username }}</span>
                                </div>
                            </td>
                            <td>
                                {% if app.is_active %}
                                    <span class="badge badge-success">Active</span>
                                {% else %}
                                    <span class="badge badge-error">Inactive</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="text-sm">
                                    <div class="text-white">{{ app.key_count }} total</div>
                                    <div class="text-gray-400">{{ app.used_keys }} used</div>
                                </div>
                            </td>
                            <td>
                                {% if app.total_auths > 0 %}
                                    {% set success_rate = (app.successful_auths / app.total_auths * 100) %}
                                    <div class="flex items-center space-x-2">
                                        <span class="text-sm text-white">{{ success_rate | round(1) }}%</span>
                                        <div class="w-16 bg-pepe-gray rounded-full h-2">
                                            <div class="bg-pepe-green h-2 rounded-full" style="width: {{ success_rate }}%"></div>
                                        </div>
                                    </div>
                                {% else %}
                                    <span class="text-sm text-gray-500">No data</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if app.last_activity %}
                                    <span class="text-sm text-gray-400">{{ app.last_activity.strftime('%Y-%m-%d') }}</span>
                                {% else %}
                                    <span class="text-sm text-gray-500">Never</span>
                                {% endif %}
                            </td>
                            <td>
                                <span class="text-sm text-gray-400">{{ app.created_at.strftime('%Y-%m-%d') }}</span>
                            </td>
                            <td>
                                <div class="flex items-center space-x-2">
                                    <a href="{{ url_for('admin.app_detail', app_id=app.id) }}" 
                                       class="text-blue-400 hover:text-blue-300 transition-colors" 
                                       title="View Details">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                        </svg>
                                    </a>
                                    <button onclick="regenerateSecret({{ app.id }}, '{{ app.name }}')" 
                                            class="text-yellow-400 hover:text-yellow-300 transition-colors" 
                                            title="Regenerate Secret">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                        </svg>
                                    </button>
                                    {% if app.is_active %}
                                        <button onclick="toggleAppStatus({{ app.id }}, false, '{{ app.name }}')" 
                                                class="text-red-400 hover:text-red-300 transition-colors" 
                                                title="Deactivate">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                            </svg>
                                        </button>
                                    {% else %}
                                        <button onclick="toggleAppStatus({{ app.id }}, true, '{{ app.name }}')" 
                                                class="text-green-400 hover:text-green-300 transition-colors" 
                                                title="Activate">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                            </svg>
                                        </button>
                                    {% endif %}
                                    <button onclick="deleteApp({{ app.id }}, '{{ app.name }}')" 
                                            class="text-red-400 hover:text-red-300 transition-colors" 
                                            title="Delete App">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                        </svg>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- Bulk Actions -->
        <div class="p-4 border-t border-pepe-light-gray/20 bg-pepe-darker/30" id="bulk-actions" style="display: none;">
            <div class="flex items-center justify-between">
                <span class="text-sm text-gray-300" id="selected-count">0 apps selected</span>
                <div class="flex space-x-2">
                    <button onclick="bulkActivate()" class="btn-secondary text-sm">Activate Selected</button>
                    <button onclick="bulkDeactivate()" class="btn-secondary text-sm">Deactivate Selected</button>
                    <button onclick="bulkRegenSecrets()" class="btn-danger text-sm">Regenerate Secrets</button>
                    <button onclick="bulkDelete()" class="btn-danger text-sm">Delete Selected</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Pagination -->
    {% if pagination.pages > 1 %}
        <div class="flex items-center justify-between mt-8">
            <div class="text-sm text-gray-400">
                Showing {{ pagination.per_page * (pagination.page - 1) + 1 }} to {{ pagination.per_page * pagination.page if pagination.page < pagination.pages else pagination.total }} of {{ pagination.total }} apps
            </div>
            <div class="flex space-x-2">
                {% if pagination.has_prev %}
                    <a href="{{ url_for('admin.apps', page=pagination.prev_num) }}" class="btn-secondary text-sm">Previous</a>
                {% endif %}
                
                {% for page_num in pagination.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != pagination.page %}
                            <a href="{{ url_for('admin.apps', page=page_num) }}" class="btn-secondary text-sm">{{ page_num }}</a>
                        {% else %}
                            <span class="btn-primary text-sm">{{ page_num }}</span>
                        {% endif %}
                    {% else %}
                        <span class="text-gray-400">...</span>
                    {% endif %}
                {% endfor %}
                
                {% if pagination.has_next %}
                    <a href="{{ url_for('admin.apps', page=pagination.next_num) }}" class="btn-secondary text-sm">Next</a>
                {% endif %}
            </div>
        </div>
    {% endif %}
</div>
{% endblock %}

{% block scripts %}
<script>
// Search and filter functionality
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('search-input');
    const ownerFilter = document.getElementById('owner-filter');
    const statusFilter = document.getElementById('status-filter');
    const sortFilter = document.getElementById('sort-filter');
    const selectAll = document.getElementById('select-all');
    const appCheckboxes = document.querySelectorAll('.app-checkbox');
    const bulkActions = document.getElementById('bulk-actions');
    const selectedCount = document.getElementById('selected-count');
    
    // Search functionality
    searchInput.addEventListener('input', filterApps);
    ownerFilter.addEventListener('change', filterApps);
    statusFilter.addEventListener('change', filterApps);
    sortFilter.addEventListener('change', sortApps);
    
    // Select all functionality
    selectAll.addEventListener('change', function() {
        appCheckboxes.forEach(checkbox => {
            if (checkbox.closest('tr').style.display !== 'none') {
                checkbox.checked = this.checked;
            }
        });
        updateBulkActions();
    });
    
    // Individual checkbox functionality
    appCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateBulkActions);
    });
    
    function filterApps() {
        const searchTerm = searchInput.value.toLowerCase();
        const ownerId = ownerFilter.value;
        const status = statusFilter.value;
        
        document.querySelectorAll('.app-row').forEach(row => {
            const name = row.dataset.name;
            const rowOwner = row.dataset.owner;
            const rowStatus = row.dataset.status;
            const hasWebhook = row.dataset.webhook === 'true';
            const hasHwidLock = row.dataset.hwid === 'true';
            
            const matchesSearch = !searchTerm || name.includes(searchTerm);
            const matchesOwner = !ownerId || rowOwner === ownerId;
            let matchesStatus = true;
            
            if (status === 'active' || status === 'inactive') {
                matchesStatus = rowStatus === status;
            } else if (status === 'webhook') {
                matchesStatus = hasWebhook;
            } else if (status === 'hwid_lock') {
                matchesStatus = hasHwidLock;
            }
            
            if (matchesSearch && matchesOwner && matchesStatus) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
                row.querySelector('.app-checkbox').checked = false;
            }
        });
        
        updateBulkActions();
    }
    
    function sortApps() {
        const sortBy = sortFilter.value;
        const tbody = document.querySelector('#apps-table tbody');
        const rows = Array.from(tbody.querySelectorAll('.app-row'));
        
        rows.sort((a, b) => {
            let aVal, bVal;
            
            switch(sortBy) {
                case 'name_asc':
                    aVal = a.dataset.name;
                    bVal = b.dataset.name;
                    return aVal.localeCompare(bVal);
                case 'name_desc':
                    aVal = a.dataset.name;
                    bVal = b.dataset.name;
                    return bVal.localeCompare(aVal);
                case 'keys_desc':
                    aVal = parseInt(a.querySelector('td:nth-child(5)').textContent.split(' ')[0]);
                    bVal = parseInt(b.querySelector('td:nth-child(5)').textContent.split(' ')[0]);
                    return bVal - aVal;
                case 'created_asc':
                    aVal = a.querySelector('td:nth-child(8)').textContent;
                    bVal = b.querySelector('td:nth-child(8)').textContent;
                    return new Date(aVal) - new Date(bVal);
                case 'created_desc':
                default:
                    aVal = a.querySelector('td:nth-child(8)').textContent;
                    bVal = b.querySelector('td:nth-child(8)').textContent;
                    return new Date(bVal) - new Date(aVal);
            }
        });
        
        rows.forEach(row => tbody.appendChild(row));
    }
    
    function updateBulkActions() {
        const checkedBoxes = document.querySelectorAll('.app-checkbox:checked');
        const count = checkedBoxes.length;
        
        if (count > 0) {
            bulkActions.style.display = 'block';
            selectedCount.textContent = `${count} app${count > 1 ? 's' : ''} selected`;
        } else {
            bulkActions.style.display = 'none';
        }
        
        // Update select all checkbox
        const visibleCheckboxes = Array.from(appCheckboxes).filter(cb => 
            cb.closest('tr').style.display !== 'none'
        );
        const checkedVisible = visibleCheckboxes.filter(cb => cb.checked);
        
        selectAll.indeterminate = checkedVisible.length > 0 && checkedVisible.length < visibleCheckboxes.length;
        selectAll.checked = visibleCheckboxes.length > 0 && checkedVisible.length === visibleCheckboxes.length;
    }
});

// App management functions
function regenerateSecret(appId, appName) {
    if (confirm(`Are you sure you want to regenerate the secret for "${appName}"? This will require updating the application code.`)) {
        fetch(`/admin/app/${appId}/regenerate-secret`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('meta[name=csrf-token]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification(`Secret regenerated for ${appName}!`, 'success');
                location.reload();
            } else {
                showNotification('Error: ' + data.message, 'error');
            }
        });
    }
}

function toggleAppStatus(appId, activate, appName) {
    const action = activate ? 'activate' : 'deactivate';
    if (confirm(`Are you sure you want to ${action} "${appName}"?`)) {
        fetch(`/admin/app/${appId}/toggle-status`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('meta[name=csrf-token]').getAttribute('content')
            },
            body: JSON.stringify({ active: activate })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification(`${appName} ${action}d successfully!`, 'success');
                location.reload();
            } else {
                showNotification('Error: ' + data.message, 'error');
            }
        });
    }
}

function deleteApp(appId, appName) {
    const userInput = prompt(`Are you sure you want to delete "${appName}"? This will delete all associated license keys.\n\nType "DELETE" to confirm:`);
    if (userInput === 'DELETE') {
        fetch(`/admin/app/${appId}/delete`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('meta[name=csrf-token]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification(`${appName} deleted successfully!`, 'success');
                location.reload();
            } else {
                showNotification('Error: ' + data.message, 'error');
            }
        });
    }
}

// Bulk operations
function getSelectedApps() {
    return Array.from(document.querySelectorAll('.app-checkbox:checked')).map(cb => cb.value);
}

function bulkActivate() {
    const apps = getSelectedApps();
    if (apps.length === 0) return;
    
    if (confirm(`Are you sure you want to activate ${apps.length} applications?`)) {
        fetch('/admin/apps/bulk-activate', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('meta[name=csrf-token]').getAttribute('content')
            },
            body: JSON.stringify({ apps: apps })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification(`${apps.length} applications activated!`, 'success');
                location.reload();
            } else {
                showNotification('Error: ' + data.message, 'error');
            }
        });
    }
}

function bulkDeactivate() {
    const apps = getSelectedApps();
    if (apps.length === 0) return;
    
    if (confirm(`Are you sure you want to deactivate ${apps.length} applications?`)) {
        fetch('/admin/apps/bulk-deactivate', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('meta[name=csrf-token]').getAttribute('content')
            },
            body: JSON.stringify({ apps: apps })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification(`${apps.length} applications deactivated!`, 'success');
                location.reload();
            } else {
                showNotification('Error: ' + data.message, 'error');
            }
        });
    }
}

function bulkRegenSecrets() {
    const apps = getSelectedApps();
    if (apps.length === 0) return;
    
    if (confirm(`Are you sure you want to regenerate secrets for ${apps.length} applications? This will require updating all application code.`)) {
        fetch('/admin/apps/bulk-regenerate-secrets', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('meta[name=csrf-token]').getAttribute('content')
            },
            body: JSON.stringify({ apps: apps })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification(`Secrets regenerated for ${apps.length} applications!`, 'success');
                location.reload();
            } else {
                showNotification('Error: ' + data.message, 'error');
            }
        });
    }
}

function bulkDelete() {
    const apps = getSelectedApps();
    if (apps.length === 0) return;
    
    const userInput = prompt(`Are you sure you want to delete ${apps.length} applications? This will delete all associated license keys.\n\nType "DELETE" to confirm:`);
    if (userInput === 'DELETE') {
        fetch('/admin/apps/bulk-delete', {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('meta[name=csrf-token]').getAttribute('content')
            },
            body: JSON.stringify({ apps: apps })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification(`${apps.length} applications deleted!`, 'success');
                location.reload();
            } else {
                showNotification('Error: ' + data.message, 'error');
            }
        });
    }
}

function exportApps() {
    window.location.href = '/admin/apps/export';
}

function resetFilters() {
    document.getElementById('search-input').value = '';
    document.getElementById('owner-filter').value = '';
    document.getElementById('status-filter').value = '';
    document.getElementById('sort-filter').value = 'created_desc';
    
    document.querySelectorAll('.app-row').forEach(row => {
        row.style.display = '';
    });
    
    document.querySelectorAll('.app-checkbox').forEach(cb => {
        cb.checked = false;
    });
    
    document.getElementById('select-all').checked = false;
    document.getElementById('bulk-actions').style.display = 'none';
}

function showNotification(message, type) {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg ${
        type === 'success' ? 'bg-green-500' : 'bg-red-500'
    } text-white`;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.remove();
    }, 3000);
}
</script>
{% endblock %}
