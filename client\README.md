# PepeAuth Python Client

A professional KeyAuth-like license authentication client for Python applications.

## Features

- 🔑 **License Key Authentication** - Secure license validation
- 📱 **Hardware ID Binding** - Automatic device binding for security
- 🔒 **HMAC Security** - Optional HMAC signing for API requests
- 👤 **User Information** - Detailed user and license information
- ⏰ **Expiration Tracking** - Monitor license expiration dates
- 🎯 **Easy Integration** - Simple API for your applications

## Quick Start

### Basic Usage

```python
from client import PepeAuthClient

# Initialize client
client = PepeAuthClient(api_url="http://127.0.0.1:5000")

# Authenticate with license key
if client.login("YOUR-LICENSE-KEY"):
    print("✅ Authentication successful!")
    
    # Get user information
    user_info = client.get_user_info()
    print(f"Plan: {user_info['plan']}")
    print(f"Days left: {user_info['expires_in_days']}")
    
    # Your application logic here
    print("🎮 Starting premium features...")
else:
    print("❌ Authentication failed!")
```

### Interactive Client

Run the interactive client:

```bash
python client.py
```

This will prompt you for a license key and provide an interactive menu.

### Integration Example

Run the integration example:

```bash
python example.py
```

This demonstrates how to integrate the client into your application.

## API Reference

### PepeAuthClient Class

#### Constructor

```python
client = PepeAuthClient(api_url="http://127.0.0.1:5000", app_name="MyApp")
```

- `api_url`: Base URL of the PepeAuth server
- `app_name`: Name of your application

#### Methods

##### `login(license_key)`

Authenticate with a license key.

- **Parameters**: `license_key` (str) - The license key to validate
- **Returns**: `bool` - True if authentication successful
- **Side effects**: Sets session token and user info on success

##### `get_user_info()`

Get detailed user information (requires authentication).

- **Returns**: `dict` - User information or None if failed
- **Requires**: Must be authenticated first

##### `is_authenticated()`

Check if client is currently authenticated.

- **Returns**: `bool` - True if authenticated

##### `logout()`

Clear authentication data.

- **Side effects**: Clears session token and user info

## Hardware ID

The client automatically generates a unique Hardware ID (HWID) based on:
- System information (OS, architecture)
- MAC address
- Processor information

This HWID is used to bind licenses to specific devices for security.

## Error Handling

The client handles various error conditions:

- **Network errors** - Connection timeouts, server unavailable
- **Invalid responses** - Malformed JSON, unexpected data
- **Authentication errors** - Invalid keys, expired licenses, banned users
- **HWID mismatches** - License bound to different device

## Test License Keys

For testing purposes, use these keys:

- `DXZESZV0PDP5872H` - Activated Premium key
- `GLBHKHB46D0KWASI` - Unused Premium key  
- `XO9IH22LKFTG4AJQ` - Unused Premium key

## Security Features

- **Device Binding** - Licenses are bound to specific hardware
- **Session Tokens** - Secure session management
- **HMAC Signing** - Optional request signing for enhanced security
- **Rate Limiting** - Built-in protection against abuse

## Requirements

- Python 3.6+
- `requests` library
- `hashlib` (built-in)
- `hmac` (built-in)
- `platform` (built-in)
- `uuid` (built-in)

## Installation

1. Copy the `client.py` file to your project
2. Install requirements: `pip install requests`
3. Import and use the `PepeAuthClient` class

## Example Integration

```python
def protected_feature():
    client = PepeAuthClient()
    
    if client.login(user_license_key):
        # Check license expiration
        user_info = client.get_user_info()
        if user_info['expires_in_days'] <= 0:
            print("License expired!")
            return False
        
        # Execute protected functionality
        return execute_premium_features()
    else:
        print("Access denied - Invalid license")
        return False
```

## Support

For support and documentation, visit the PepeAuth server admin panel or contact your license provider.
