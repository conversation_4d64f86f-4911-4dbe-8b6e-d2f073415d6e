{% extends "base.html" %}

{% block title %}Edit User - Admin - PepeAuth{% endblock %}

{% block content %}
<div class="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center space-x-4 mb-4">
            <a href="{{ url_for('admin.users') }}" class="text-gray-400 hover:text-white">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
            </a>
            <h1 class="text-3xl font-bold text-white">Edit User</h1>
        </div>
        <p class="text-gray-300">Modify user settings and permissions</p>
    </div>
    
    <!-- User Info -->
    <div class="glass-card p-6 rounded-xl mb-8">
        <h3 class="text-lg font-semibold text-white mb-4">User Information</h3>
        <div class="grid grid-cols-2 gap-4 text-sm">
            <div>
                <span class="text-gray-300">Username:</span>
                <span class="text-white ml-2">{{ user.username }}</span>
            </div>
            <div>
                <span class="text-gray-300">Email:</span>
                <span class="text-white ml-2">{{ user.email }}</span>
            </div>
            <div>
                <span class="text-gray-300">Created:</span>
                <span class="text-white ml-2">{{ user.created_at }}</span>
            </div>
            <div>
                <span class="text-gray-300">User ID:</span>
                <span class="text-white ml-2">{{ user.id }}</span>
            </div>
        </div>
    </div>
    
    <!-- Edit Form -->
    <div class="glass-card p-8 rounded-2xl">
        <form method="POST" class="space-y-6">
            {{ form.hidden_tag() }}
            
            <div>
                {{ form.plan_id.label(class="block text-sm font-medium text-white mb-2") }}
                {{ form.plan_id(class="input-field") }}
                {% if form.plan_id.errors %}
                    <div class="mt-1 text-sm text-red-400">
                        {% for error in form.plan_id.errors %}
                            <p>{{ error }}</p>
                        {% endfor %}
                    </div>
                {% endif %}
                <p class="mt-1 text-sm text-gray-400">Select the subscription plan for this user</p>
            </div>
            
            <div class="flex items-start">
                <div class="flex items-center h-5">
                    {{ form.is_admin(class="h-4 w-4 text-pepe-green focus:ring-pepe-green border-gray-600 rounded bg-pepe-gray") }}
                </div>
                <div class="ml-3">
                    {{ form.is_admin.label(class="text-sm font-medium text-white") }}
                    <p class="text-sm text-gray-400">Grant administrative privileges to this user</p>
                </div>
            </div>
            
            <div class="flex space-x-4">
                <button type="submit" class="btn-primary flex-1">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Save Changes
                </button>
                <a href="{{ url_for('admin.users') }}" class="btn-secondary flex-1 text-center">
                    Cancel
                </a>
            </div>
        </form>
    </div>
    
    <!-- Warning -->
    <div class="glass-card p-6 rounded-xl mt-8 border-l-4 border-yellow-500">
        <div class="flex items-start">
            <svg class="w-6 h-6 text-yellow-400 mr-3 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
            </svg>
            <div>
                <h4 class="text-lg font-semibold text-yellow-400 mb-2">Important Notes</h4>
                <ul class="text-yellow-300 text-sm space-y-1">
                    <li>• Changing the plan will immediately affect the user's limits</li>
                    <li>• Admin users have full access to the admin panel</li>
                    <li>• Plan changes take effect immediately</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}
