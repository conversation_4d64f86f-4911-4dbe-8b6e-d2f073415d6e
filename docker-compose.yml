version: '3.8'

services:
  pepeauth:
    build: .
    container_name: pepeauth
    ports:
      - "5000:5000"
    environment:
      - SECRET_KEY=${SECRET_KEY:-your-secret-key-here}
      - DEBUG=False
      - DATABASE_URL=********************************************/pepeauth
      - REDIS_URL=redis://redis:6379/0
      - RATE_LIMIT_STORAGE_URL=redis://redis:6379/1
    volumes:
      - ./logs:/app/logs
      - ./uploads:/app/uploads
      - ./backups:/app/backups
    depends_on:
      - postgres
      - redis
    restart: unless-stopped
    networks:
      - pepeauth-network

  postgres:
    image: postgres:15-alpine
    container_name: pepeauth-postgres
    environment:
      - POSTGRES_DB=pepeauth
      - POSTGRES_USER=pepeauth
      - POSTGRES_PASSWORD=pepeauth
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backups:/backups
    restart: unless-stopped
    networks:
      - pepeauth-network

  redis:
    image: redis:7-alpine
    container_name: pepeauth-redis
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - pepeauth-network

  nginx:
    image: nginx:alpine
    container_name: pepeauth-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - pepeauth
    restart: unless-stopped
    networks:
      - pepeauth-network

volumes:
  postgres_data:
  redis_data:

networks:
  pepeauth-network:
    driver: bridge
