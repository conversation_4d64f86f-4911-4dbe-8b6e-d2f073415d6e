#!/usr/bin/env python3
"""
Check what HWID is stored in the database for our test keys
"""

from app import create_app, query_db

def check_hwid():
    """Check stored HWID values"""
    app = create_app()
    
    with app.app_context():
        print("🔍 Checking stored HWID values")
        print("=" * 50)
        
        keys = query_db('SELECT key, hwid, used FROM license_key WHERE used = 1')
        
        for key_data in keys:
            print(f"Key: {key_data['key']}")
            print(f"HWID: {key_data['hwid']}")
            print(f"Used: {key_data['used']}")
            print("-" * 30)

if __name__ == "__main__":
    check_hwid()
