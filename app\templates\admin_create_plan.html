{% extends "base.html" %}

{% block title %}Create Plan - Admin - PepeAuth{% endblock %}

{% block content %}
<div class="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center space-x-4 mb-4">
            <a href="{{ url_for('admin.plans') }}" class="text-gray-400 hover:text-white">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
            </a>
            <h1 class="text-3xl font-bold text-white">Create New Plan</h1>
        </div>
        <p class="text-gray-300">Create a new subscription plan with custom limits</p>
    </div>
    
    <!-- Form -->
    <div class="glass-card p-8 rounded-2xl">
        <form method="POST" class="space-y-6">
            {{ form.hidden_tag() }}
            
            <div>
                {{ form.name.label(class="block text-sm font-medium text-white mb-2") }}
                {{ form.name(class="input-field", placeholder="Enterprise") }}
                {% if form.name.errors %}
                    <div class="mt-1 text-sm text-red-400">
                        {% for error in form.name.errors %}
                            <p>{{ error }}</p>
                        {% endfor %}
                    </div>
                {% endif %}
                <p class="mt-1 text-sm text-gray-400">Choose a unique name for this plan</p>
            </div>
            
            <div>
                {{ form.max_apps.label(class="block text-sm font-medium text-white mb-2") }}
                {{ form.max_apps(class="input-field") }}
                {% if form.max_apps.errors %}
                    <div class="mt-1 text-sm text-red-400">
                        {% for error in form.max_apps.errors %}
                            <p>{{ error }}</p>
                        {% endfor %}
                    </div>
                {% endif %}
                <p class="mt-1 text-sm text-gray-400">{{ form.max_apps.description }}</p>
            </div>
            
            <div>
                {{ form.max_keys.label(class="block text-sm font-medium text-white mb-2") }}
                {{ form.max_keys(class="input-field") }}
                {% if form.max_keys.errors %}
                    <div class="mt-1 text-sm text-red-400">
                        {% for error in form.max_keys.errors %}
                            <p>{{ error }}</p>
                        {% endfor %}
                    </div>
                {% endif %}
                <p class="mt-1 text-sm text-gray-400">{{ form.max_keys.description }}</p>
            </div>
            
            <div>
                {{ form.rate_limit.label(class="block text-sm font-medium text-white mb-2") }}
                {{ form.rate_limit(class="input-field") }}
                {% if form.rate_limit.errors %}
                    <div class="mt-1 text-sm text-red-400">
                        {% for error in form.rate_limit.errors %}
                            <p>{{ error }}</p>
                        {% endfor %}
                    </div>
                {% endif %}
                <p class="mt-1 text-sm text-gray-400">{{ form.rate_limit.description }}</p>
            </div>
            
            <div class="flex space-x-4">
                <button type="submit" class="btn-primary flex-1">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Create Plan
                </button>
                <a href="{{ url_for('admin.plans') }}" class="btn-secondary flex-1 text-center">
                    Cancel
                </a>
            </div>
        </form>
    </div>
    
    <!-- Info -->
    <div class="glass-card p-6 rounded-xl mt-8">
        <h3 class="text-lg font-semibold text-white mb-4">Plan Configuration Tips</h3>
        <div class="space-y-3 text-sm text-gray-300">
            <div class="flex items-start">
                <svg class="w-5 h-5 text-pepe-green mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <p><strong>Unlimited Values:</strong> Use -1 for unlimited apps, keys, or rate limits</p>
            </div>
            <div class="flex items-start">
                <svg class="w-5 h-5 text-pepe-green mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <p><strong>Rate Limits:</strong> API calls per day (e.g., 1000 = 1000 calls per day)</p>
            </div>
            <div class="flex items-start">
                <svg class="w-5 h-5 text-pepe-green mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <p><strong>Plan Names:</strong> Must be unique across all plans</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}
