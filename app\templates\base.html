<!DOCTYPE html>
<html lang="en" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}PepeAuth - License Management Platform{% endblock %}</title>
    
    <!-- TailwindCSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        'pepe-green': '#2ecc71',
                        'pepe-dark': '#111827',
                        'pepe-darker': '#0f172a',
                        'pepe-gray': '#1f2937',
                        'pepe-light-gray': '#374151'
                    },
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif']
                    }
                }
            }
        }
    </script>
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/pepe.css') }}">
    
    <!-- Alpine.js -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
</head>
<body class="bg-pepe-dark text-white font-sans min-h-screen">
    <!-- Navigation -->
    <nav class="glass-card border-b border-pepe-light-gray/20 sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <div class="flex items-center">
                    <a href="{{ url_for('index') }}" class="flex items-center space-x-2">
                        <div class="w-8 h-8 bg-pepe-green rounded-lg flex items-center justify-center">
                            <span class="text-white font-bold text-lg">P</span>
                        </div>
                        <span class="text-xl font-bold text-white">PepeAuth</span>
                    </a>
                </div>
                
                <!-- Navigation Links -->
                <div class="hidden md:block">
                    <div class="ml-10 flex items-baseline space-x-4">
                        {% if current_user.is_authenticated %}
                            <a href="{{ url_for('dashboard.index') }}" 
                               class="nav-link {% if request.endpoint and 'dashboard' in request.endpoint %}active{% endif %}">
                                Dashboard
                            </a>
                            {% if current_user.is_admin %}
                                <a href="{{ url_for('admin.index') }}"
                                   class="nav-link {% if request.endpoint and 'admin' in request.endpoint and 'keyauth' not in request.endpoint %}active{% endif %}">
                                    Admin
                                </a>
                                <a href="{{ url_for('admin.keyauth_admin') }}"
                                   class="nav-link {% if request.endpoint and 'keyauth' in request.endpoint %}active{% endif %}">
                                    🔑 KeyAuth
                                </a>
                            {% endif %}
                            <a href="{{ url_for('auth.profile') }}" class="nav-link">Profile</a>
                        {% else %}
                            <a href="{{ url_for('index') }}" class="nav-link">Home</a>
                            <a href="#features" class="nav-link">Features</a>
                            <a href="#pricing" class="nav-link">Pricing</a>
                        {% endif %}
                    </div>
                </div>
                
                <!-- User Menu -->
                <div class="flex items-center space-x-4">
                    {% if current_user.is_authenticated %}
                        <div class="relative" x-data="{ open: false }">
                            <button @click="open = !open" 
                                    class="flex items-center space-x-2 text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-pepe-green">
                                <div class="w-8 h-8 bg-pepe-green rounded-full flex items-center justify-center">
                                    <span class="text-white font-medium">{{ current_user.username[0].upper() }}</span>
                                </div>
                                <span class="hidden md:block text-white">{{ current_user.username }}</span>
                            </button>
                            
                            <div x-show="open" @click.away="open = false" 
                                 class="absolute right-0 mt-2 w-48 glass-card rounded-lg shadow-lg py-1 z-50">
                                <a href="{{ url_for('auth.profile') }}" 
                                   class="block px-4 py-2 text-sm text-white hover:bg-pepe-green/20 rounded-lg mx-1">
                                    Profile
                                </a>
                                <a href="{{ url_for('auth.logout') }}" 
                                   class="block px-4 py-2 text-sm text-white hover:bg-red-500/20 rounded-lg mx-1">
                                    Logout
                                </a>
                            </div>
                        </div>
                    {% else %}
                        <a href="{{ url_for('auth.login') }}" class="btn-secondary">Login</a>
                        <a href="{{ url_for('auth.register') }}" class="btn-primary">Get Started</a>
                    {% endif %}
                </div>
                
                <!-- Mobile menu button -->
                <div class="md:hidden">
                    <button type="button" class="text-white hover:text-pepe-green focus:outline-none focus:text-pepe-green">
                        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    </nav>
    
    <!-- Flash Messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <div class="fixed top-20 right-4 z-50 space-y-2" x-data="{ show: true }" x-show="show">
                {% for category, message in messages %}
                    <div class="glass-card p-4 rounded-lg shadow-lg max-w-sm 
                                {% if category == 'error' %}border-l-4 border-red-500{% endif %}
                                {% if category == 'success' %}border-l-4 border-pepe-green{% endif %}
                                {% if category == 'info' %}border-l-4 border-blue-500{% endif %}
                                {% if category == 'warning' %}border-l-4 border-yellow-500{% endif %}"
                         x-transition>
                        <div class="flex items-center justify-between">
                            <p class="text-sm text-white">{{ message }}</p>
                            <button @click="show = false" class="ml-4 text-white/60 hover:text-white">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                {% endfor %}
            </div>
        {% endif %}
    {% endwith %}
    
    <!-- Main Content -->
    <main class="{% block main_class %}{% endblock %}">
        {% block content %}{% endblock %}
    </main>
    
    <!-- Footer -->
    <footer class="bg-pepe-darker border-t border-pepe-light-gray/20 mt-auto">
        <div class="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div class="col-span-1 md:col-span-2">
                    <div class="flex items-center space-x-2 mb-4">
                        <div class="w-8 h-8 bg-pepe-green rounded-lg flex items-center justify-center">
                            <span class="text-white font-bold text-lg">P</span>
                        </div>
                        <span class="text-xl font-bold text-white">PepeAuth</span>
                    </div>
                    <p class="text-gray-400 text-sm max-w-md">
                        Modern license management platform for developers. 
                        Secure, scalable, and easy to integrate.
                    </p>
                </div>
                
                <div>
                    <h3 class="text-white font-semibold mb-4">Platform</h3>
                    <ul class="space-y-2 text-sm text-gray-400">
                        <li><a href="#" class="hover:text-pepe-green transition-colors">API Documentation</a></li>
                        <li><a href="#" class="hover:text-pepe-green transition-colors">Status</a></li>
                        <li><a href="#" class="hover:text-pepe-green transition-colors">Changelog</a></li>
                    </ul>
                </div>
                
                <div>
                    <h3 class="text-white font-semibold mb-4">Support</h3>
                    <ul class="space-y-2 text-sm text-gray-400">
                        <li><a href="#" class="hover:text-pepe-green transition-colors">Help Center</a></li>
                        <li><a href="#" class="hover:text-pepe-green transition-colors">Contact</a></li>
                        <li><a href="#" class="hover:text-pepe-green transition-colors">Discord</a></li>
                    </ul>
                </div>
            </div>
            
            <div class="border-t border-pepe-light-gray/20 mt-8 pt-8 text-center">
                <p class="text-gray-400 text-sm">
                    © 2025 PepeAuth. Built with 🐸 and Flask.
                </p>
            </div>
        </div>
    </footer>
    
    {% block scripts %}{% endblock %}
</body>
</html>
