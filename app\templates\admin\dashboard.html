{% extends "base.html" %}

{% block title %}Admin Dashboard - PepeAuth{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div>
                <h1 class="text-3xl font-bold text-white">Admin Dashboard</h1>
                <p class="mt-1 text-gray-300">System overview and management</p>
            </div>
            <div class="mt-4 sm:mt-0 flex space-x-3">
                <a href="{{ url_for('admin.users') }}" class="btn-primary">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                    </svg>
                    Manage Users
                </a>
                <a href="{{ url_for('admin.system_logs') }}" class="btn-secondary">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    View Logs
                </a>
            </div>
        </div>
    </div>
    
    <!-- System Stats -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="glass-card p-6 rounded-xl card-hover">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-10 h-10 bg-blue-500/20 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-300">Total Users</p>
                    <p class="text-2xl font-semibold text-white">{{ stats.total_users }}</p>
                    <p class="text-xs text-gray-400">{{ stats.active_users }} active today</p>
                </div>
            </div>
        </div>
        
        <div class="glass-card p-6 rounded-xl card-hover">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-10 h-10 bg-pepe-green/20 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-pepe-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-300">Total Apps</p>
                    <p class="text-2xl font-semibold text-white">{{ stats.total_apps }}</p>
                    <p class="text-xs text-gray-400">{{ stats.active_apps }} active</p>
                </div>
            </div>
        </div>
        
        <div class="glass-card p-6 rounded-xl card-hover">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-10 h-10 bg-purple-500/20 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m0 0a2 2 0 012 2m-2-2a2 2 0 00-2 2m2-2V5a2 2 0 00-2-2m0 0H9a2 2 0 00-2 2v0a2 2 0 002 2h2m-4 4v6a2 2 0 002 2h2a2 2 0 002-2v-6m-4 0a2 2 0 012-2h0a2 2 0 012 2v0"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-300">License Keys</p>
                    <p class="text-2xl font-semibold text-white">{{ stats.total_keys }}</p>
                    <p class="text-xs text-gray-400">{{ stats.used_keys }} used</p>
                </div>
            </div>
        </div>
        
        <div class="glass-card p-6 rounded-xl card-hover">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-10 h-10 bg-yellow-500/20 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-300">API Requests</p>
                    <p class="text-2xl font-semibold text-white">{{ stats.api_requests_today }}</p>
                    <p class="text-xs text-gray-400">Today</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Charts Row -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <!-- User Growth Chart -->
        <div class="glass-card p-6 rounded-xl">
            <h3 class="text-lg font-semibold text-white mb-4">User Growth (Last 30 Days)</h3>
            <div class="h-64 flex items-center justify-center">
                <canvas id="userGrowthChart" width="400" height="200"></canvas>
            </div>
        </div>
        
        <!-- API Usage Chart -->
        <div class="glass-card p-6 rounded-xl">
            <h3 class="text-lg font-semibold text-white mb-4">API Usage (Last 7 Days)</h3>
            <div class="h-64 flex items-center justify-center">
                <canvas id="apiUsageChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
    
    <!-- Recent Activity -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <!-- Recent Users -->
        <div class="glass-card p-6 rounded-xl">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-white">Recent Users</h3>
                <a href="{{ url_for('admin.users') }}" class="text-sm text-pepe-green hover:text-pepe-green/80">View All</a>
            </div>
            <div class="space-y-4">
                {% for user in recent_users %}
                    <div class="flex items-center space-x-3 p-3 rounded-lg bg-pepe-gray/30">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-pepe-green rounded-full flex items-center justify-center">
                                <span class="text-white font-medium text-sm">{{ user.username[0].upper() }}</span>
                            </div>
                        </div>
                        <div class="flex-1 min-w-0">
                            <p class="text-sm font-medium text-white truncate">{{ user.username }}</p>
                            <p class="text-xs text-gray-400">{{ user.email }}</p>
                        </div>
                        <div class="flex-shrink-0">
                            <span class="badge badge-info">{{ user.plan.name }}</span>
                        </div>
                        <div class="flex-shrink-0 text-xs text-gray-400">
                            {{ user.created_at.strftime('%m/%d') }}
                        </div>
                    </div>
                {% endfor %}
            </div>
        </div>
        
        <!-- System Alerts -->
        <div class="glass-card p-6 rounded-xl">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-white">System Alerts</h3>
                <span class="badge badge-warning">{{ alerts|length }}</span>
            </div>
            <div class="space-y-4">
                {% for alert in alerts %}
                    <div class="flex items-start space-x-3 p-3 rounded-lg 
                                {% if alert.level == 'critical' %}bg-red-500/10 border border-red-500/20{% endif %}
                                {% if alert.level == 'warning' %}bg-yellow-500/10 border border-yellow-500/20{% endif %}
                                {% if alert.level == 'info' %}bg-blue-500/10 border border-blue-500/20{% endif %}">
                        <div class="flex-shrink-0">
                            {% if alert.level == 'critical' %}
                                <svg class="w-5 h-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                </svg>
                            {% elif alert.level == 'warning' %}
                                <svg class="w-5 h-5 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            {% else %}
                                <svg class="w-5 h-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            {% endif %}
                        </div>
                        <div class="flex-1 min-w-0">
                            <p class="text-sm text-white">{{ alert.message }}</p>
                            <p class="text-xs text-gray-400">{{ alert.created_at.strftime('%Y-%m-%d %H:%M') }}</p>
                        </div>
                        <button onclick="dismissAlert({{ alert.id }})" class="text-gray-400 hover:text-white">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                {% else %}
                    <div class="text-center py-8">
                        <div class="w-12 h-12 bg-pepe-gray rounded-full flex items-center justify-center mx-auto mb-3">
                            <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <p class="text-gray-400 text-sm">No active alerts</p>
                    </div>
                {% endfor %}
            </div>
        </div>
    </div>
    
    <!-- Quick Actions -->
    <div class="glass-card p-6 rounded-xl mb-8">
        <h3 class="text-lg font-semibold text-white mb-4">Quick Actions</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <a href="{{ url_for('admin.create_user') }}" 
               class="flex items-center p-4 rounded-lg bg-pepe-gray/30 hover:bg-pepe-green/20 transition-colors group">
                <div class="w-10 h-10 bg-blue-500/20 rounded-lg flex items-center justify-center mr-3 group-hover:bg-blue-500/30">
                    <svg class="w-5 h-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"></path>
                    </svg>
                </div>
                <div>
                    <p class="text-sm font-medium text-white">Create User</p>
                    <p class="text-xs text-gray-400">Add new user account</p>
                </div>
            </a>
            
            <a href="{{ url_for('admin.bulk_keys') }}" 
               class="flex items-center p-4 rounded-lg bg-pepe-gray/30 hover:bg-pepe-green/20 transition-colors group">
                <div class="w-10 h-10 bg-pepe-green/20 rounded-lg flex items-center justify-center mr-3 group-hover:bg-pepe-green/30">
                    <svg class="w-5 h-5 text-pepe-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m0 0a2 2 0 012 2m-2-2a2 2 0 00-2 2m2-2V5a2 2 0 00-2-2m0 0H9a2 2 0 00-2 2v0a2 2 0 002 2h2m-4 4v6a2 2 0 002 2h2a2 2 0 002-2v-6m-4 0a2 2 0 012-2h0a2 2 0 012 2v0"></path>
                    </svg>
                </div>
                <div>
                    <p class="text-sm font-medium text-white">Bulk Generate Keys</p>
                    <p class="text-xs text-gray-400">Create multiple license keys</p>
                </div>
            </a>
            
            <a href="{{ url_for('admin.system_settings') }}" 
               class="flex items-center p-4 rounded-lg bg-pepe-gray/30 hover:bg-pepe-green/20 transition-colors group">
                <div class="w-10 h-10 bg-purple-500/20 rounded-lg flex items-center justify-center mr-3 group-hover:bg-purple-500/30">
                    <svg class="w-5 h-5 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                </div>
                <div>
                    <p class="text-sm font-medium text-white">System Settings</p>
                    <p class="text-xs text-gray-400">Configure system options</p>
                </div>
            </a>
            
            <a href="{{ url_for('admin.export_data') }}" 
               class="flex items-center p-4 rounded-lg bg-pepe-gray/30 hover:bg-pepe-green/20 transition-colors group">
                <div class="w-10 h-10 bg-yellow-500/20 rounded-lg flex items-center justify-center mr-3 group-hover:bg-yellow-500/30">
                    <svg class="w-5 h-5 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                </div>
                <div>
                    <p class="text-sm font-medium text-white">Export Data</p>
                    <p class="text-xs text-gray-400">Download system reports</p>
                </div>
            </a>
        </div>
    </div>
    
    <!-- Recent Activity Log -->
    <div class="glass-card p-6 rounded-xl">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-white">Recent System Activity</h3>
            <a href="{{ url_for('admin.system_logs') }}" class="text-sm text-pepe-green hover:text-pepe-green/80">View All Logs</a>
        </div>
        <div class="overflow-x-auto">
            <table class="table-glass">
                <thead>
                    <tr>
                        <th>Time</th>
                        <th>Level</th>
                        <th>Category</th>
                        <th>Message</th>
                        <th>User</th>
                    </tr>
                </thead>
                <tbody>
                    {% for log in recent_logs %}
                        <tr>
                            <td class="text-sm text-gray-400">{{ log.created_at.strftime('%H:%M:%S') }}</td>
                            <td>
                                <span class="badge 
                                    {% if log.level == 'ERROR' %}badge-error{% endif %}
                                    {% if log.level == 'WARNING' %}badge-warning{% endif %}
                                    {% if log.level == 'INFO' %}badge-info{% endif %}
                                    {% if log.level == 'CRITICAL' %}badge-error{% endif %}">
                                    {{ log.level }}
                                </span>
                            </td>
                            <td class="text-sm text-gray-300">{{ log.category }}</td>
                            <td class="text-sm text-white">{{ log.message[:50] }}{% if log.message|length > 50 %}...{% endif %}</td>
                            <td class="text-sm text-gray-400">{{ log.user.username if log.user else 'System' }}</td>
                        </tr>
                    {% else %}
                        <tr>
                            <td colspan="5" class="text-center text-gray-400 py-8">No recent activity</td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// User Growth Chart
const userGrowthCtx = document.getElementById('userGrowthChart').getContext('2d');
new Chart(userGrowthCtx, {
    type: 'line',
    data: {
        labels: {{ user_growth_labels | tojsonfilter }},
        datasets: [{
            label: 'New Users',
            data: {{ user_growth_data | tojsonfilter }},
            borderColor: '#2ecc71',
            backgroundColor: 'rgba(46, 204, 113, 0.1)',
            tension: 0.4,
            fill: true
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                labels: {
                    color: '#ffffff'
                }
            }
        },
        scales: {
            x: {
                ticks: {
                    color: '#9ca3af'
                },
                grid: {
                    color: 'rgba(156, 163, 175, 0.1)'
                }
            },
            y: {
                ticks: {
                    color: '#9ca3af'
                },
                grid: {
                    color: 'rgba(156, 163, 175, 0.1)'
                }
            }
        }
    }
});

// API Usage Chart
const apiUsageCtx = document.getElementById('apiUsageChart').getContext('2d');
new Chart(apiUsageCtx, {
    type: 'bar',
    data: {
        labels: {{ api_usage_labels | tojsonfilter }},
        datasets: [{
            label: 'API Requests',
            data: {{ api_usage_data | tojsonfilter }},
            backgroundColor: 'rgba(46, 204, 113, 0.8)',
            borderColor: '#2ecc71',
            borderWidth: 1
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                labels: {
                    color: '#ffffff'
                }
            }
        },
        scales: {
            x: {
                ticks: {
                    color: '#9ca3af'
                },
                grid: {
                    color: 'rgba(156, 163, 175, 0.1)'
                }
            },
            y: {
                ticks: {
                    color: '#9ca3af'
                },
                grid: {
                    color: 'rgba(156, 163, 175, 0.1)'
                }
            }
        }
    }
});

// Auto-refresh dashboard every 30 seconds
setInterval(function() {
    // You can implement AJAX refresh here if needed
    // location.reload();
}, 30000);

function dismissAlert(alertId) {
    fetch(`/admin/alert/${alertId}/dismiss`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('meta[name=csrf-token]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        }
    });
}
</script>
{% endblock %}
