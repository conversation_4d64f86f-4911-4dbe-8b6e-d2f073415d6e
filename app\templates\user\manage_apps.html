{% extends "base.html" %}

{% block title %}Manage Apps - PepeAuth{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div>
                <h1 class="text-3xl font-bold text-white">Manage Applications</h1>
                <p class="mt-1 text-gray-300">View and manage all your applications</p>
            </div>
            <div class="mt-4 sm:mt-0 flex space-x-3">
                <a href="{{ url_for('dashboard.create_app') }}" class="btn-primary">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Create App
                </a>
            </div>
        </div>
    </div>
    
    <!-- Stats Overview -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="glass-card p-6 rounded-xl">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-blue-500/20 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-300">Total Apps</p>
                    <p class="text-2xl font-semibold text-white">{{ apps|length }}</p>
                </div>
            </div>
        </div>
        
        <div class="glass-card p-6 rounded-xl">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-green-500/20 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-300">Active Apps</p>
                    <p class="text-2xl font-semibold text-white">{{ active_apps_count }}</p>
                </div>
            </div>
        </div>
        
        <div class="glass-card p-6 rounded-xl">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-pepe-green/20 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-pepe-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m0 0a2 2 0 012 2m-2-2a2 2 0 00-2 2m2-2V5a2 2 0 00-2-2m0 0H9a2 2 0 00-2 2v0a2 2 0 002 2h2m-4 4v6a2 2 0 002 2h2a2 2 0 002-2v-6m-4 0a2 2 0 012-2h0a2 2 0 012 2v0"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-300">Total Keys</p>
                    <p class="text-2xl font-semibold text-white">{{ total_keys }}</p>
                </div>
            </div>
        </div>
        
        <div class="glass-card p-6 rounded-xl">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-yellow-500/20 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-300">With Webhooks</p>
                    <p class="text-2xl font-semibold text-white">{{ webhook_apps_count }}</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Search and Filter -->
    <div class="glass-card p-6 rounded-xl mb-8">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
            <div class="flex-1 max-w-lg">
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </div>
                    <input type="text" 
                           class="input-field pl-10" 
                           placeholder="Search applications..."
                           x-data
                           x-on:input="filterApps($event.target.value)">
                </div>
            </div>
            <div class="flex items-center space-x-4">
                <select class="input-field w-auto" x-data x-on:change="filterByStatus($event.target.value)">
                    <option value="">All Status</option>
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                </select>
                <select class="input-field w-auto" x-data x-on:change="filterByHwid($event.target.value)">
                    <option value="">All HWID</option>
                    <option value="enabled">HWID Enabled</option>
                    <option value="disabled">HWID Disabled</option>
                </select>
            </div>
        </div>
    </div>
    
    <!-- Apps Grid -->
    {% if apps %}
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" id="apps-grid">
            {% for app in apps %}
                <div class="glass-card p-6 rounded-xl card-hover app-card" 
                     data-name="{{ app.name.lower() }}"
                     data-status="{{ 'active' if app.is_active else 'inactive' }}"
                     data-hwid="{{ 'enabled' if app.hwid_lock else 'disabled' }}">
                    
                    <!-- App Header -->
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-white truncate">{{ app.name }}</h3>
                        <div class="flex items-center space-x-2">
                            {% if app.hwid_lock %}
                                <div class="w-2 h-2 bg-yellow-500 rounded-full tooltip" data-tooltip="HWID Lock Enabled"></div>
                            {% endif %}
                            {% if app.webhook_url %}
                                <div class="w-2 h-2 bg-blue-500 rounded-full tooltip" data-tooltip="Webhook Configured"></div>
                            {% endif %}
                            {% if app.is_active %}
                                <div class="w-2 h-2 bg-pepe-green rounded-full tooltip" data-tooltip="Active"></div>
                            {% else %}
                                <div class="w-2 h-2 bg-red-500 rounded-full tooltip" data-tooltip="Inactive"></div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- App Stats -->
                    <div class="space-y-3 mb-6">
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-400">Total Keys:</span>
                            <span class="text-white font-medium">{{ app.key_count }}</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-400">Used Keys:</span>
                            <span class="text-white font-medium">{{ app.used_keys }}</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-400">Success Rate:</span>
                            <span class="text-white font-medium">
                                {% if app.total_auths > 0 %}
                                    {{ ((app.successful_auths / app.total_auths) * 100) | round(1) }}%
                                {% else %}
                                    N/A
                                {% endif %}
                            </span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-400">Created:</span>
                            <span class="text-white font-medium">{{ app.created_at.strftime('%Y-%m-%d') }}</span>
                        </div>
                    </div>
                    
                    <!-- App Secret (Masked) -->
                    <div class="mb-6">
                        <label class="text-xs text-gray-400 uppercase tracking-wide">App Secret</label>
                        <div class="flex items-center mt-1">
                            <code class="code-block text-xs flex-1 mr-2 secret-display" data-secret="{{ app.secret }}">
                                {{ app.secret[:8] }}...{{ app.secret[-4:] }}
                            </code>
                            <button type="button" 
                                    class="text-gray-400 hover:text-white transition-colors copy-secret"
                                    data-secret="{{ app.secret }}"
                                    title="Copy Secret">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                </svg>
                            </button>
                            <button type="button" 
                                    class="ml-2 text-gray-400 hover:text-white transition-colors toggle-secret"
                                    title="Show/Hide Secret">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                    
                    <!-- Action Buttons -->
                    <div class="flex space-x-2">
                        <a href="{{ url_for('dashboard.app_detail', app_id=app.id) }}" 
                           class="btn-primary text-sm flex-1 text-center">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                            Manage
                        </a>
                        <a href="{{ url_for('dashboard.generate_keys', app_id=app.id) }}" 
                           class="btn-secondary text-sm px-3">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m0 0a2 2 0 012 2m-2-2a2 2 0 00-2 2m2-2V5a2 2 0 00-2-2m0 0H9a2 2 0 00-2 2v0a2 2 0 002 2h2m-4 4v6a2 2 0 002 2h2a2 2 0 002-2v-6m-4 0a2 2 0 012-2h0a2 2 0 012 2v0"></path>
                            </svg>
                        </a>
                        <div class="relative" x-data="{ open: false }">
                            <button @click="open = !open" 
                                    class="btn-secondary text-sm px-3">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"></path>
                                </svg>
                            </button>
                            
                            <div x-show="open" @click.away="open = false" 
                                 class="absolute right-0 mt-2 w-48 glass-card rounded-lg shadow-lg py-1 z-50">
                                <a href="{{ url_for('dashboard.edit_app', app_id=app.id) }}" 
                                   class="block px-4 py-2 text-sm text-white hover:bg-pepe-green/20 rounded-lg mx-1">
                                    Edit App
                                </a>
                                <a href="{{ url_for('dashboard.app_webhooks', app_id=app.id) }}" 
                                   class="block px-4 py-2 text-sm text-white hover:bg-pepe-green/20 rounded-lg mx-1">
                                    Webhooks
                                </a>
                                <button onclick="regenerateSecret({{ app.id }})" 
                                        class="block w-full text-left px-4 py-2 text-sm text-white hover:bg-yellow-500/20 rounded-lg mx-1">
                                    Regenerate Secret
                                </button>
                                {% if app.is_active %}
                                    <button onclick="toggleAppStatus({{ app.id }}, false)" 
                                            class="block w-full text-left px-4 py-2 text-sm text-white hover:bg-red-500/20 rounded-lg mx-1">
                                        Deactivate
                                    </button>
                                {% else %}
                                    <button onclick="toggleAppStatus({{ app.id }}, true)" 
                                            class="block w-full text-left px-4 py-2 text-sm text-white hover:bg-green-500/20 rounded-lg mx-1">
                                        Activate
                                    </button>
                                {% endif %}
                                <hr class="border-pepe-light-gray/20 my-1">
                                <button onclick="deleteApp({{ app.id }}, '{{ app.name }}')" 
                                        class="block w-full text-left px-4 py-2 text-sm text-red-400 hover:bg-red-500/20 rounded-lg mx-1">
                                    Delete App
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>
    {% else %}
        <!-- Empty State -->
        <div class="glass-card p-12 rounded-xl text-center">
            <div class="w-16 h-16 bg-pepe-gray rounded-full flex items-center justify-center mx-auto mb-4">
                <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                </svg>
            </div>
            <h3 class="text-lg font-medium text-white mb-2">No applications yet</h3>
            <p class="text-gray-300 mb-6">Create your first application to start managing license keys.</p>
            <a href="{{ url_for('dashboard.create_app') }}" class="btn-primary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                Create Your First App
            </a>
        </div>
    {% endif %}
</div>
{% endblock %}

{% block scripts %}
<script>
// Filter functions
function filterApps(searchTerm) {
    const cards = document.querySelectorAll('.app-card');
    cards.forEach(card => {
        const name = card.dataset.name;
        if (name.includes(searchTerm.toLowerCase())) {
            card.style.display = 'block';
        } else {
            card.style.display = 'none';
        }
    });
}

function filterByStatus(status) {
    const cards = document.querySelectorAll('.app-card');
    cards.forEach(card => {
        if (!status || card.dataset.status === status) {
            card.style.display = 'block';
        } else {
            card.style.display = 'none';
        }
    });
}

function filterByHwid(hwid) {
    const cards = document.querySelectorAll('.app-card');
    cards.forEach(card => {
        if (!hwid || card.dataset.hwid === hwid) {
            card.style.display = 'block';
        } else {
            card.style.display = 'none';
        }
    });
}

// Copy secret functionality
document.addEventListener('DOMContentLoaded', function() {
    document.querySelectorAll('.copy-secret').forEach(button => {
        button.addEventListener('click', function() {
            const secret = this.dataset.secret;
            navigator.clipboard.writeText(secret).then(() => {
                // Show success feedback
                const originalHTML = this.innerHTML;
                this.innerHTML = '<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>';
                this.classList.add('text-green-400');
                setTimeout(() => {
                    this.innerHTML = originalHTML;
                    this.classList.remove('text-green-400');
                }, 2000);
            });
        });
    });
    
    // Toggle secret visibility
    document.querySelectorAll('.toggle-secret').forEach(button => {
        button.addEventListener('click', function() {
            const secretDisplay = this.parentElement.querySelector('.secret-display');
            const fullSecret = secretDisplay.dataset.secret;
            const maskedSecret = fullSecret.substring(0, 8) + '...' + fullSecret.substring(fullSecret.length - 4);
            
            if (secretDisplay.textContent.includes('...')) {
                secretDisplay.textContent = fullSecret;
            } else {
                secretDisplay.textContent = maskedSecret;
            }
        });
    });
});

// App management functions
function regenerateSecret(appId) {
    if (confirm('Are you sure you want to regenerate the app secret? This will require updating your application code.')) {
        fetch(`/dashboard/app/${appId}/regenerate-secret`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('meta[name=csrf-token]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        });
    }
}

function toggleAppStatus(appId, activate) {
    const action = activate ? 'activate' : 'deactivate';
    if (confirm(`Are you sure you want to ${action} this application?`)) {
        fetch(`/dashboard/app/${appId}/toggle-status`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('meta[name=csrf-token]').getAttribute('content')
            },
            body: JSON.stringify({ active: activate })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        });
    }
}

function deleteApp(appId, appName) {
    const userInput = prompt(`Are you sure you want to delete "${appName}"? This action cannot be undone and will delete all associated license keys.\n\nType "DELETE" to confirm:`);
    if (userInput === 'DELETE') {
        fetch(`/dashboard/app/${appId}/delete`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('meta[name=csrf-token]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        });
    }
}
</script>
{% endblock %}
