{% extends "base.html" %}

{% block title %}Generate Keys - {{ app.name }} - PepeAuth{% endblock %}

{% block content %}
<div class="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center space-x-4 mb-4">
            <a href="{{ url_for('dashboard.app_detail', app_id=app.id) }}" class="text-gray-400 hover:text-white">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
            </a>
            <h1 class="text-3xl font-bold text-white">Generate License Keys</h1>
        </div>
        <p class="text-gray-300">Generate new license keys for <strong>{{ app.name }}</strong></p>
    </div>
    
    <!-- Form -->
    <div class="glass-card p-8 rounded-2xl">
        <form method="POST" class="space-y-6">
            {{ form.hidden_tag() }}
            
            <div>
                {{ form.quantity.label(class="block text-sm font-medium text-white mb-2") }}
                {{ form.quantity(class="input-field") }}
                {% if form.quantity.errors %}
                    <div class="mt-1 text-sm text-red-400">
                        {% for error in form.quantity.errors %}
                            <p>{{ error }}</p>
                        {% endfor %}
                    </div>
                {% endif %}
                <p class="mt-1 text-sm text-gray-400">Number of license keys to generate (1-100)</p>
            </div>
            
            <div>
                {{ form.note.label(class="block text-sm font-medium text-white mb-2") }}
                {{ form.note(class="input-field", rows="3", placeholder="Optional note for these keys...") }}
                {% if form.note.errors %}
                    <div class="mt-1 text-sm text-red-400">
                        {% for error in form.note.errors %}
                            <p>{{ error }}</p>
                        {% endfor %}
                    </div>
                {% endif %}
                <p class="mt-1 text-sm text-gray-400">{{ form.note.description }}</p>
            </div>
            
            <div class="flex space-x-4">
                <button type="submit" class="btn-primary flex-1">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m0 0a2 2 0 012 2m-2-2a2 2 0 00-2 2m2-2V5a2 2 0 00-2-2m0 0H9a2 2 0 00-2 2v0a2 2 0 002 2h2m-4 4v6a2 2 0 002 2h2a2 2 0 002-2v-6m-4 0a2 2 0 012-2h0a2 2 0 012 2v0"></path>
                    </svg>
                    Generate Keys
                </button>
                <a href="{{ url_for('dashboard.app_detail', app_id=app.id) }}" class="btn-secondary flex-1 text-center">
                    Cancel
                </a>
            </div>
        </form>
    </div>
    
    <!-- App Info -->
    <div class="glass-card p-6 rounded-xl mt-8">
        <h3 class="text-lg font-semibold text-white mb-4">App Configuration</h3>
        <div class="grid grid-cols-2 gap-4 text-sm">
            <div>
                <span class="text-gray-300">HWID Lock:</span>
                <span class="text-white ml-2">{{ 'Enabled' if app.hwid_lock else 'Disabled' }}</span>
            </div>
            <div>
                <span class="text-gray-300">License Expiry:</span>
                <span class="text-white ml-2">{{ app.expiry_days }} days</span>
            </div>
        </div>
        
        {% if app.hwid_lock %}
            <div class="mt-4 p-3 bg-yellow-500/10 border border-yellow-500/20 rounded-lg">
                <div class="flex items-center">
                    <svg class="w-5 h-5 text-yellow-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                    </svg>
                    <span class="text-yellow-400 text-sm font-medium">HWID Lock Enabled</span>
                </div>
                <p class="text-yellow-300 text-sm mt-1">
                    Generated keys will be bound to the first hardware ID that activates them.
                </p>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}
