{% extends "base.html" %}

{% block title %}Edit Plan - Admin - PepeAuth{% endblock %}

{% block content %}
<div class="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center space-x-4 mb-4">
            <a href="{{ url_for('admin.plans') }}" class="text-gray-400 hover:text-white">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
            </a>
            <h1 class="text-3xl font-bold text-white">Edit Plan</h1>
        </div>
        <p class="text-gray-300">Modify plan settings and limits</p>
    </div>
    
    <!-- Plan Info -->
    <div class="glass-card p-6 rounded-xl mb-8">
        <h3 class="text-lg font-semibold text-white mb-4">Current Plan: {{ plan.name }}</h3>
        <div class="grid grid-cols-2 gap-4 text-sm">
            <div>
                <span class="text-gray-300">Max Apps:</span>
                <span class="text-white ml-2">{{ plan.max_apps if plan.max_apps != -1 else 'Unlimited' }}</span>
            </div>
            <div>
                <span class="text-gray-300">Max Keys:</span>
                <span class="text-white ml-2">{{ plan.max_keys if plan.max_keys != -1 else 'Unlimited' }}</span>
            </div>
            <div>
                <span class="text-gray-300">Rate Limit:</span>
                <span class="text-white ml-2">{{ plan.rate_limit if plan.rate_limit != -1 else 'Unlimited' }}/day</span>
            </div>
            <div>
                <span class="text-gray-300">Plan ID:</span>
                <span class="text-white ml-2">{{ plan.id }}</span>
            </div>
        </div>
    </div>
    
    <!-- Edit Form -->
    <div class="glass-card p-8 rounded-2xl">
        <form method="POST" class="space-y-6">
            {{ form.hidden_tag() }}
            
            <div>
                {{ form.name.label(class="block text-sm font-medium text-white mb-2") }}
                {{ form.name(class="input-field") }}
                {% if form.name.errors %}
                    <div class="mt-1 text-sm text-red-400">
                        {% for error in form.name.errors %}
                            <p>{{ error }}</p>
                        {% endfor %}
                    </div>
                {% endif %}
                <p class="mt-1 text-sm text-gray-400">Plan name must be unique</p>
            </div>
            
            <div>
                {{ form.max_apps.label(class="block text-sm font-medium text-white mb-2") }}
                {{ form.max_apps(class="input-field") }}
                {% if form.max_apps.errors %}
                    <div class="mt-1 text-sm text-red-400">
                        {% for error in form.max_apps.errors %}
                            <p>{{ error }}</p>
                        {% endfor %}
                    </div>
                {% endif %}
                <p class="mt-1 text-sm text-gray-400">{{ form.max_apps.description }}</p>
            </div>
            
            <div>
                {{ form.max_keys.label(class="block text-sm font-medium text-white mb-2") }}
                {{ form.max_keys(class="input-field") }}
                {% if form.max_keys.errors %}
                    <div class="mt-1 text-sm text-red-400">
                        {% for error in form.max_keys.errors %}
                            <p>{{ error }}</p>
                        {% endfor %}
                    </div>
                {% endif %}
                <p class="mt-1 text-sm text-gray-400">{{ form.max_keys.description }}</p>
            </div>
            
            <div>
                {{ form.rate_limit.label(class="block text-sm font-medium text-white mb-2") }}
                {{ form.rate_limit(class="input-field") }}
                {% if form.rate_limit.errors %}
                    <div class="mt-1 text-sm text-red-400">
                        {% for error in form.rate_limit.errors %}
                            <p>{{ error }}</p>
                        {% endfor %}
                    </div>
                {% endif %}
                <p class="mt-1 text-sm text-gray-400">{{ form.rate_limit.description }}</p>
            </div>
            
            <div class="flex space-x-4">
                <button type="submit" class="btn-primary flex-1">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Save Changes
                </button>
                <a href="{{ url_for('admin.plans') }}" class="btn-secondary flex-1 text-center">
                    Cancel
                </a>
            </div>
        </form>
    </div>
    
    <!-- Warning -->
    <div class="glass-card p-6 rounded-xl mt-8 border-l-4 border-yellow-500">
        <div class="flex items-start">
            <svg class="w-6 h-6 text-yellow-400 mr-3 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
            </svg>
            <div>
                <h4 class="text-lg font-semibold text-yellow-400 mb-2">Important Notes</h4>
                <ul class="text-yellow-300 text-sm space-y-1">
                    <li>• Changes will affect all users currently on this plan</li>
                    <li>• Reducing limits may prevent users from creating new content</li>
                    <li>• Use -1 for unlimited values</li>
                    <li>• Plan changes take effect immediately</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}
