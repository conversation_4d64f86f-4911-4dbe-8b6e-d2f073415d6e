#!/usr/bin/env python3
"""
PepeAuth - Modern License Management Platform
Entry point for the Flask application
"""

import os
import sys
from app import create_app

def main():
    """Main entry point for the application"""
    # Create Flask app
    app = create_app()

    # Get configuration from environment
    host = os.environ.get('FLASK_HOST', '127.0.0.1')
    port = int(os.environ.get('FLASK_PORT', 5000))
    debug = os.environ.get('FLASK_DEBUG', 'False').lower() == 'true'

    # Print startup information
    print("🐸 PepeAuth License Management Platform")
    print("=" * 50)
    print(f"Environment: {'Development' if debug else 'Production'}")
    print(f"Host: {host}")
    print(f"Port: {port}")
    print(f"Debug: {debug}")
    print("=" * 50)

    if debug:
        print("⚠️  Debug mode is enabled. Do not use in production!")
        print("📝 Database will be initialized automatically")
        print("🔧 Hot reload is enabled")

    print(f"🚀 Starting server at http://{host}:{port}")
    print("Press Ctrl+C to stop the server")
    print()

    try:
        # Run the Flask development server
        app.run(
            host=host,
            port=port,
            debug=debug,
            threaded=True
        )
    except KeyboardInterrupt:
        print("\n👋 Server stopped by user")
        sys.exit(0)
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()