#!/usr/bin/env python3
"""
Database migration script for PepeAuth
Handles schema updates and data migrations
"""

import sqlite3
import os
import sys
from datetime import datetime

def get_db_connection():
    """Get database connection"""
    db_path = 'pepeauth.db'
    if not os.path.exists(db_path):
        print(f"Database {db_path} does not exist!")
        return None
    
    conn = sqlite3.connect(db_path)
    conn.row_factory = sqlite3.Row
    return conn

def check_column_exists(conn, table, column):
    """Check if a column exists in a table"""
    cursor = conn.cursor()
    cursor.execute(f"PRAGMA table_info({table})")
    columns = [row[1] for row in cursor.fetchall()]
    return column in columns

def add_missing_columns(conn):
    """Add missing columns to existing tables"""
    migrations = []
    
    # Check and add missing columns to plan table
    if not check_column_exists(conn, 'plan', 'price'):
        migrations.append("ALTER TABLE plan ADD COLUMN price DECIMAL(10,2) DEFAULT 0.00")
    
    if not check_column_exists(conn, 'plan', 'duration_days'):
        migrations.append("ALTER TABLE plan ADD COLUMN duration_days INTEGER NOT NULL DEFAULT 30")
    
    if not check_column_exists(conn, 'plan', 'features'):
        migrations.append("ALTER TABLE plan ADD COLUMN features TEXT")
    
    # Check and add missing columns to user table
    if not check_column_exists(conn, 'user', 'hwid_reset_count'):
        migrations.append("ALTER TABLE user ADD COLUMN hwid_reset_count INTEGER NOT NULL DEFAULT 0")
    
    if not check_column_exists(conn, 'user', 'max_hwid_resets'):
        migrations.append("ALTER TABLE user ADD COLUMN max_hwid_resets INTEGER NOT NULL DEFAULT 3")
    
    if not check_column_exists(conn, 'user', 'last_login'):
        migrations.append("ALTER TABLE user ADD COLUMN last_login DATETIME")
    
    if not check_column_exists(conn, 'user', 'last_ip'):
        migrations.append("ALTER TABLE user ADD COLUMN last_ip TEXT")
    
    # Check and add missing columns to app table
    if not check_column_exists(conn, 'app', 'webhook_url'):
        migrations.append("ALTER TABLE app ADD COLUMN webhook_url TEXT")
    
    if not check_column_exists(conn, 'app', 'webhook_secret'):
        migrations.append("ALTER TABLE app ADD COLUMN webhook_secret TEXT")
    
    if not check_column_exists(conn, 'app', 'is_active'):
        migrations.append("ALTER TABLE app ADD COLUMN is_active INTEGER NOT NULL DEFAULT 1")
    
    # Check and add missing columns to license_key table
    if not check_column_exists(conn, 'license_key', 'is_banned'):
        migrations.append("ALTER TABLE license_key ADD COLUMN is_banned INTEGER NOT NULL DEFAULT 0")
    
    if not check_column_exists(conn, 'license_key', 'ban_reason'):
        migrations.append("ALTER TABLE license_key ADD COLUMN ban_reason TEXT")
    
    if not check_column_exists(conn, 'license_key', 'user_id'):
        migrations.append("ALTER TABLE license_key ADD COLUMN user_id INTEGER")
    
    # Execute migrations
    for migration in migrations:
        try:
            print(f"Executing: {migration}")
            conn.execute(migration)
            conn.commit()
            print("✅ Success")
        except sqlite3.Error as e:
            print(f"❌ Error: {e}")
    
    return len(migrations)

def create_missing_tables(conn):
    """Create any missing tables"""
    tables_created = 0
    
    # Check if auth_log table exists
    cursor = conn.cursor()
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='auth_log'")
    if not cursor.fetchone():
        print("Creating auth_log table...")
        conn.execute('''
            CREATE TABLE auth_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                license_key_id INTEGER,
                app_id INTEGER,
                user_id INTEGER,
                hwid TEXT,
                ip_address TEXT,
                user_agent TEXT,
                success INTEGER NOT NULL,
                failure_reason TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (license_key_id) REFERENCES license_key(id),
                FOREIGN KEY (app_id) REFERENCES app(id),
                FOREIGN KEY (user_id) REFERENCES user(id)
            )
        ''')
        tables_created += 1
    
    # Check if system_log table exists
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='system_log'")
    if not cursor.fetchone():
        print("Creating system_log table...")
        conn.execute('''
            CREATE TABLE system_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                level TEXT NOT NULL,
                category TEXT NOT NULL,
                message TEXT NOT NULL,
                user_id INTEGER,
                app_id INTEGER,
                ip_address TEXT,
                extra_data TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES user(id),
                FOREIGN KEY (app_id) REFERENCES app(id)
            )
        ''')
        tables_created += 1
    
    # Check if hwid_reset_log table exists
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='hwid_reset_log'")
    if not cursor.fetchone():
        print("Creating hwid_reset_log table...")
        conn.execute('''
            CREATE TABLE hwid_reset_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                license_key_id INTEGER NOT NULL,
                old_hwid TEXT,
                new_hwid TEXT,
                reset_by INTEGER,
                reason TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES user(id),
                FOREIGN KEY (license_key_id) REFERENCES license_key(id),
                FOREIGN KEY (reset_by) REFERENCES user(id)
            )
        ''')
        tables_created += 1
    
    # Check if webhook_log table exists
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='webhook_log'")
    if not cursor.fetchone():
        print("Creating webhook_log table...")
        conn.execute('''
            CREATE TABLE webhook_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                app_id INTEGER NOT NULL,
                event_type TEXT NOT NULL,
                payload TEXT,
                response_code INTEGER,
                response_body TEXT,
                success INTEGER NOT NULL DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (app_id) REFERENCES app(id)
            )
        ''')
        tables_created += 1
    
    # Check if rate_limit_log table exists
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='rate_limit_log'")
    if not cursor.fetchone():
        print("Creating rate_limit_log table...")
        conn.execute('''
            CREATE TABLE rate_limit_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                identifier TEXT NOT NULL,
                endpoint TEXT NOT NULL,
                requests_count INTEGER NOT NULL DEFAULT 1,
                window_start DATETIME NOT NULL,
                blocked INTEGER NOT NULL DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        tables_created += 1
    
    if tables_created > 0:
        conn.commit()
    
    return tables_created

def update_plan_data(conn):
    """Update plan data with new fields"""
    print("Updating plan data...")
    
    # Update existing plans with new data
    plans_data = [
        (0.00, 7, '["Basic API Access", "HWID Binding", "Email Support"]', 'Trial'),
        (9.99, 30, '["Full API Access", "HWID Binding", "Webhooks", "Priority Support"]', 'Premium'),
        (49.99, -1, '["Unlimited Everything", "Priority Support", "Custom Features"]', 'Lifetime')
    ]
    
    for price, duration, features, name in plans_data:
        try:
            conn.execute('''
                UPDATE plan 
                SET price = ?, duration_days = ?, features = ?
                WHERE name = ?
            ''', (price, duration, features, name))
        except sqlite3.Error as e:
            print(f"Error updating plan {name}: {e}")
    
    conn.commit()

def main():
    """Main migration function"""
    print("🔄 PepeAuth Database Migration")
    print("=" * 40)
    
    # Connect to database
    conn = get_db_connection()
    if not conn:
        sys.exit(1)
    
    try:
        # Add missing columns
        columns_added = add_missing_columns(conn)
        print(f"✅ Added {columns_added} missing columns")
        
        # Create missing tables
        tables_created = create_missing_tables(conn)
        print(f"✅ Created {tables_created} missing tables")
        
        # Update plan data
        update_plan_data(conn)
        print("✅ Updated plan data")
        
        print("\n🎉 Migration completed successfully!")
        
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        conn.rollback()
        sys.exit(1)
    
    finally:
        conn.close()

if __name__ == "__main__":
    main()
