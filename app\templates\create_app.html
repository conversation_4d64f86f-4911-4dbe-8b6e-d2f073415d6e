{% extends "base.html" %}

{% block title %}Create App - PepeAuth{% endblock %}

{% block content %}
<div class="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center space-x-4 mb-4">
            <a href="{{ url_for('dashboard.index') }}" class="text-gray-400 hover:text-white">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
            </a>
            <h1 class="text-3xl font-bold text-white">Create New App</h1>
        </div>
        <p class="text-gray-300">Set up a new application to manage license keys</p>
    </div>
    
    <!-- Form -->
    <div class="glass-card p-8 rounded-2xl">
        <form method="POST" class="space-y-6">
            {{ form.hidden_tag() }}
            
            <div>
                {{ form.name.label(class="block text-sm font-medium text-white mb-2") }}
                {{ form.name(class="input-field", placeholder="My Awesome App") }}
                {% if form.name.errors %}
                    <div class="mt-1 text-sm text-red-400">
                        {% for error in form.name.errors %}
                            <p>{{ error }}</p>
                        {% endfor %}
                    </div>
                {% endif %}
                <p class="mt-1 text-sm text-gray-400">Choose a unique name for your application</p>
            </div>
            
            <div>
                {{ form.expiry_days.label(class="block text-sm font-medium text-white mb-2") }}
                {{ form.expiry_days(class="input-field") }}
                {% if form.expiry_days.errors %}
                    <div class="mt-1 text-sm text-red-400">
                        {% for error in form.expiry_days.errors %}
                            <p>{{ error }}</p>
                        {% endfor %}
                    </div>
                {% endif %}
                <p class="mt-1 text-sm text-gray-400">Number of days before license keys expire after activation</p>
            </div>
            
            <div class="flex items-start">
                <div class="flex items-center h-5">
                    {{ form.hwid_lock(class="h-4 w-4 text-pepe-green focus:ring-pepe-green border-gray-600 rounded bg-pepe-gray") }}
                </div>
                <div class="ml-3">
                    {{ form.hwid_lock.label(class="text-sm font-medium text-white") }}
                    <p class="text-sm text-gray-400">Bind license keys to specific hardware IDs for enhanced security</p>
                </div>
            </div>
            
            <div class="flex space-x-4">
                <button type="submit" class="btn-primary flex-1">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Create App
                </button>
                <a href="{{ url_for('dashboard.index') }}" class="btn-secondary flex-1 text-center">
                    Cancel
                </a>
            </div>
        </form>
    </div>
    
    <!-- Info Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-8">
        <div class="glass-card p-6 rounded-xl">
            <h3 class="text-lg font-semibold text-white mb-2">🔐 Security Features</h3>
            <ul class="text-sm text-gray-300 space-y-1">
                <li>• Unique app secret for API authentication</li>
                <li>• Optional HWID binding for device locking</li>
                <li>• Configurable license expiry periods</li>
                <li>• Rate limiting based on your plan</li>
            </ul>
        </div>
        
        <div class="glass-card p-6 rounded-xl">
            <h3 class="text-lg font-semibold text-white mb-2">📊 What You Get</h3>
            <ul class="text-sm text-gray-300 space-y-1">
                <li>• License key generation and management</li>
                <li>• Real-time usage analytics</li>
                <li>• API endpoints for validation</li>
                <li>• Detailed activity logs</li>
            </ul>
        </div>
    </div>
</div>
{% endblock %}
