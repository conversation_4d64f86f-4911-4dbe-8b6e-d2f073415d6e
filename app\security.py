"""
Security utilities for PepeAuth
Includes rate limiting, ban system, HWID management, and security logging
"""

import hashlib
import hmac
import secrets
import time
from datetime import datetime, timedelta
from functools import wraps
from flask import request, jsonify, current_app, g
from app.database import query_db, execute_db
import logging

# Configure security logger
security_logger = logging.getLogger('pepeauth.security')
security_logger.setLevel(logging.INFO)

class SecurityManager:
    """Centralized security management"""
    
    @staticmethod
    def generate_secure_token(length=32):
        """Generate a cryptographically secure random token"""
        return secrets.token_urlsafe(length)
    
    @staticmethod
    def generate_hmac_signature(secret, message):
        """Generate HMAC-SHA256 signature"""
        return hmac.new(
            secret.encode('utf-8'),
            message.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
    
    @staticmethod
    def verify_hmac_signature(secret, message, signature):
        """Verify HMAC-SHA256 signature"""
        expected = SecurityManager.generate_hmac_signature(secret, message)
        return hmac.compare_digest(expected, signature)
    
    @staticmethod
    def hash_password(password):
        """Hash password using bcrypt"""
        import bcrypt
        return bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
    
    @staticmethod
    def verify_password(password, hashed):
        """Verify password against bcrypt hash"""
        import bcrypt
        return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))
    
    @staticmethod
    def sanitize_input(text, max_length=1000):
        """Sanitize user input"""
        if not text:
            return ""
        
        # Remove null bytes and control characters
        text = ''.join(char for char in text if ord(char) >= 32 or char in '\n\r\t')
        
        # Limit length
        return text[:max_length]
    
    @staticmethod
    def is_valid_email(email):
        """Basic email validation"""
        import re
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, email) is not None
    
    @staticmethod
    def get_client_ip():
        """Get real client IP address"""
        # Check for forwarded IP first
        forwarded_ips = request.headers.get('X-Forwarded-For')
        if forwarded_ips:
            return forwarded_ips.split(',')[0].strip()
        
        # Check other common headers
        real_ip = request.headers.get('X-Real-IP')
        if real_ip:
            return real_ip
        
        # Fall back to remote address
        return request.remote_addr or 'unknown'

class RateLimiter:
    """Rate limiting implementation"""
    
    @staticmethod
    def is_rate_limited(identifier, endpoint, limit=100, window=3600):
        """
        Check if identifier is rate limited for endpoint
        
        Args:
            identifier: IP address or user ID
            endpoint: API endpoint name
            limit: Maximum requests per window
            window: Time window in seconds
            
        Returns:
            True if rate limited, False otherwise
        """
        now = datetime.now()
        window_start = now - timedelta(seconds=window)
        
        # Clean old entries
        execute_db(
            'DELETE FROM rate_limit_log WHERE window_start < ?',
            (window_start.isoformat(),)
        )
        
        # Get current count
        current_count = query_db(
            '''SELECT SUM(requests_count) as total 
               FROM rate_limit_log 
               WHERE identifier = ? AND endpoint = ? AND window_start >= ?''',
            (identifier, endpoint, window_start.isoformat()),
            one=True
        )
        
        total_requests = current_count['total'] or 0
        
        if total_requests >= limit:
            # Log rate limit hit
            execute_db(
                '''INSERT INTO rate_limit_log (identifier, endpoint, requests_count, window_start, blocked, created_at)
                   VALUES (?, ?, ?, ?, ?, ?)''',
                (identifier, endpoint, 1, now.isoformat(), 1, now.isoformat())
            )
            
            security_logger.warning(f'Rate limit exceeded: {identifier} on {endpoint}')
            return True
        
        # Log request
        execute_db(
            '''INSERT INTO rate_limit_log (identifier, endpoint, requests_count, window_start, blocked, created_at)
               VALUES (?, ?, ?, ?, ?, ?)''',
            (identifier, endpoint, 1, now.isoformat(), 0, now.isoformat())
        )
        
        return False
    
    @staticmethod
    def get_rate_limit_info(identifier, endpoint, limit=100, window=3600):
        """Get rate limit information for identifier"""
        now = datetime.now()
        window_start = now - timedelta(seconds=window)
        
        current_count = query_db(
            '''SELECT SUM(requests_count) as total 
               FROM rate_limit_log 
               WHERE identifier = ? AND endpoint = ? AND window_start >= ?''',
            (identifier, endpoint, window_start.isoformat()),
            one=True
        )
        
        total_requests = current_count['total'] or 0
        remaining = max(0, limit - total_requests)
        reset_time = int((now + timedelta(seconds=window)).timestamp())
        
        return {
            'limit': limit,
            'remaining': remaining,
            'reset': reset_time,
            'window': window
        }

class BanSystem:
    """User and IP banning system"""
    
    @staticmethod
    def is_user_banned(user_id):
        """Check if user is banned"""
        user = query_db(
            'SELECT is_banned, ban_reason FROM user WHERE id = ?',
            (user_id,),
            one=True
        )
        return user and user['is_banned']
    
    @staticmethod
    def is_ip_banned(ip_address):
        """Check if IP is banned (can be extended with IP ban table)"""
        # For now, check if any user from this IP is banned
        banned_count = query_db(
            '''SELECT COUNT(*) as count FROM auth_log al
               JOIN user u ON al.user_id = u.id
               WHERE al.ip_address = ? AND u.is_banned = 1
               AND al.created_at > datetime('now', '-24 hours')''',
            (ip_address,),
            one=True
        )
        
        return banned_count and banned_count['count'] > 10  # Threshold for IP ban
    
    @staticmethod
    def ban_user(user_id, reason, banned_by=None):
        """Ban a user"""
        execute_db(
            'UPDATE user SET is_banned = 1, ban_reason = ? WHERE id = ?',
            (reason, user_id)
        )
        
        # Log the ban
        execute_db(
            '''INSERT INTO system_log (level, category, message, user_id, created_at)
               VALUES (?, ?, ?, ?, ?)''',
            ('WARNING', 'BAN', f'User banned: {reason}', user_id, datetime.now().isoformat())
        )
        
        security_logger.warning(f'User {user_id} banned: {reason}')
    
    @staticmethod
    def unban_user(user_id, unbanned_by=None):
        """Unban a user"""
        execute_db(
            'UPDATE user SET is_banned = 0, ban_reason = NULL WHERE id = ?',
            (user_id,)
        )
        
        # Log the unban
        execute_db(
            '''INSERT INTO system_log (level, category, message, user_id, created_at)
               VALUES (?, ?, ?, ?, ?)''',
            ('INFO', 'UNBAN', 'User unbanned', user_id, datetime.now().isoformat())
        )
        
        security_logger.info(f'User {user_id} unbanned')

class HWIDManager:
    """Hardware ID management and validation"""
    
    @staticmethod
    def is_hwid_valid(hwid):
        """Validate HWID format"""
        if not hwid or len(hwid) < 16 or len(hwid) > 64:
            return False
        
        # Check for valid characters (alphanumeric and some special chars)
        import re
        return re.match(r'^[a-zA-Z0-9\-_]+$', hwid) is not None
    
    @staticmethod
    def can_reset_hwid(user_id):
        """Check if user can reset HWID"""
        user = query_db(
            'SELECT hwid_reset_count, max_hwid_resets FROM user WHERE id = ?',
            (user_id,),
            one=True
        )
        
        if not user:
            return False
        
        return user['hwid_reset_count'] < user['max_hwid_resets']
    
    @staticmethod
    def reset_hwid(license_key_id, old_hwid, new_hwid, user_id=None, reset_by=None, reason="User requested"):
        """Reset HWID for a license key"""
        if not HWIDManager.is_hwid_valid(new_hwid):
            raise ValueError("Invalid HWID format")
        
        # Update license key
        execute_db(
            'UPDATE license_key SET hwid = ? WHERE id = ?',
            (new_hwid, license_key_id)
        )
        
        # Log the reset
        execute_db(
            '''INSERT INTO hwid_reset_log (user_id, license_key_id, old_hwid, new_hwid, reset_by, reason, created_at)
               VALUES (?, ?, ?, ?, ?, ?, ?)''',
            (user_id, license_key_id, old_hwid, new_hwid, reset_by, reason, datetime.now().isoformat())
        )
        
        # Increment user's reset count
        if user_id:
            execute_db(
                'UPDATE user SET hwid_reset_count = hwid_reset_count + 1 WHERE id = ?',
                (user_id,)
            )
        
        security_logger.info(f'HWID reset for key {license_key_id}: {old_hwid} -> {new_hwid}')

class SecurityLogger:
    """Security event logging"""
    
    @staticmethod
    def log_auth_attempt(license_key_id, app_id, user_id, hwid, ip_address, user_agent, success, failure_reason=None):
        """Log authentication attempt"""
        execute_db(
            '''INSERT INTO auth_log (license_key_id, app_id, user_id, hwid, ip_address, user_agent, success, failure_reason, created_at)
               VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)''',
            (license_key_id, app_id, user_id, hwid, ip_address, user_agent, success, failure_reason, datetime.now().isoformat())
        )
        
        if success:
            security_logger.info(f'Successful auth: key={license_key_id}, ip={ip_address}')
        else:
            security_logger.warning(f'Failed auth: key={license_key_id}, ip={ip_address}, reason={failure_reason}')
    
    @staticmethod
    def log_system_event(level, category, message, user_id=None, app_id=None, ip_address=None, extra_data=None):
        """Log system event"""
        execute_db(
            '''INSERT INTO system_log (level, category, message, user_id, app_id, ip_address, extra_data, created_at)
               VALUES (?, ?, ?, ?, ?, ?, ?, ?)''',
            (level, category, message, user_id, app_id, ip_address, extra_data, datetime.now().isoformat())
        )
        
        log_func = getattr(security_logger, level.lower(), security_logger.info)
        log_func(f'{category}: {message}')
    
    @staticmethod
    def log_suspicious_activity(description, user_id=None, ip_address=None, details=None):
        """Log suspicious activity"""
        SecurityLogger.log_system_event(
            'WARNING', 'SUSPICIOUS', description, user_id, None, ip_address, details
        )

# Decorators for security
def require_rate_limit(endpoint, limit=100, window=3600):
    """Decorator to enforce rate limiting"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            identifier = SecurityManager.get_client_ip()
            
            if RateLimiter.is_rate_limited(identifier, endpoint, limit, window):
                return jsonify({
                    'status': False,
                    'message': 'Rate limit exceeded. Please try again later.'
                }), 429
            
            # Add rate limit info to response headers
            rate_info = RateLimiter.get_rate_limit_info(identifier, endpoint, limit, window)
            g.rate_limit_info = rate_info
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def require_valid_hmac(secret_key_func):
    """Decorator to require valid HMAC signature"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            signature = request.headers.get('X-Signature')
            if not signature:
                return jsonify({
                    'status': False,
                    'message': 'Missing HMAC signature'
                }), 401
            
            secret = secret_key_func()
            if not secret:
                return jsonify({
                    'status': False,
                    'message': 'Invalid secret key'
                }), 401
            
            payload = request.get_data(as_text=True)
            if not SecurityManager.verify_hmac_signature(secret, payload, signature):
                SecurityLogger.log_suspicious_activity(
                    'Invalid HMAC signature',
                    ip_address=SecurityManager.get_client_ip(),
                    details=f'Endpoint: {request.endpoint}'
                )
                return jsonify({
                    'status': False,
                    'message': 'Invalid HMAC signature'
                }), 401
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def require_not_banned():
    """Decorator to check if user/IP is banned"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            ip_address = SecurityManager.get_client_ip()
            
            # Check IP ban
            if BanSystem.is_ip_banned(ip_address):
                SecurityLogger.log_suspicious_activity(
                    'Banned IP attempted access',
                    ip_address=ip_address
                )
                return jsonify({
                    'status': False,
                    'message': 'Access denied'
                }), 403
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator
