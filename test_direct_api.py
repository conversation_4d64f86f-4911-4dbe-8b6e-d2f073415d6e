#!/usr/bin/env python3
"""
Direct test of KeyAuth API endpoints using Flask test client
"""

from app import create_app
import json

def test_keyauth_endpoints():
    """Test KeyAuth endpoints directly"""
    app = create_app()
    
    with app.test_client() as client:
        print("🧪 Testing KeyAuth API Endpoints Directly")
        print("=" * 50)
        
        # Test login_key endpoint
        print("🔑 Testing /api/v1/login_key...")
        response = client.post('/api/v1/login_key', 
                             json={'key': 'INVALID-KEY-123'},
                             content_type='application/json')
        
        print(f"Status: {response.status_code}")
        print(f"Response: {response.get_json()}")
        
        if response.status_code == 403:
            print("✅ login_key endpoint working correctly")
        else:
            print("❌ login_key endpoint issue")
        
        # Test user_info endpoint
        print("\n👤 Testing /api/v1/user_info...")
        response = client.post('/api/v1/user_info', 
                             json={'session_token': 'invalid', 'key': 'invalid'},
                             content_type='application/json')
        
        print(f"Status: {response.status_code}")
        print(f"Response: {response.get_json()}")
        
        if response.status_code == 403:
            print("✅ user_info endpoint working correctly")
        else:
            print("❌ user_info endpoint issue")
        
        # Test admin endpoint
        print("\n👑 Testing /api/v1/admin/create_key...")
        response = client.post('/api/v1/admin/create_key', 
                             json={'count': 1, 'plan': 'Premium'},
                             content_type='application/json')
        
        print(f"Status: {response.status_code}")
        print(f"Response: {response.get_json()}")
        
        if response.status_code == 401:
            print("✅ admin endpoint properly protected")
        else:
            print("❌ admin endpoint security issue")
        
        print("\n" + "=" * 50)
        print("🎉 Direct API test completed!")

if __name__ == "__main__":
    test_keyauth_endpoints()
