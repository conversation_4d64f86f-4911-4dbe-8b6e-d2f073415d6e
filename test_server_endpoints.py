#!/usr/bin/env python3
"""
Test server endpoints by making HTTP requests to the running server
"""

import requests
import time

def test_server_endpoints():
    """Test the running server endpoints"""
    print("🌐 Testing Running Server Endpoints")
    print("=" * 50)
    
    base_url = "http://127.0.0.1:5000"
    
    # Test home page first
    print("🏠 Testing home page...")
    try:
        response = requests.get(f"{base_url}/", timeout=5)
        print(f"Home page status: {response.status_code}")
        if response.status_code == 200:
            print("✅ Server is responding")
        else:
            print("❌ Server not responding correctly")
            return
    except Exception as e:
        print(f"❌ Cannot connect to server: {e}")
        return
    
    # Test API endpoints
    endpoints_to_test = [
        "/api/v1/login_key",
        "/api/v1/user_info", 
        "/api/v1/admin/create_key"
    ]
    
    for endpoint in endpoints_to_test:
        print(f"\n🔍 Testing {endpoint}...")
        try:
            response = requests.post(
                f"{base_url}{endpoint}",
                json={"test": "data"},
                timeout=5
            )
            print(f"Status: {response.status_code}")
            
            if response.status_code == 404:
                print("❌ Endpoint not found!")
            else:
                print("✅ Endpoint exists")
                try:
                    data = response.json()
                    print(f"Response: {data}")
                except:
                    print(f"Raw response: {response.text[:100]}...")
        except Exception as e:
            print(f"❌ Error testing {endpoint}: {e}")

if __name__ == "__main__":
    test_server_endpoints()
