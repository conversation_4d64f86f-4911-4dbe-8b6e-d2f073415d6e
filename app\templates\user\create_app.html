{% extends "base.html" %}

{% block title %}Create App - PepeAuth{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center space-x-4 mb-4">
            <a href="{{ url_for('dashboard.index') }}" 
               class="text-gray-400 hover:text-white transition-colors">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
            </a>
            <div>
                <h1 class="text-3xl font-bold text-white">Create New Application</h1>
                <p class="mt-1 text-gray-300">Set up a new application to manage license keys</p>
            </div>
        </div>
        
        <!-- Plan Limits Info -->
        <div class="glass-card p-4 rounded-lg border-l-4 border-blue-500">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <svg class="w-5 h-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm text-white">
                        <span class="font-medium">{{ current_user.plan.name }} Plan:</span>
                        You can create 
                        {% if current_user.plan.max_apps == -1 %}
                            unlimited applications
                        {% else %}
                            up to {{ current_user.plan.max_apps }} applications ({{ stats.total_apps }}/{{ current_user.plan.max_apps }} used)
                        {% endif %}
                    </p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Create App Form -->
    <div class="glass-card p-8 rounded-xl">
        <form method="POST" class="space-y-6" x-data="{ 
            appName: '', 
            hwidLock: true, 
            expiryDays: 30,
            generateSecret: function() {
                // Generate a random secret for preview
                const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
                let result = '';
                for (let i = 0; i < 32; i++) {
                    result += chars.charAt(Math.floor(Math.random() * chars.length));
                }
                return result;
            }
        }">
            {{ form.hidden_tag() }}
            
            <!-- App Name -->
            <div class="form-group">
                <label for="name" class="form-label">
                    Application Name
                    <span class="text-red-400">*</span>
                </label>
                {{ form.name(class="input-field", placeholder="Enter your app name", x_model="appName") }}
                {% if form.name.errors %}
                    <div class="form-error">
                        {% for error in form.name.errors %}
                            <p>{{ error }}</p>
                        {% endfor %}
                    </div>
                {% endif %}
                <p class="mt-1 text-sm text-gray-400">
                    Choose a descriptive name for your application. This will be used to identify your app in the dashboard.
                </p>
            </div>
            
            <!-- HWID Lock -->
            <div class="form-group">
                <label class="form-label">Hardware ID (HWID) Lock</label>
                <div class="flex items-center space-x-3">
                    {{ form.hwid_lock(class="w-4 h-4 text-pepe-green bg-pepe-gray border-pepe-light-gray rounded focus:ring-pepe-green focus:ring-2", x_model="hwidLock") }}
                    <span class="text-white">Enable HWID binding for license keys</span>
                </div>
                <p class="mt-2 text-sm text-gray-400">
                    When enabled, license keys will be bound to the user's hardware ID, preventing sharing across multiple devices.
                </p>
            </div>
            
            <!-- Default Expiry Days -->
            <div class="form-group">
                <label for="expiry_days" class="form-label">
                    Default Key Expiry (Days)
                    <span class="text-red-400">*</span>
                </label>
                {{ form.expiry_days(class="input-field", x_model="expiryDays") }}
                {% if form.expiry_days.errors %}
                    <div class="form-error">
                        {% for error in form.expiry_days.errors %}
                            <p>{{ error }}</p>
                        {% endfor %}
                    </div>
                {% endif %}
                <p class="mt-1 text-sm text-gray-400">
                    Default expiration period for new license keys. You can override this when creating individual keys.
                </p>
            </div>
            
            <!-- App Secret Preview -->
            <div class="form-group">
                <label class="form-label">Application Secret</label>
                <div class="glass-card p-4 rounded-lg bg-pepe-darker/50">
                    <div class="flex items-center justify-between">
                        <div class="flex-1">
                            <p class="text-sm text-gray-300 mb-2">Your app secret will be automatically generated:</p>
                            <div class="code-block text-xs font-mono break-all">
                                <span x-text="generateSecret()"></span>
                            </div>
                        </div>
                        <div class="ml-4">
                            <svg class="w-8 h-8 text-pepe-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="mt-3 p-3 bg-yellow-500/10 border border-yellow-500/20 rounded-lg">
                        <div class="flex items-start">
                            <svg class="w-5 h-5 text-yellow-400 mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                            <div>
                                <p class="text-sm text-yellow-300 font-medium">Important Security Note</p>
                                <p class="text-xs text-yellow-200 mt-1">
                                    Keep your app secret secure! It's used to authenticate API requests and cannot be recovered if lost.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Preview Card -->
            <div class="form-group" x-show="appName.length > 0">
                <label class="form-label">Preview</label>
                <div class="glass-card p-6 rounded-lg border border-pepe-green/30">
                    <div class="flex items-center justify-between mb-4">
                        <h4 class="text-lg font-semibold text-white" x-text="appName || 'Your App Name'"></h4>
                        <div class="flex items-center space-x-2">
                            <div x-show="hwidLock" class="w-2 h-2 bg-yellow-500 rounded-full" title="HWID Lock Enabled"></div>
                            <div class="w-2 h-2 bg-pepe-green rounded-full" title="Active"></div>
                        </div>
                    </div>
                    
                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span class="text-gray-400">HWID Lock:</span>
                            <span class="text-white" x-text="hwidLock ? 'Enabled' : 'Disabled'"></span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Default Expiry:</span>
                            <span class="text-white" x-text="expiryDays + ' days'"></span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Status:</span>
                            <span class="badge badge-success">Active</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Form Actions -->
            <div class="flex items-center justify-between pt-6 border-t border-pepe-light-gray/20">
                <a href="{{ url_for('dashboard.index') }}" class="btn-secondary">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    Cancel
                </a>
                
                <button type="submit" class="btn-primary" 
                        :disabled="appName.length < 3"
                        :class="{ 'opacity-50 cursor-not-allowed': appName.length < 3 }">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Create Application
                </button>
            </div>
        </form>
    </div>
    
    <!-- Help Section -->
    <div class="mt-8 glass-card p-6 rounded-xl">
        <h3 class="text-lg font-semibold text-white mb-4">
            <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            Need Help?
        </h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <h4 class="text-white font-medium mb-2">What is an Application?</h4>
                <p class="text-sm text-gray-300">
                    An application represents a software project or service that you want to protect with license keys. 
                    Each app has its own secret key for API authentication and can have multiple license keys.
                </p>
            </div>
            <div>
                <h4 class="text-white font-medium mb-2">HWID Lock Explained</h4>
                <p class="text-sm text-gray-300">
                    Hardware ID locking binds license keys to specific devices, preventing users from sharing 
                    keys across multiple computers. This is recommended for most applications.
                </p>
            </div>
            <div>
                <h4 class="text-white font-medium mb-2">App Secret Security</h4>
                <p class="text-sm text-gray-300">
                    Your app secret is used to authenticate API requests. Keep it secure and never share it publicly. 
                    You can regenerate it if needed, but this will require updating your application code.
                </p>
            </div>
            <div>
                <h4 class="text-white font-medium mb-2">Default Expiry</h4>
                <p class="text-sm text-gray-300">
                    This sets the default expiration period for new license keys. You can always override this 
                    when creating individual keys or bulk generating keys.
                </p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Form validation
    const form = document.querySelector('form');
    const nameInput = document.querySelector('input[name="name"]');
    
    form.addEventListener('submit', function(e) {
        if (nameInput.value.length < 3) {
            e.preventDefault();
            alert('Application name must be at least 3 characters long.');
            nameInput.focus();
        }
    });
    
    // Real-time validation feedback
    nameInput.addEventListener('input', function() {
        const submitBtn = document.querySelector('button[type="submit"]');
        if (this.value.length < 3) {
            submitBtn.disabled = true;
            submitBtn.classList.add('opacity-50', 'cursor-not-allowed');
        } else {
            submitBtn.disabled = false;
            submitBtn.classList.remove('opacity-50', 'cursor-not-allowed');
        }
    });
});
</script>
{% endblock %}
