{% extends "base.html" %}

{% block title %}Admin Dashboard - PepeAuth{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-white">Admin Dashboard</h1>
        <p class="mt-1 text-gray-300">System overview and management</p>
    </div>
    
    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="glass-card p-6 rounded-xl">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-blue-500/20 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-300">Total Users</p>
                    <p class="text-2xl font-semibold text-white">{{ stats.users }}</p>
                </div>
            </div>
        </div>
        
        <div class="glass-card p-6 rounded-xl">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-pepe-green/20 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-pepe-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-300">Total Apps</p>
                    <p class="text-2xl font-semibold text-white">{{ stats.apps }}</p>
                </div>
            </div>
        </div>
        
        <div class="glass-card p-6 rounded-xl">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-purple-500/20 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m0 0a2 2 0 012 2m-2-2a2 2 0 00-2 2m2-2V5a2 2 0 00-2-2m0 0H9a2 2 0 00-2 2v0a2 2 0 002 2h2m-4 4v6a2 2 0 002 2h2a2 2 0 002-2v-6m-4 0a2 2 0 012-2h0a2 2 0 012 2v0"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-300">License Keys</p>
                    <p class="text-2xl font-semibold text-white">{{ stats.keys }}</p>
                </div>
            </div>
        </div>
        
        <div class="glass-card p-6 rounded-xl">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-green-500/20 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-300">Used Keys</p>
                    <p class="text-2xl font-semibold text-white">{{ stats.used_keys }}</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Quick Actions -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div class="glass-card p-6 rounded-xl">
            <h3 class="text-lg font-semibold text-white mb-4">User Management</h3>
            <p class="text-gray-300 text-sm mb-4">Manage user accounts, plans, and permissions</p>
            <a href="{{ url_for('admin.users') }}" class="btn-primary w-full text-center">
                Manage Users
            </a>
        </div>
        
        <div class="glass-card p-6 rounded-xl">
            <h3 class="text-lg font-semibold text-white mb-4">App Management</h3>
            <p class="text-gray-300 text-sm mb-4">View and manage all applications in the system</p>
            <a href="{{ url_for('admin.apps') }}" class="btn-primary w-full text-center">
                Manage Apps
            </a>
        </div>
        
        <div class="glass-card p-6 rounded-xl">
            <h3 class="text-lg font-semibold text-white mb-4">Plan Management</h3>
            <p class="text-gray-300 text-sm mb-4">Configure subscription plans and limits</p>
            <a href="{{ url_for('admin.plans') }}" class="btn-primary w-full text-center">
                Manage Plans
            </a>
        </div>
    </div>
    
    <!-- Recent Activity -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Recent Users -->
        <div class="glass-card p-6 rounded-xl">
            <h3 class="text-lg font-semibold text-white mb-4">Recent Users</h3>
            {% if recent_users %}
                <div class="space-y-3">
                    {% for user in recent_users %}
                        <div class="flex items-center justify-between p-3 bg-pepe-darker/50 rounded-lg">
                            <div>
                                <p class="text-white font-medium">{{ user.username }}</p>
                                <p class="text-gray-400 text-sm">{{ user.email }}</p>
                            </div>
                            <div class="text-right">
                                <p class="text-gray-400 text-sm">{{ user.created_at }}</p>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            {% else %}
                <p class="text-gray-400">No recent users</p>
            {% endif %}
        </div>
        
        <!-- Recent Apps -->
        <div class="glass-card p-6 rounded-xl">
            <h3 class="text-lg font-semibold text-white mb-4">Recent Apps</h3>
            {% if recent_apps %}
                <div class="space-y-3">
                    {% for app in recent_apps %}
                        <div class="flex items-center justify-between p-3 bg-pepe-darker/50 rounded-lg">
                            <div>
                                <p class="text-white font-medium">{{ app.name }}</p>
                                <p class="text-gray-400 text-sm">by {{ app.username }}</p>
                            </div>
                            <div class="text-right">
                                <p class="text-gray-400 text-sm">{{ app.created_at }}</p>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            {% else %}
                <p class="text-gray-400">No recent apps</p>
            {% endif %}
        </div>
    </div>
    
    <!-- Plan Distribution -->
    {% if plan_stats %}
        <div class="glass-card p-6 rounded-xl mt-8">
            <h3 class="text-lg font-semibold text-white mb-4">Plan Distribution</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                {% for plan in plan_stats %}
                    <div class="text-center p-4 bg-pepe-darker/50 rounded-lg">
                        <p class="text-2xl font-bold text-pepe-green">{{ plan.user_count }}</p>
                        <p class="text-white font-medium">{{ plan.name }}</p>
                        <p class="text-gray-400 text-sm">users</p>
                    </div>
                {% endfor %}
            </div>
        </div>
    {% endif %}
</div>
{% endblock %}
