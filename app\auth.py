import logging
from flask import Blueprint, render_template, request, redirect, url_for, flash, g
from flask_login import UserMixin, login_user, logout_user, login_required, current_user
from werkzeug.security import check_password_hash, generate_password_hash
from app import bcrypt, query_db, execute_db
from app.forms import LoginForm, RegisterForm

bp = Blueprint('auth', __name__, url_prefix='/auth')

class User(UserMixin):
    """User class for Flask-Login"""
    def __init__(self, user_data):
        self.id = str(user_data['id'])
        self.email = user_data['email']
        self.username = user_data['username']
        self.password = user_data['password']
        self.plan_id = user_data['plan_id']
        self.is_admin = bool(user_data['is_admin'])
        self.created_at = user_data['created_at']
    
    def get_plan(self):
        """Get user's plan information"""
        return query_db(
            'SELECT * FROM plan WHERE id = ?', 
            (self.plan_id,), 
            one=True
        )
    
    def can_create_app(self):
        """Check if user can create more apps"""
        plan = self.get_plan()
        if plan['max_apps'] == -1:  # Unlimited
            return True
        
        current_apps = query_db(
            'SELECT COUNT(*) as count FROM app WHERE owner_id = ?',
            (self.id,),
            one=True
        )
        return current_apps['count'] < plan['max_apps']
    
    def can_create_key(self, app_id):
        """Check if user can create more keys for an app"""
        plan = self.get_plan()
        if plan['max_keys'] == -1:  # Unlimited
            return True
        
        current_keys = query_db(
            'SELECT COUNT(*) as count FROM license_key lk '
            'JOIN app a ON lk.app_id = a.id '
            'WHERE a.owner_id = ?',
            (self.id,),
            one=True
        )
        return current_keys['count'] < plan['max_keys']

@bp.route('/register', methods=['GET', 'POST'])
def register():
    """User registration"""
    if current_user.is_authenticated:
        return redirect(url_for('dashboard.index'))
    
    form = RegisterForm()
    if form.validate_on_submit():
        email = form.email.data.lower().strip()
        username = form.username.data.strip()
        password = form.password.data
        
        # Check if user already exists
        existing_user = query_db(
            'SELECT id FROM user WHERE email = ? OR username = ?',
            (email, username),
            one=True
        )
        
        if existing_user:
            flash('Email or username already exists.', 'error')
            return render_template('register.html', form=form)
        
        # Hash password and create user
        password_hash = bcrypt.generate_password_hash(password).decode('utf-8')
        
        try:
            user_id = execute_db(
                'INSERT INTO user (email, username, password, plan_id) VALUES (?, ?, ?, ?)',
                (email, username, password_hash, 1)  # Default to Free plan
            )
            
            # Log registration
            logging.info(f'New user registered: {username} ({email})')
            
            flash('Registration successful! Please log in.', 'success')
            return redirect(url_for('auth.login'))
            
        except Exception as e:
            logging.error(f'Registration error: {str(e)}')
            flash('Registration failed. Please try again.', 'error')
    
    return render_template('register.html', form=form)

@bp.route('/login', methods=['GET', 'POST'])
def login():
    """User login"""
    if current_user.is_authenticated:
        return redirect(url_for('dashboard.index'))
    
    form = LoginForm()
    if form.validate_on_submit():
        username_or_email = form.username.data.strip()
        password = form.password.data
        
        # Find user by username or email
        user_data = query_db(
            'SELECT * FROM user WHERE username = ? OR email = ?',
            (username_or_email, username_or_email.lower()),
            one=True
        )
        
        if user_data and bcrypt.check_password_hash(user_data['password'], password):
            user = User(user_data)
            login_user(user, remember=form.remember_me.data)
            
            # Log successful login
            logging.info(f'User logged in: {user.username}')
            
            next_page = request.args.get('next')
            if next_page:
                return redirect(next_page)
            return redirect(url_for('dashboard.index'))
        else:
            # Log failed login attempt
            logging.warning(f'Failed login attempt for: {username_or_email}')
            flash('Invalid username/email or password.', 'error')
    
    return render_template('login.html', form=form)

@bp.route('/logout')
@login_required
def logout():
    """User logout"""
    username = current_user.username
    logout_user()
    logging.info(f'User logged out: {username}')
    flash('You have been logged out.', 'info')
    return redirect(url_for('index'))

@bp.route('/profile')
@login_required
def profile():
    """User profile page"""
    plan = current_user.get_plan()
    
    # Get user statistics
    stats = {
        'apps': query_db(
            'SELECT COUNT(*) as count FROM app WHERE owner_id = ?',
            (current_user.id,),
            one=True
        )['count'],
        'keys': query_db(
            'SELECT COUNT(*) as count FROM license_key lk '
            'JOIN app a ON lk.app_id = a.id '
            'WHERE a.owner_id = ?',
            (current_user.id,),
            one=True
        )['count'],
        'used_keys': query_db(
            'SELECT COUNT(*) as count FROM license_key lk '
            'JOIN app a ON lk.app_id = a.id '
            'WHERE a.owner_id = ? AND lk.used = 1',
            (current_user.id,),
            one=True
        )['count']
    }
    
    return render_template('profile.html', plan=plan, stats=stats)
