#!/usr/bin/env python3
"""
Quick database fix script for PepeAuth
"""

import sqlite3
import os

def fix_database():
    """Fix database schema issues"""
    db_path = 'pepeauth.db'
    
    if not os.path.exists(db_path):
        print("Database doesn't exist, creating new one...")
        # Create new database with schema
        conn = sqlite3.connect(db_path)
        with open('schema.sql', 'r') as f:
            conn.executescript(f.read())
        conn.close()
        print("✅ New database created!")
        return
    
    print("Fixing existing database...")
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # List of columns to add if missing
    migrations = [
        # Plan table
        ("plan", "price", "ALTER TABLE plan ADD COLUMN price DECIMAL(10,2) DEFAULT 0.00"),
        ("plan", "duration_days", "ALTER TABLE plan ADD COLUMN duration_days INTEGER NOT NULL DEFAULT 30"),
        ("plan", "features", "ALTER TABLE plan ADD COLUMN features TEXT"),
        
        # User table
        ("user", "hwid_reset_count", "ALTER TABLE user ADD COLUMN hwid_reset_count INTEGER NOT NULL DEFAULT 0"),
        ("user", "max_hwid_resets", "ALTER TABLE user ADD COLUMN max_hwid_resets INTEGER NOT NULL DEFAULT 3"),
        ("user", "last_login", "ALTER TABLE user ADD COLUMN last_login DATETIME"),
        ("user", "last_ip", "ALTER TABLE user ADD COLUMN last_ip TEXT"),
        
        # App table
        ("app", "webhook_url", "ALTER TABLE app ADD COLUMN webhook_url TEXT"),
        ("app", "webhook_secret", "ALTER TABLE app ADD COLUMN webhook_secret TEXT"),
        ("app", "is_active", "ALTER TABLE app ADD COLUMN is_active INTEGER NOT NULL DEFAULT 1"),
        
        # License key table
        ("license_key", "is_banned", "ALTER TABLE license_key ADD COLUMN is_banned INTEGER NOT NULL DEFAULT 0"),
        ("license_key", "ban_reason", "ALTER TABLE license_key ADD COLUMN ban_reason TEXT"),
    ]
    
    for table, column, sql in migrations:
        try:
            # Check if column exists
            cursor.execute(f"PRAGMA table_info({table})")
            columns = [row[1] for row in cursor.fetchall()]
            
            if column not in columns:
                print(f"Adding {column} to {table}...")
                cursor.execute(sql)
                print("✅ Success")
        except sqlite3.Error as e:
            print(f"❌ Error adding {column} to {table}: {e}")
    
    # Create missing tables
    missing_tables = [
        ("auth_log", '''
            CREATE TABLE auth_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                license_key_id INTEGER,
                app_id INTEGER,
                user_id INTEGER,
                hwid TEXT,
                ip_address TEXT,
                user_agent TEXT,
                success INTEGER NOT NULL,
                failure_reason TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        '''),
        ("system_log", '''
            CREATE TABLE system_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                level TEXT NOT NULL,
                category TEXT NOT NULL,
                message TEXT NOT NULL,
                user_id INTEGER,
                app_id INTEGER,
                ip_address TEXT,
                extra_data TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        '''),
        ("hwid_reset_log", '''
            CREATE TABLE hwid_reset_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                license_key_id INTEGER NOT NULL,
                old_hwid TEXT,
                new_hwid TEXT,
                reset_by INTEGER,
                reason TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        '''),
        ("webhook_log", '''
            CREATE TABLE webhook_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                app_id INTEGER NOT NULL,
                event_type TEXT NOT NULL,
                payload TEXT,
                response_code INTEGER,
                response_body TEXT,
                success INTEGER NOT NULL DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        '''),
        ("rate_limit_log", '''
            CREATE TABLE rate_limit_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                identifier TEXT NOT NULL,
                endpoint TEXT NOT NULL,
                requests_count INTEGER NOT NULL DEFAULT 1,
                window_start DATETIME NOT NULL,
                blocked INTEGER NOT NULL DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
    ]
    
    for table_name, create_sql in missing_tables:
        try:
            cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table_name}'")
            if not cursor.fetchone():
                print(f"Creating {table_name} table...")
                cursor.execute(create_sql)
                print("✅ Success")
        except sqlite3.Error as e:
            print(f"❌ Error creating {table_name}: {e}")
    
    # Update plan data
    try:
        print("Updating plan data...")
        plans_data = [
            (0.00, 7, '["Basic API Access", "HWID Binding", "Email Support"]', 'Trial'),
            (9.99, 30, '["Full API Access", "HWID Binding", "Webhooks", "Priority Support"]', 'Premium'),
            (49.99, -1, '["Unlimited Everything", "Priority Support", "Custom Features"]', 'Lifetime')
        ]
        
        for price, duration, features, name in plans_data:
            cursor.execute('''
                UPDATE plan 
                SET price = ?, duration_days = ?, features = ?
                WHERE name = ?
            ''', (price, duration, features, name))
        print("✅ Plan data updated")
    except sqlite3.Error as e:
        print(f"❌ Error updating plans: {e}")
    
    conn.commit()
    conn.close()
    print("🎉 Database fixed successfully!")

if __name__ == "__main__":
    fix_database()
