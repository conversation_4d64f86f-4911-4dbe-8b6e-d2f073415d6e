"""
KeyAuth-like Admin API Module
Handles admin operations like creating keys, managing users, and viewing statistics
"""

import logging
import secrets
import string
from datetime import datetime, timedelta
from flask import Blueprint, request, jsonify, current_app
from flask_login import login_required, current_user
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(__file__)))
from app import query_db, execute_db, limiter

bp = Blueprint('api_admin', __name__, url_prefix='/api/v1/admin')

def generate_license_key(length: int = 16) -> str:
    """Generate a random license key"""
    chars = string.ascii_uppercase + string.digits
    return ''.join(secrets.choice(chars) for _ in range(length))

def require_admin():
    """Check if current user is admin"""
    if not current_user.is_authenticated:
        return jsonify({
            'status': False,
            'message': 'Authentication required'
        }), 401
    
    if not getattr(current_user, 'is_admin', False):
        return jsonify({
            'status': False,
            'message': 'Admin privileges required'
        }), 403
    
    return None

@bp.route('/create_key', methods=['POST'])
@limiter.limit("50 per hour")
def create_key():
    """
    Create new license keys (Admin only)
    
    Expected JSON payload:
    {
        "count": 1,
        "plan": "Premium",
        "expiry_days": 30,
        "user_id": null (optional - assign to specific user)
    }
    """
    try:
        # Check admin privileges
        admin_check = require_admin()
        if admin_check:
            return admin_check
        
        if not request.is_json:
            return jsonify({
                'status': False,
                'message': 'Content-Type must be application/json'
            }), 400
        
        data = request.get_json()
        count = data.get('count', 1)
        plan_name = data.get('plan', 'Premium')
        expiry_days = data.get('expiry_days', current_app.config.get('DEFAULT_EXPIRY_DAYS', 30))
        user_id = data.get('user_id')
        
        # Validate input
        if not isinstance(count, int) or count < 1 or count > 100:
            return jsonify({
                'status': False,
                'message': 'Count must be between 1 and 100'
            }), 400
        
        if not isinstance(expiry_days, int) or expiry_days < 1:
            return jsonify({
                'status': False,
                'message': 'Expiry days must be a positive integer'
            }), 400
        
        # Get plan ID
        plan_data = query_db('SELECT id FROM plan WHERE name = ?', (plan_name,), one=True)
        if not plan_data:
            return jsonify({
                'status': False,
                'message': f'Plan "{plan_name}" not found'
            }), 400
        
        # Validate user_id if provided
        if user_id:
            user_data = query_db('SELECT id FROM user WHERE id = ?', (user_id,), one=True)
            if not user_data:
                return jsonify({
                    'status': False,
                    'message': f'User ID {user_id} not found'
                }), 400
        
        # Generate license keys
        created_keys = []
        for _ in range(count):
            # Generate unique key
            while True:
                key = generate_license_key()
                existing = query_db('SELECT id FROM license_key WHERE key = ?', (key,), one=True)
                if not existing:
                    break
            
            # Insert key into database
            key_id = execute_db(
                'INSERT INTO license_key (key, plan_id, user_id, created_by, expiry_days) VALUES (?, ?, ?, ?, ?)',
                (key, plan_data['id'], user_id, current_user.id, expiry_days)
            )
            
            created_keys.append({
                'id': key_id,
                'key': key,
                'plan': plan_name,
                'expiry_days': expiry_days,
                'created_at': datetime.now().isoformat()
            })
        
        logging.info(f'Admin {current_user.username} created {count} license keys for plan {plan_name}')
        
        return jsonify({
            'status': True,
            'message': f'Successfully created {count} license key(s)',
            'keys': created_keys
        }), 200
        
    except Exception as e:
        logging.error(f'Create key error: {str(e)}')
        return jsonify({
            'status': False,
            'message': 'Internal server error'
        }), 500

@bp.route('/ban_user', methods=['POST'])
@limiter.limit("20 per hour")
def ban_user():
    """
    Ban or unban a user (Admin only)
    
    Expected JSON payload:
    {
        "user_id": 123,
        "banned": true,
        "reason": "Violation of terms"
    }
    """
    try:
        # Check admin privileges
        admin_check = require_admin()
        if admin_check:
            return admin_check
        
        if not request.is_json:
            return jsonify({
                'status': False,
                'message': 'Content-Type must be application/json'
            }), 400
        
        data = request.get_json()
        user_id = data.get('user_id')
        banned = data.get('banned', True)
        reason = data.get('reason', 'No reason provided')
        
        if not user_id:
            return jsonify({
                'status': False,
                'message': 'User ID is required'
            }), 400
        
        # Check if user exists
        user_data = query_db('SELECT username FROM user WHERE id = ?', (user_id,), one=True)
        if not user_data:
            return jsonify({
                'status': False,
                'message': 'User not found'
            }), 404
        
        # Update user ban status
        execute_db(
            'UPDATE user SET is_banned = ?, ban_reason = ? WHERE id = ?',
            (1 if banned else 0, reason if banned else None, user_id)
        )
        
        action = 'banned' if banned else 'unbanned'
        logging.info(f'Admin {current_user.username} {action} user {user_data["username"]} (ID: {user_id})')
        
        return jsonify({
            'status': True,
            'message': f'User {user_data["username"]} has been {action}'
        }), 200
        
    except Exception as e:
        logging.error(f'Ban user error: {str(e)}')
        return jsonify({
            'status': False,
            'message': 'Internal server error'
        }), 500

@bp.route('/stats', methods=['GET'])
@limiter.limit("30 per hour")
def get_stats():
    """Get system statistics (Admin only)"""
    try:
        # Check admin privileges
        admin_check = require_admin()
        if admin_check:
            return admin_check
        
        # Get user statistics
        user_stats = query_db(
            '''SELECT 
                COUNT(*) as total_users,
                COUNT(CASE WHEN is_banned = 1 THEN 1 END) as banned_users,
                COUNT(CASE WHEN plan_id = (SELECT id FROM plan WHERE name = 'Premium') THEN 1 END) as premium_users
               FROM user''',
            one=True
        )
        
        # Get license key statistics
        key_stats = query_db(
            '''SELECT 
                COUNT(*) as total_keys,
                COUNT(CASE WHEN used = 1 THEN 1 END) as used_keys,
                COUNT(CASE WHEN expires_at < ? THEN 1 END) as expired_keys
               FROM license_key''',
            (datetime.now().isoformat(),),
            one=True
        )
        
        # Get recent activity
        recent_activations = query_db(
            '''SELECT lk.key, lk.used_at, u.username 
               FROM license_key lk
               LEFT JOIN user u ON lk.user_id = u.id
               WHERE lk.used = 1 
               ORDER BY lk.used_at DESC 
               LIMIT 10'''
        )
        
        return jsonify({
            'status': True,
            'stats': {
                'users': {
                    'total': user_stats['total_users'],
                    'banned': user_stats['banned_users'],
                    'premium': user_stats['premium_users']
                },
                'keys': {
                    'total': key_stats['total_keys'],
                    'used': key_stats['used_keys'],
                    'unused': key_stats['total_keys'] - key_stats['used_keys'],
                    'expired': key_stats['expired_keys']
                },
                'recent_activations': [
                    {
                        'key': row['key'][:8] + '...',  # Partially hide key
                        'username': row['username'] or 'Unknown',
                        'activated_at': row['used_at']
                    }
                    for row in recent_activations
                ]
            }
        }), 200
        
    except Exception as e:
        logging.error(f'Get stats error: {str(e)}')
        return jsonify({
            'status': False,
            'message': 'Internal server error'
        }), 500

@bp.errorhandler(429)
def ratelimit_handler(e):
    """Handle rate limit exceeded"""
    return jsonify({
        'status': False,
        'message': 'Rate limit exceeded. Please try again later.'
    }), 429
