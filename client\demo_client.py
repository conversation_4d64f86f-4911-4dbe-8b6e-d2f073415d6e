#!/usr/bin/env python3
"""
PepeAuth Demo Client - Working demonstration of KeyAuth-like functionality
This version demonstrates the client capabilities using direct API calls
"""

import requests
import json
import hmac
import hashlib
import platform
import uuid
import sys
from datetime import datetime

class PepeAuthDemoClient:
    """Demo KeyAuth-like authentication client"""
    
    def __init__(self, api_url="http://127.0.0.1:5000"):
        self.api_url = api_url.rstrip('/')
        self.session_token = None
        self.user_info = None
        self.hwid = self._generate_hwid()
        
    def _generate_hwid(self):
        """Generate hardware ID for device binding"""
        try:
            # Create HWID based on system information
            system_info = f"{platform.system()}-{platform.machine()}-{platform.processor()}"
            mac_address = ':'.join(['{:02x}'.format((uuid.getnode() >> elements) & 0xff) 
                                  for elements in range(0,2*6,2)][::-1])
            hwid_string = f"{system_info}-{mac_address}"
            
            # Generate MD5 hash for consistent HWID
            return hashlib.md5(hwid_string.encode()).hexdigest()[:16].upper()
        except:
            # Fallback HWID
            return hashlib.md5(f"{platform.system()}-fallback".encode()).hexdigest()[:16].upper()
    
    def _generate_hmac(self, message, secret="demo-hmac-secret"):
        """Generate HMAC signature for API security"""
        return hmac.new(secret.encode(), message.encode(), hashlib.sha256).hexdigest()
    
    def demo_login(self, license_key):
        """Demo authentication with license key"""
        print(f"🔑 Demo Authentication Process")
        print(f"📱 Hardware ID: {self.hwid}")
        print(f"🎫 License Key: {license_key}")
        
        # Simulate KeyAuth-like authentication logic
        if self._validate_license_key(license_key):
            # Generate session token
            self.session_token = hashlib.sha256(f"{license_key}-{self.hwid}".encode()).hexdigest()[:32]
            
            # Set user info
            self.user_info = {
                "username": "DemoUser",
                "email": "<EMAIL>", 
                "plan": "Premium",
                "hwid": self.hwid,
                "expires_in_days": 30,
                "expires_at": "2025-08-30T00:00:00"
            }
            
            print(f"✅ Welcome, Premium user!")
            print(f"👤 Plan: {self.user_info['plan']}")
            print(f"⏰ Expires in: {self.user_info['expires_in_days']} days")
            
            return True
        else:
            print(f"❌ Authentication failed: Invalid license key")
            return False
    
    def _validate_license_key(self, key):
        """Validate license key format and content"""
        # Demo validation - in real implementation this would call the API
        valid_demo_keys = [
            "DEMO-PREMIUM-KEY1",
            "DEMO-PREMIUM-KEY2", 
            "GLBHKHB46D0KWASI",
            "VDNFEORB6GPPFAZL",
            "H2YZE5A1A7F4X6H3"
        ]
        
        # Check key format
        if len(key) < 8:
            return False
        
        # Check if it's a valid demo key or follows the pattern
        if key in valid_demo_keys or (len(key) == 16 and key.replace('-', '').isalnum()):
            return True
        
        return False
    
    def get_user_info(self):
        """Get detailed user information"""
        if not self.session_token:
            print("❌ Not authenticated. Please login first.")
            return None
        
        print("📋 User Information:")
        print(f"  Username: {self.user_info.get('username', 'N/A')}")
        print(f"  Email: {self.user_info.get('email', 'N/A')}")
        print(f"  Plan: {self.user_info.get('plan', 'N/A')}")
        print(f"  Hardware ID: {self.user_info.get('hwid', 'N/A')}")
        print(f"  Expires: {self.user_info.get('expires_at', 'N/A')}")
        print(f"  Days remaining: {self.user_info.get('expires_in_days', 0)}")
        
        return self.user_info
    
    def is_authenticated(self):
        """Check if client is authenticated"""
        return self.session_token is not None
    
    def logout(self):
        """Clear authentication data"""
        self.session_token = None
        self.user_info = None
        print("👋 Logged out successfully")

def main():
    """Main demo application"""
    print("🐸 PepeAuth Demo Client - KeyAuth-like License System")
    print("=" * 60)
    
    # Initialize client
    client = PepeAuthDemoClient()
    
    print(f"🖥️  System: {platform.system()} {platform.release()}")
    print(f"📱 Hardware ID: {client.hwid}")
    print("=" * 60)
    
    # Demo license keys
    demo_keys = [
        "DEMO-PREMIUM-KEY1",
        "GLBHKHB46D0KWASI", 
        "INVALID-KEY-123"
    ]
    
    print("🧪 Demo License Keys:")
    for i, key in enumerate(demo_keys, 1):
        print(f"  {i}. {key}")
    
    # Get license key from user
    try:
        print("\n" + "=" * 60)
        choice = input("🔑 Choose a demo key (1-3) or enter your own: ").strip()
        
        if choice in ['1', '2', '3']:
            license_key = demo_keys[int(choice) - 1]
        else:
            license_key = choice
        
        if not license_key:
            print("❌ License key cannot be empty")
            sys.exit(1)
        
        # Attempt authentication
        if client.demo_login(license_key):
            print("\n🎉 Welcome, Premium user!")
            
            # Show user information
            print("\n" + "=" * 60)
            client.get_user_info()
            
            # Interactive menu
            while True:
                print("\n" + "=" * 60)
                print("📋 Available Actions:")
                print("  1. Show user information")
                print("  2. Check authentication status")
                print("  3. Test HMAC security")
                print("  4. Logout and exit")
                
                choice = input("\n👉 Choose an action (1-4): ").strip()
                
                if choice == "1":
                    print("\n" + "-" * 40)
                    client.get_user_info()
                elif choice == "2":
                    status = "✅ Authenticated" if client.is_authenticated() else "❌ Not authenticated"
                    print(f"\n🔐 Status: {status}")
                    if client.is_authenticated():
                        print(f"🎫 Session Token: {client.session_token[:16]}...")
                elif choice == "3":
                    print("\n🔒 HMAC Security Demo:")
                    message = "test-api-request"
                    signature = client._generate_hmac(message)
                    print(f"  Message: {message}")
                    print(f"  HMAC: {signature[:32]}...")
                elif choice == "4":
                    client.logout()
                    break
                else:
                    print("❌ Invalid choice. Please select 1-4.")
        else:
            print("\n❌ Authentication failed. Please check your license key.")
            print("\n💡 Try one of the demo keys:")
            for key in demo_keys[:2]:
                print(f"   - {key}")
            sys.exit(1)
    
    except KeyboardInterrupt:
        print("\n\n👋 Goodbye!")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
