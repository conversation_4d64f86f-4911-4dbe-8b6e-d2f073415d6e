{% extends "base.html" %}

{% block title %}Webhook Settings - PepeAuth{% endblock %}

{% block content %}
<div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center space-x-4 mb-4">
            <a href="{{ url_for('dashboard.index') }}" 
               class="text-gray-400 hover:text-white transition-colors">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
            </a>
            <div>
                <h1 class="text-3xl font-bold text-white">Webhook Settings</h1>
                <p class="mt-1 text-gray-300">Configure webhook notifications for your applications</p>
            </div>
        </div>
        
        <!-- Info Card -->
        <div class="glass-card p-4 rounded-lg border-l-4 border-blue-500">
            <div class="flex items-start">
                <div class="flex-shrink-0">
                    <svg class="w-5 h-5 text-blue-400 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm text-white">
                        <span class="font-medium">Webhooks</span> allow your applications to receive real-time notifications 
                        when events occur, such as successful authentications, key usage, or security alerts.
                    </p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- App Selection -->
    <div class="glass-card p-6 rounded-xl mb-8">
        <h3 class="text-lg font-semibold text-white mb-4">Select Application</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {% for app in apps %}
                <div class="glass-card p-4 rounded-lg cursor-pointer transition-all hover:scale-105 app-selector"
                     data-app-id="{{ app.id }}"
                     onclick="selectApp({{ app.id }}, '{{ app.name }}')">
                    <div class="flex items-center justify-between mb-2">
                        <h4 class="text-white font-medium">{{ app.name }}</h4>
                        <div class="flex items-center space-x-1">
                            {% if app.webhook_url %}
                                <div class="w-2 h-2 bg-pepe-green rounded-full tooltip" data-tooltip="Webhook Configured"></div>
                            {% else %}
                                <div class="w-2 h-2 bg-gray-500 rounded-full tooltip" data-tooltip="No Webhook"></div>
                            {% endif %}
                        </div>
                    </div>
                    <p class="text-sm text-gray-400">{{ app.key_count }} keys</p>
                </div>
            {% endfor %}
        </div>
    </div>
    
    <!-- Webhook Configuration -->
    <div id="webhook-config" class="glass-card p-8 rounded-xl mb-8" style="display: none;">
        <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold text-white">
                Webhook Configuration for <span id="selected-app-name" class="text-pepe-green"></span>
            </h3>
            <button onclick="testWebhook()" class="btn-secondary" id="test-webhook-btn" disabled>
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
                </svg>
                Test Webhook
            </button>
        </div>
        
        <form id="webhook-form" class="space-y-6">
            <!-- Webhook URL -->
            <div class="form-group">
                <label for="webhook_url" class="form-label">
                    Webhook URL
                    <span class="text-red-400">*</span>
                </label>
                <input type="url" 
                       id="webhook_url" 
                       name="webhook_url" 
                       class="input-field" 
                       placeholder="https://your-server.com/webhook"
                       required>
                <p class="mt-1 text-sm text-gray-400">
                    The URL where webhook notifications will be sent. Must be HTTPS for security.
                </p>
            </div>
            
            <!-- Webhook Secret -->
            <div class="form-group">
                <label for="webhook_secret" class="form-label">
                    Webhook Secret
                    <span class="text-red-400">*</span>
                </label>
                <div class="flex space-x-2">
                    <input type="text" 
                           id="webhook_secret" 
                           name="webhook_secret" 
                           class="input-field flex-1" 
                           placeholder="Enter a secure secret key"
                           required>
                    <button type="button" onclick="generateSecret()" class="btn-secondary">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                    </button>
                </div>
                <p class="mt-1 text-sm text-gray-400">
                    Used to verify webhook authenticity. Keep this secret secure!
                </p>
            </div>
            
            <!-- Event Types -->
            <div class="form-group">
                <label class="form-label">Event Types</label>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="space-y-3">
                        <div class="flex items-center space-x-3">
                            <input type="checkbox" 
                                   id="event_auth_success" 
                                   name="events" 
                                   value="auth_success"
                                   class="w-4 h-4 text-pepe-green bg-pepe-gray border-pepe-light-gray rounded focus:ring-pepe-green focus:ring-2"
                                   checked>
                            <label for="event_auth_success" class="text-white">Authentication Success</label>
                        </div>
                        <div class="flex items-center space-x-3">
                            <input type="checkbox" 
                                   id="event_auth_fail" 
                                   name="events" 
                                   value="auth_fail"
                                   class="w-4 h-4 text-pepe-green bg-pepe-gray border-pepe-light-gray rounded focus:ring-pepe-green focus:ring-2"
                                   checked>
                            <label for="event_auth_fail" class="text-white">Authentication Failure</label>
                        </div>
                        <div class="flex items-center space-x-3">
                            <input type="checkbox" 
                                   id="event_key_created" 
                                   name="events" 
                                   value="key_created"
                                   class="w-4 h-4 text-pepe-green bg-pepe-gray border-pepe-light-gray rounded focus:ring-pepe-green focus:ring-2">
                            <label for="event_key_created" class="text-white">Key Created</label>
                        </div>
                        <div class="flex items-center space-x-3">
                            <input type="checkbox" 
                                   id="event_key_banned" 
                                   name="events" 
                                   value="key_banned"
                                   class="w-4 h-4 text-pepe-green bg-pepe-gray border-pepe-light-gray rounded focus:ring-pepe-green focus:ring-2">
                            <label for="event_key_banned" class="text-white">Key Banned</label>
                        </div>
                    </div>
                    <div class="space-y-3">
                        <div class="flex items-center space-x-3">
                            <input type="checkbox" 
                                   id="event_hwid_reset" 
                                   name="events" 
                                   value="hwid_reset"
                                   class="w-4 h-4 text-pepe-green bg-pepe-gray border-pepe-light-gray rounded focus:ring-pepe-green focus:ring-2">
                            <label for="event_hwid_reset" class="text-white">HWID Reset</label>
                        </div>
                        <div class="flex items-center space-x-3">
                            <input type="checkbox" 
                                   id="event_key_expired" 
                                   name="events" 
                                   value="key_expired"
                                   class="w-4 h-4 text-pepe-green bg-pepe-gray border-pepe-light-gray rounded focus:ring-pepe-green focus:ring-2">
                            <label for="event_key_expired" class="text-white">Key Expired</label>
                        </div>
                        <div class="flex items-center space-x-3">
                            <input type="checkbox" 
                                   id="event_suspicious_activity" 
                                   name="events" 
                                   value="suspicious_activity"
                                   class="w-4 h-4 text-pepe-green bg-pepe-gray border-pepe-light-gray rounded focus:ring-pepe-green focus:ring-2">
                            <label for="event_suspicious_activity" class="text-white">Suspicious Activity</label>
                        </div>
                    </div>
                </div>
                <p class="mt-2 text-sm text-gray-400">
                    Select which events should trigger webhook notifications.
                </p>
            </div>
            
            <!-- Form Actions -->
            <div class="flex items-center justify-between pt-6 border-t border-pepe-light-gray/20">
                <button type="button" onclick="clearWebhook()" class="btn-danger">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                    </svg>
                    Clear Webhook
                </button>
                
                <button type="submit" class="btn-primary">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Save Webhook
                </button>
            </div>
        </form>
    </div>
    
    <!-- Webhook Logs -->
    <div id="webhook-logs" class="glass-card p-6 rounded-xl" style="display: none;">
        <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold text-white">Recent Webhook Deliveries</h3>
            <button onclick="refreshLogs()" class="btn-secondary text-sm">
                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                Refresh
            </button>
        </div>
        
        <div class="overflow-x-auto">
            <table class="table-glass" id="webhook-logs-table">
                <thead>
                    <tr>
                        <th>Event</th>
                        <th>Status</th>
                        <th>Response Code</th>
                        <th>Timestamp</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- Logs will be loaded dynamically -->
                </tbody>
            </table>
        </div>
    </div>
    
    <!-- Webhook Documentation -->
    <div class="glass-card p-6 rounded-xl">
        <h3 class="text-lg font-semibold text-white mb-4">
            <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            Webhook Documentation
        </h3>
        
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Payload Format -->
            <div>
                <h4 class="text-white font-medium mb-3">Payload Format</h4>
                <div class="code-block text-sm">
{
  "event": "auth_success",
  "timestamp": "2025-01-31T12:00:00Z",
  "app_id": "123",
  "app_name": "My App",
  "data": {
    "license_key": "ABC123...",
    "hwid": "device123",
    "ip_address": "***********",
    "user_agent": "MyApp/1.0"
  }
}
                </div>
            </div>
            
            <!-- Security -->
            <div>
                <h4 class="text-white font-medium mb-3">Security</h4>
                <div class="space-y-3 text-sm text-gray-300">
                    <p>• All webhooks include an HMAC-SHA256 signature in the <code class="text-pepe-green">X-Signature</code> header</p>
                    <p>• Verify the signature using your webhook secret</p>
                    <p>• Only HTTPS URLs are supported</p>
                    <p>• Webhooks timeout after 10 seconds</p>
                </div>
            </div>
            
            <!-- Event Types -->
            <div>
                <h4 class="text-white font-medium mb-3">Event Types</h4>
                <div class="space-y-2 text-sm text-gray-300">
                    <div><code class="text-pepe-green">auth_success</code> - Successful license authentication</div>
                    <div><code class="text-pepe-green">auth_fail</code> - Failed authentication attempt</div>
                    <div><code class="text-pepe-green">key_created</code> - New license key generated</div>
                    <div><code class="text-pepe-green">key_banned</code> - License key banned</div>
                    <div><code class="text-pepe-green">hwid_reset</code> - Hardware ID reset</div>
                    <div><code class="text-pepe-green">key_expired</code> - License key expired</div>
                </div>
            </div>
            
            <!-- Response Codes -->
            <div>
                <h4 class="text-white font-medium mb-3">Expected Response</h4>
                <div class="space-y-2 text-sm text-gray-300">
                    <p>• Return HTTP 200 for successful processing</p>
                    <p>• Return HTTP 4xx/5xx for errors</p>
                    <p>• Failed deliveries are retried up to 3 times</p>
                    <p>• Exponential backoff between retries</p>
                </div>
            </div>
        </div>
        
        <!-- Example Implementation -->
        <div class="mt-6">
            <h4 class="text-white font-medium mb-3">Example Implementation (Python)</h4>
            <div class="code-block text-sm">
import hmac
import hashlib
from flask import Flask, request

app = Flask(__name__)
WEBHOOK_SECRET = "your-webhook-secret"

@app.route('/webhook', methods=['POST'])
def handle_webhook():
    # Verify signature
    signature = request.headers.get('X-Signature')
    payload = request.get_data()
    expected = hmac.new(
        WEBHOOK_SECRET.encode(),
        payload,
        hashlib.sha256
    ).hexdigest()
    
    if not hmac.compare_digest(signature, expected):
        return 'Invalid signature', 401
    
    # Process webhook
    data = request.get_json()
    print(f"Received {data['event']} event")
    
    return 'OK', 200
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let selectedAppId = null;

function selectApp(appId, appName) {
    selectedAppId = appId;
    document.getElementById('selected-app-name').textContent = appName;
    document.getElementById('webhook-config').style.display = 'block';
    document.getElementById('webhook-logs').style.display = 'block';
    
    // Highlight selected app
    document.querySelectorAll('.app-selector').forEach(el => {
        el.classList.remove('border-2', 'border-pepe-green');
    });
    document.querySelector(`[data-app-id="${appId}"]`).classList.add('border-2', 'border-pepe-green');
    
    // Load existing webhook configuration
    loadWebhookConfig(appId);
    loadWebhookLogs(appId);
}

function loadWebhookConfig(appId) {
    fetch(`/dashboard/app/${appId}/webhook`)
        .then(response => response.json())
        .then(data => {
            if (data.webhook_url) {
                document.getElementById('webhook_url').value = data.webhook_url;
                document.getElementById('webhook_secret').value = data.webhook_secret || '';
                document.getElementById('test-webhook-btn').disabled = false;
                
                // Set event checkboxes
                const events = data.events || ['auth_success', 'auth_fail'];
                document.querySelectorAll('input[name="events"]').forEach(checkbox => {
                    checkbox.checked = events.includes(checkbox.value);
                });
            }
        });
}

function loadWebhookLogs(appId) {
    fetch(`/dashboard/app/${appId}/webhook/logs`)
        .then(response => response.json())
        .then(data => {
            const tbody = document.querySelector('#webhook-logs-table tbody');
            tbody.innerHTML = '';
            
            if (data.logs && data.logs.length > 0) {
                data.logs.forEach(log => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>
                            <span class="badge badge-info">${log.event_type}</span>
                        </td>
                        <td>
                            <span class="badge ${log.success ? 'badge-success' : 'badge-error'}">
                                ${log.success ? 'Success' : 'Failed'}
                            </span>
                        </td>
                        <td>
                            <span class="text-white">${log.response_code || 'N/A'}</span>
                        </td>
                        <td>
                            <span class="text-gray-400 text-sm">${new Date(log.created_at).toLocaleString()}</span>
                        </td>
                        <td>
                            <button onclick="viewLogDetails(${log.id})" class="text-blue-400 hover:text-blue-300 text-sm">
                                View Details
                            </button>
                        </td>
                    `;
                    tbody.appendChild(row);
                });
            } else {
                tbody.innerHTML = '<tr><td colspan="5" class="text-center text-gray-400 py-8">No webhook deliveries yet</td></tr>';
            }
        });
}

function generateSecret() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < 32; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    document.getElementById('webhook_secret').value = result;
}

function testWebhook() {
    if (!selectedAppId) return;
    
    const url = document.getElementById('webhook_url').value;
    if (!url) {
        alert('Please enter a webhook URL first.');
        return;
    }
    
    const btn = document.getElementById('test-webhook-btn');
    btn.disabled = true;
    btn.innerHTML = '<div class="spinner mr-2"></div>Testing...';
    
    fetch(`/dashboard/app/${selectedAppId}/webhook/test`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('meta[name=csrf-token]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Test webhook sent successfully!', 'success');
        } else {
            showNotification('Test webhook failed: ' + data.message, 'error');
        }
    })
    .finally(() => {
        btn.disabled = false;
        btn.innerHTML = '<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path></svg>Test Webhook';
        loadWebhookLogs(selectedAppId);
    });
}

function clearWebhook() {
    if (!selectedAppId) return;
    
    if (confirm('Are you sure you want to clear the webhook configuration?')) {
        fetch(`/dashboard/app/${selectedAppId}/webhook`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('meta[name=csrf-token]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('webhook_url').value = '';
                document.getElementById('webhook_secret').value = '';
                document.querySelectorAll('input[name="events"]').forEach(cb => cb.checked = false);
                document.getElementById('test-webhook-btn').disabled = true;
                showNotification('Webhook configuration cleared!', 'success');
            } else {
                showNotification('Error: ' + data.message, 'error');
            }
        });
    }
}

function refreshLogs() {
    if (selectedAppId) {
        loadWebhookLogs(selectedAppId);
    }
}

function viewLogDetails(logId) {
    // Open modal or navigate to detailed log view
    window.open(`/dashboard/webhook/log/${logId}`, '_blank');
}

// Form submission
document.getElementById('webhook-form').addEventListener('submit', function(e) {
    e.preventDefault();
    
    if (!selectedAppId) return;
    
    const formData = new FormData(this);
    const events = Array.from(document.querySelectorAll('input[name="events"]:checked')).map(cb => cb.value);
    
    const data = {
        webhook_url: formData.get('webhook_url'),
        webhook_secret: formData.get('webhook_secret'),
        events: events
    };
    
    fetch(`/dashboard/app/${selectedAppId}/webhook`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('meta[name=csrf-token]').getAttribute('content')
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.getElementById('test-webhook-btn').disabled = false;
            showNotification('Webhook configuration saved!', 'success');
        } else {
            showNotification('Error: ' + data.message, 'error');
        }
    });
});

function showNotification(message, type) {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg ${
        type === 'success' ? 'bg-green-500' : 'bg-red-500'
    } text-white`;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.remove();
    }, 3000);
}
</script>
{% endblock %}
