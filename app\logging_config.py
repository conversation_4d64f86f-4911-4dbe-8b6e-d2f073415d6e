"""
Comprehensive logging configuration for PepeAuth
Includes security logging, audit trails, and performance monitoring
"""

import logging
import logging.handlers
import os
import json
from datetime import datetime
from flask import request, g, current_app
from functools import wraps

class JSONFormatter(logging.Formatter):
    """Custom JSON formatter for structured logging"""
    
    def format(self, record):
        log_entry = {
            'timestamp': datetime.utcnow().isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno
        }
        
        # Add request context if available
        if hasattr(g, 'request_id'):
            log_entry['request_id'] = g.request_id
        
        if request:
            log_entry['request'] = {
                'method': request.method,
                'url': request.url,
                'remote_addr': request.remote_addr,
                'user_agent': request.headers.get('User-Agent', '')
            }
        
        # Add extra fields from record
        if hasattr(record, 'user_id'):
            log_entry['user_id'] = record.user_id
        
        if hasattr(record, 'app_id'):
            log_entry['app_id'] = record.app_id
        
        if hasattr(record, 'license_key'):
            log_entry['license_key'] = record.license_key
        
        if hasattr(record, 'extra_data'):
            log_entry['extra_data'] = record.extra_data
        
        return json.dumps(log_entry)

class SecurityAuditLogger:
    """Specialized logger for security events"""
    
    def __init__(self, app=None):
        self.app = app
        if app:
            self.init_app(app)
    
    def init_app(self, app):
        """Initialize security logging for Flask app"""
        # Create logs directory
        log_dir = app.config.get('LOG_DIR', 'logs')
        os.makedirs(log_dir, exist_ok=True)
        
        # Security logger
        security_logger = logging.getLogger('pepeauth.security')
        security_logger.setLevel(logging.INFO)
        
        # Security log file handler
        security_handler = logging.handlers.RotatingFileHandler(
            os.path.join(log_dir, 'security.log'),
            maxBytes=10*1024*1024,  # 10MB
            backupCount=10
        )
        security_handler.setFormatter(JSONFormatter())
        security_logger.addHandler(security_handler)
        
        # Audit logger
        audit_logger = logging.getLogger('pepeauth.audit')
        audit_logger.setLevel(logging.INFO)
        
        # Audit log file handler
        audit_handler = logging.handlers.RotatingFileHandler(
            os.path.join(log_dir, 'audit.log'),
            maxBytes=10*1024*1024,  # 10MB
            backupCount=10
        )
        audit_handler.setFormatter(JSONFormatter())
        audit_logger.addHandler(audit_handler)
        
        # API logger
        api_logger = logging.getLogger('pepeauth.api')
        api_logger.setLevel(logging.INFO)
        
        # API log file handler
        api_handler = logging.handlers.RotatingFileHandler(
            os.path.join(log_dir, 'api.log'),
            maxBytes=10*1024*1024,  # 10MB
            backupCount=10
        )
        api_handler.setFormatter(JSONFormatter())
        api_logger.addHandler(api_handler)
        
        # Performance logger
        perf_logger = logging.getLogger('pepeauth.performance')
        perf_logger.setLevel(logging.INFO)
        
        # Performance log file handler
        perf_handler = logging.handlers.RotatingFileHandler(
            os.path.join(log_dir, 'performance.log'),
            maxBytes=10*1024*1024,  # 10MB
            backupCount=10
        )
        perf_handler.setFormatter(JSONFormatter())
        perf_logger.addHandler(perf_handler)
        
        # Error logger
        error_logger = logging.getLogger('pepeauth.error')
        error_logger.setLevel(logging.ERROR)
        
        # Error log file handler
        error_handler = logging.handlers.RotatingFileHandler(
            os.path.join(log_dir, 'error.log'),
            maxBytes=10*1024*1024,  # 10MB
            backupCount=10
        )
        error_handler.setFormatter(JSONFormatter())
        error_logger.addHandler(error_handler)
        
        # Console handler for development
        if app.debug:
            console_handler = logging.StreamHandler()
            console_handler.setFormatter(logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            ))
            
            security_logger.addHandler(console_handler)
            audit_logger.addHandler(console_handler)
            api_logger.addHandler(console_handler)
            error_logger.addHandler(console_handler)
        
        # Store loggers in app
        app.security_logger = security_logger
        app.audit_logger = audit_logger
        app.api_logger = api_logger
        app.perf_logger = perf_logger
        app.error_logger = error_logger

class AuditTrail:
    """Audit trail for tracking user actions"""
    
    @staticmethod
    def log_user_action(action, user_id=None, details=None, resource_type=None, resource_id=None):
        """Log user action for audit trail"""
        audit_logger = logging.getLogger('pepeauth.audit')
        
        extra = {
            'user_id': user_id,
            'action': action,
            'resource_type': resource_type,
            'resource_id': resource_id,
            'details': details
        }
        
        audit_logger.info(f'User action: {action}', extra=extra)
    
    @staticmethod
    def log_admin_action(action, admin_id, target_user_id=None, details=None):
        """Log admin action for audit trail"""
        audit_logger = logging.getLogger('pepeauth.audit')
        
        extra = {
            'admin_id': admin_id,
            'target_user_id': target_user_id,
            'action': action,
            'details': details
        }
        
        audit_logger.info(f'Admin action: {action}', extra=extra)
    
    @staticmethod
    def log_system_change(change_type, details=None, user_id=None):
        """Log system configuration changes"""
        audit_logger = logging.getLogger('pepeauth.audit')
        
        extra = {
            'user_id': user_id,
            'change_type': change_type,
            'details': details
        }
        
        audit_logger.info(f'System change: {change_type}', extra=extra)

class PerformanceMonitor:
    """Performance monitoring and logging"""
    
    @staticmethod
    def log_request_performance(endpoint, duration, status_code, user_id=None):
        """Log request performance metrics"""
        perf_logger = logging.getLogger('pepeauth.performance')
        
        extra = {
            'endpoint': endpoint,
            'duration_ms': duration * 1000,
            'status_code': status_code,
            'user_id': user_id
        }
        
        perf_logger.info(f'Request performance: {endpoint} - {duration:.3f}s', extra=extra)
    
    @staticmethod
    def log_database_query(query, duration, result_count=None):
        """Log database query performance"""
        perf_logger = logging.getLogger('pepeauth.performance')
        
        extra = {
            'query_type': 'database',
            'duration_ms': duration * 1000,
            'result_count': result_count,
            'query': query[:200] + '...' if len(query) > 200 else query
        }
        
        perf_logger.info(f'Database query: {duration:.3f}s', extra=extra)

# Decorators for logging
def log_user_action(action, resource_type=None):
    """Decorator to log user actions"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            user_id = getattr(g, 'current_user_id', None)
            
            try:
                result = f(*args, **kwargs)
                
                # Extract resource ID from result if possible
                resource_id = None
                if hasattr(result, 'get_json') and result.get_json():
                    data = result.get_json()
                    resource_id = data.get('id') or data.get('resource_id')
                
                AuditTrail.log_user_action(
                    action=action,
                    user_id=user_id,
                    resource_type=resource_type,
                    resource_id=resource_id,
                    details=f'Endpoint: {request.endpoint}'
                )
                
                return result
                
            except Exception as e:
                AuditTrail.log_user_action(
                    action=f'{action}_failed',
                    user_id=user_id,
                    resource_type=resource_type,
                    details=f'Error: {str(e)}'
                )
                raise
        
        return decorated_function
    return decorator

def log_performance():
    """Decorator to log request performance"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            start_time = datetime.utcnow()
            
            try:
                result = f(*args, **kwargs)
                end_time = datetime.utcnow()
                duration = (end_time - start_time).total_seconds()
                
                status_code = getattr(result, 'status_code', 200)
                user_id = getattr(g, 'current_user_id', None)
                
                PerformanceMonitor.log_request_performance(
                    endpoint=request.endpoint,
                    duration=duration,
                    status_code=status_code,
                    user_id=user_id
                )
                
                return result
                
            except Exception as e:
                end_time = datetime.utcnow()
                duration = (end_time - start_time).total_seconds()
                
                PerformanceMonitor.log_request_performance(
                    endpoint=request.endpoint,
                    duration=duration,
                    status_code=500,
                    user_id=getattr(g, 'current_user_id', None)
                )
                raise
        
        return decorated_function
    return decorator

def log_api_call():
    """Decorator to log API calls"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            api_logger = logging.getLogger('pepeauth.api')
            
            # Log request
            extra = {
                'method': request.method,
                'endpoint': request.endpoint,
                'remote_addr': request.remote_addr,
                'user_agent': request.headers.get('User-Agent', ''),
                'content_length': request.content_length
            }
            
            api_logger.info(f'API call: {request.method} {request.endpoint}', extra=extra)
            
            try:
                result = f(*args, **kwargs)
                
                # Log response
                extra['status_code'] = getattr(result, 'status_code', 200)
                api_logger.info(f'API response: {extra["status_code"]}', extra=extra)
                
                return result
                
            except Exception as e:
                extra['status_code'] = 500
                extra['error'] = str(e)
                api_logger.error(f'API error: {str(e)}', extra=extra)
                raise
        
        return decorated_function
    return decorator

# Request ID middleware
def generate_request_id():
    """Generate unique request ID"""
    import uuid
    return str(uuid.uuid4())

def setup_request_logging(app):
    """Setup request-level logging"""
    
    @app.before_request
    def before_request():
        g.request_id = generate_request_id()
        g.request_start_time = datetime.utcnow()
    
    @app.after_request
    def after_request(response):
        if hasattr(g, 'request_start_time'):
            duration = (datetime.utcnow() - g.request_start_time).total_seconds()
            
            # Log slow requests
            if duration > 1.0:  # Log requests taking more than 1 second
                perf_logger = logging.getLogger('pepeauth.performance')
                perf_logger.warning(f'Slow request: {request.endpoint} - {duration:.3f}s')
        
        return response
    
    return app
