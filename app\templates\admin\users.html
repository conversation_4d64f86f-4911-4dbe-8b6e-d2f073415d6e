{% extends "base.html" %}

{% block title %}User Management - Admin - PepeAuth{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div>
                <h1 class="text-3xl font-bold text-white">User Management</h1>
                <p class="mt-1 text-gray-300">Manage all user accounts and permissions</p>
            </div>
            <div class="mt-4 sm:mt-0 flex space-x-3">
                <a href="{{ url_for('admin.create_user') }}" class="btn-primary">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"></path>
                    </svg>
                    Create User
                </a>
                <button onclick="exportUsers()" class="btn-secondary">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    Export
                </button>
            </div>
        </div>
    </div>
    
    <!-- Stats Overview -->
    <div class="grid grid-cols-1 md:grid-cols-5 gap-6 mb-8">
        <div class="glass-card p-6 rounded-xl">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-blue-500/20 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-300">Total Users</p>
                    <p class="text-2xl font-semibold text-white">{{ stats.total_users }}</p>
                </div>
            </div>
        </div>
        
        <div class="glass-card p-6 rounded-xl">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-green-500/20 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-300">Active</p>
                    <p class="text-2xl font-semibold text-white">{{ stats.active_users }}</p>
                </div>
            </div>
        </div>
        
        <div class="glass-card p-6 rounded-xl">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-red-500/20 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-300">Banned</p>
                    <p class="text-2xl font-semibold text-white">{{ stats.banned_users }}</p>
                </div>
            </div>
        </div>
        
        <div class="glass-card p-6 rounded-xl">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-purple-500/20 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-300">Admins</p>
                    <p class="text-2xl font-semibold text-white">{{ stats.admin_users }}</p>
                </div>
            </div>
        </div>
        
        <div class="glass-card p-6 rounded-xl">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-yellow-500/20 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-300">New Today</p>
                    <p class="text-2xl font-semibold text-white">{{ stats.new_users_today }}</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Filters and Search -->
    <div class="glass-card p-6 rounded-xl mb-8">
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            <div class="flex-1 max-w-lg">
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </div>
                    <input type="text" 
                           class="input-field pl-10" 
                           placeholder="Search users..."
                           id="search-input">
                </div>
            </div>
            <div class="flex flex-wrap items-center gap-4">
                <select class="input-field w-auto" id="plan-filter">
                    <option value="">All Plans</option>
                    {% for plan in plans %}
                        <option value="{{ plan.id }}">{{ plan.name }}</option>
                    {% endfor %}
                </select>
                <select class="input-field w-auto" id="status-filter">
                    <option value="">All Status</option>
                    <option value="active">Active</option>
                    <option value="banned">Banned</option>
                    <option value="admin">Admin</option>
                </select>
                <select class="input-field w-auto" id="sort-filter">
                    <option value="created_desc">Newest First</option>
                    <option value="created_asc">Oldest First</option>
                    <option value="username_asc">Username A-Z</option>
                    <option value="username_desc">Username Z-A</option>
                    <option value="last_login_desc">Last Login</option>
                </select>
                <button onclick="resetFilters()" class="btn-secondary text-sm">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    Reset
                </button>
            </div>
        </div>
    </div>
    
    <!-- Users Table -->
    <div class="glass-card rounded-xl overflow-hidden">
        <div class="overflow-x-auto">
            <table class="table-glass" id="users-table">
                <thead>
                    <tr>
                        <th class="w-4">
                            <input type="checkbox" id="select-all" class="w-4 h-4 text-pepe-green bg-pepe-gray border-pepe-light-gray rounded focus:ring-pepe-green focus:ring-2">
                        </th>
                        <th>User</th>
                        <th>Plan</th>
                        <th>Status</th>
                        <th>Apps</th>
                        <th>Keys</th>
                        <th>Last Login</th>
                        <th>Created</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for user in users %}
                        <tr class="user-row" 
                            data-plan="{{ user.plan_id }}" 
                            data-status="{{ 'banned' if user.is_banned else ('admin' if user.is_admin else 'active') }}"
                            data-username="{{ user.username.lower() }}"
                            data-email="{{ user.email.lower() }}">
                            <td>
                                <input type="checkbox" class="user-checkbox w-4 h-4 text-pepe-green bg-pepe-gray border-pepe-light-gray rounded focus:ring-pepe-green focus:ring-2" value="{{ user.id }}">
                            </td>
                            <td>
                                <div class="flex items-center space-x-3">
                                    <div class="flex-shrink-0">
                                        <div class="w-8 h-8 bg-pepe-green rounded-full flex items-center justify-center">
                                            <span class="text-white font-medium text-sm">{{ user.username[0].upper() }}</span>
                                        </div>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-white">{{ user.username }}</p>
                                        <p class="text-xs text-gray-400">{{ user.email }}</p>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span class="badge badge-info">{{ user.plan.name }}</span>
                            </td>
                            <td>
                                {% if user.is_banned %}
                                    <span class="badge badge-error">Banned</span>
                                {% elif user.is_admin %}
                                    <span class="badge badge-success">Admin</span>
                                {% else %}
                                    <span class="badge badge-info">Active</span>
                                {% endif %}
                            </td>
                            <td>
                                <span class="text-white">{{ user.app_count }}</span>
                            </td>
                            <td>
                                <span class="text-white">{{ user.key_count }}</span>
                            </td>
                            <td>
                                {% if user.last_login %}
                                    <span class="text-sm text-gray-400">{{ user.last_login.strftime('%Y-%m-%d') }}</span>
                                {% else %}
                                    <span class="text-sm text-gray-500">Never</span>
                                {% endif %}
                            </td>
                            <td>
                                <span class="text-sm text-gray-400">{{ user.created_at.strftime('%Y-%m-%d') }}</span>
                            </td>
                            <td>
                                <div class="flex items-center space-x-2">
                                    <a href="{{ url_for('admin.edit_user', user_id=user.id) }}" 
                                       class="text-blue-400 hover:text-blue-300 transition-colors" 
                                       title="Edit User">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                        </svg>
                                    </a>
                                    <a href="{{ url_for('admin.user_activity', user_id=user.id) }}" 
                                       class="text-purple-400 hover:text-purple-300 transition-colors" 
                                       title="View Activity">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                        </svg>
                                    </a>
                                    {% if not user.is_banned %}
                                        <button onclick="banUser({{ user.id }}, '{{ user.username }}')" 
                                                class="text-red-400 hover:text-red-300 transition-colors" 
                                                title="Ban User">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728"></path>
                                            </svg>
                                        </button>
                                    {% else %}
                                        <button onclick="unbanUser({{ user.id }}, '{{ user.username }}')" 
                                                class="text-green-400 hover:text-green-300 transition-colors" 
                                                title="Unban User">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                            </svg>
                                        </button>
                                    {% endif %}
                                    {% if user.id != current_user.id %}
                                        <button onclick="deleteUser({{ user.id }}, '{{ user.username }}')" 
                                                class="text-red-400 hover:text-red-300 transition-colors" 
                                                title="Delete User">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                            </svg>
                                        </button>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- Bulk Actions -->
        <div class="p-4 border-t border-pepe-light-gray/20 bg-pepe-darker/30" id="bulk-actions" style="display: none;">
            <div class="flex items-center justify-between">
                <span class="text-sm text-gray-300" id="selected-count">0 users selected</span>
                <div class="flex space-x-2">
                    <button onclick="bulkBan()" class="btn-danger text-sm">Ban Selected</button>
                    <button onclick="bulkUnban()" class="btn-secondary text-sm">Unban Selected</button>
                    <button onclick="bulkChangePlan()" class="btn-secondary text-sm">Change Plan</button>
                    <button onclick="bulkExport()" class="btn-secondary text-sm">Export Selected</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Pagination -->
    {% if pagination.pages > 1 %}
        <div class="flex items-center justify-between mt-8">
            <div class="text-sm text-gray-400">
                Showing {{ pagination.per_page * (pagination.page - 1) + 1 }} to {{ pagination.per_page * pagination.page if pagination.page < pagination.pages else pagination.total }} of {{ pagination.total }} users
            </div>
            <div class="flex space-x-2">
                {% if pagination.has_prev %}
                    <a href="{{ url_for('admin.users', page=pagination.prev_num) }}" class="btn-secondary text-sm">Previous</a>
                {% endif %}
                
                {% for page_num in pagination.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != pagination.page %}
                            <a href="{{ url_for('admin.users', page=page_num) }}" class="btn-secondary text-sm">{{ page_num }}</a>
                        {% else %}
                            <span class="btn-primary text-sm">{{ page_num }}</span>
                        {% endif %}
                    {% else %}
                        <span class="text-gray-400">...</span>
                    {% endif %}
                {% endfor %}
                
                {% if pagination.has_next %}
                    <a href="{{ url_for('admin.users', page=pagination.next_num) }}" class="btn-secondary text-sm">Next</a>
                {% endif %}
            </div>
        </div>
    {% endif %}
</div>
{% endblock %}

{% block scripts %}
<script>
// Search and filter functionality
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('search-input');
    const planFilter = document.getElementById('plan-filter');
    const statusFilter = document.getElementById('status-filter');
    const sortFilter = document.getElementById('sort-filter');
    const selectAll = document.getElementById('select-all');
    const userCheckboxes = document.querySelectorAll('.user-checkbox');
    const bulkActions = document.getElementById('bulk-actions');
    const selectedCount = document.getElementById('selected-count');
    
    // Search functionality
    searchInput.addEventListener('input', filterUsers);
    planFilter.addEventListener('change', filterUsers);
    statusFilter.addEventListener('change', filterUsers);
    sortFilter.addEventListener('change', sortUsers);
    
    // Select all functionality
    selectAll.addEventListener('change', function() {
        userCheckboxes.forEach(checkbox => {
            if (checkbox.closest('tr').style.display !== 'none') {
                checkbox.checked = this.checked;
            }
        });
        updateBulkActions();
    });
    
    // Individual checkbox functionality
    userCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateBulkActions);
    });
    
    function filterUsers() {
        const searchTerm = searchInput.value.toLowerCase();
        const planId = planFilter.value;
        const status = statusFilter.value;
        
        document.querySelectorAll('.user-row').forEach(row => {
            const username = row.dataset.username;
            const email = row.dataset.email;
            const rowPlan = row.dataset.plan;
            const rowStatus = row.dataset.status;
            
            const matchesSearch = !searchTerm || username.includes(searchTerm) || email.includes(searchTerm);
            const matchesPlan = !planId || rowPlan === planId;
            const matchesStatus = !status || rowStatus === status;
            
            if (matchesSearch && matchesPlan && matchesStatus) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
                row.querySelector('.user-checkbox').checked = false;
            }
        });
        
        updateBulkActions();
    }
    
    function sortUsers() {
        const sortBy = sortFilter.value;
        const tbody = document.querySelector('#users-table tbody');
        const rows = Array.from(tbody.querySelectorAll('.user-row'));
        
        rows.sort((a, b) => {
            let aVal, bVal;
            
            switch(sortBy) {
                case 'username_asc':
                    aVal = a.dataset.username;
                    bVal = b.dataset.username;
                    return aVal.localeCompare(bVal);
                case 'username_desc':
                    aVal = a.dataset.username;
                    bVal = b.dataset.username;
                    return bVal.localeCompare(aVal);
                case 'created_asc':
                    aVal = a.querySelector('td:nth-child(8)').textContent;
                    bVal = b.querySelector('td:nth-child(8)').textContent;
                    return new Date(aVal) - new Date(bVal);
                case 'created_desc':
                default:
                    aVal = a.querySelector('td:nth-child(8)').textContent;
                    bVal = b.querySelector('td:nth-child(8)').textContent;
                    return new Date(bVal) - new Date(aVal);
            }
        });
        
        rows.forEach(row => tbody.appendChild(row));
    }
    
    function updateBulkActions() {
        const checkedBoxes = document.querySelectorAll('.user-checkbox:checked');
        const count = checkedBoxes.length;
        
        if (count > 0) {
            bulkActions.style.display = 'block';
            selectedCount.textContent = `${count} user${count > 1 ? 's' : ''} selected`;
        } else {
            bulkActions.style.display = 'none';
        }
        
        // Update select all checkbox
        const visibleCheckboxes = Array.from(userCheckboxes).filter(cb => 
            cb.closest('tr').style.display !== 'none'
        );
        const checkedVisible = visibleCheckboxes.filter(cb => cb.checked);
        
        selectAll.indeterminate = checkedVisible.length > 0 && checkedVisible.length < visibleCheckboxes.length;
        selectAll.checked = visibleCheckboxes.length > 0 && checkedVisible.length === visibleCheckboxes.length;
    }
});

// User management functions
function banUser(userId, username) {
    const reason = prompt(`Enter ban reason for ${username}:`);
    if (reason !== null) {
        fetch(`/admin/user/${userId}/ban`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('meta[name=csrf-token]').getAttribute('content')
            },
            body: JSON.stringify({ reason: reason })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification(`User ${username} banned successfully!`, 'success');
                location.reload();
            } else {
                showNotification('Error: ' + data.message, 'error');
            }
        });
    }
}

function unbanUser(userId, username) {
    if (confirm(`Are you sure you want to unban ${username}?`)) {
        fetch(`/admin/user/${userId}/unban`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('meta[name=csrf-token]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification(`User ${username} unbanned successfully!`, 'success');
                location.reload();
            } else {
                showNotification('Error: ' + data.message, 'error');
            }
        });
    }
}

function deleteUser(userId, username) {
    const userInput = prompt(`Are you sure you want to delete ${username}? This action cannot be undone.\n\nType "DELETE" to confirm:`);
    if (userInput === 'DELETE') {
        fetch(`/admin/user/${userId}/delete`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('meta[name=csrf-token]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification(`User ${username} deleted successfully!`, 'success');
                location.reload();
            } else {
                showNotification('Error: ' + data.message, 'error');
            }
        });
    }
}

// Bulk operations
function getSelectedUsers() {
    return Array.from(document.querySelectorAll('.user-checkbox:checked')).map(cb => cb.value);
}

function bulkBan() {
    const users = getSelectedUsers();
    if (users.length === 0) return;
    
    const reason = prompt(`Enter ban reason for ${users.length} users:`);
    if (reason !== null) {
        fetch('/admin/users/bulk-ban', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('meta[name=csrf-token]').getAttribute('content')
            },
            body: JSON.stringify({ users: users, reason: reason })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification(`${users.length} users banned successfully!`, 'success');
                location.reload();
            } else {
                showNotification('Error: ' + data.message, 'error');
            }
        });
    }
}

function bulkUnban() {
    const users = getSelectedUsers();
    if (users.length === 0) return;
    
    if (confirm(`Are you sure you want to unban ${users.length} users?`)) {
        fetch('/admin/users/bulk-unban', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('meta[name=csrf-token]').getAttribute('content')
            },
            body: JSON.stringify({ users: users })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification(`${users.length} users unbanned successfully!`, 'success');
                location.reload();
            } else {
                showNotification('Error: ' + data.message, 'error');
            }
        });
    }
}

function bulkChangePlan() {
    const users = getSelectedUsers();
    if (users.length === 0) return;

    // Create a simple plan selection
    const plans = ['1 - Trial', '2 - Premium', '3 - Lifetime'];
    const selection = prompt(`Select plan for ${users.length} users:\n${plans.join('\n')}\n\nEnter plan number (1, 2, or 3):`);

    if (selection && ['1', '2', '3'].includes(selection)) {
        fetch('/admin/users/bulk-change-plan', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('meta[name=csrf-token]').getAttribute('content')
            },
            body: JSON.stringify({ users: users, plan_id: parseInt(selection) })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification(`Plan changed for ${users.length} users!`, 'success');
                location.reload();
            } else {
                showNotification('Error: ' + data.message, 'error');
            }
        });
    }
}

function bulkExport() {
    const users = getSelectedUsers();
    if (users.length === 0) return;
    
    window.location.href = `/admin/users/export?users=${users.join(',')}`;
}

function exportUsers() {
    window.location.href = '/admin/users/export';
}

function resetFilters() {
    document.getElementById('search-input').value = '';
    document.getElementById('plan-filter').value = '';
    document.getElementById('status-filter').value = '';
    document.getElementById('sort-filter').value = 'created_desc';
    
    document.querySelectorAll('.user-row').forEach(row => {
        row.style.display = '';
    });
    
    document.querySelectorAll('.user-checkbox').forEach(cb => {
        cb.checked = false;
    });
    
    document.getElementById('select-all').checked = false;
    document.getElementById('bulk-actions').style.display = 'none';
}

function showNotification(message, type) {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg ${
        type === 'success' ? 'bg-green-500' : 'bg-red-500'
    } text-white`;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.remove();
    }, 3000);
}
</script>
{% endblock %}
