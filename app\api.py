import logging
from datetime import datetime, timedelta
from flask import Blueprint, request, jsonify, g
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
from app import query_db, execute_db, limiter

bp = Blueprint('api', __name__, url_prefix='/api/v1')

def get_rate_limit():
    """Get rate limit based on app's owner plan"""
    app_name = request.json.get('app_name') if request.is_json else request.form.get('app_name')
    if not app_name:
        return "100 per day"  # Default limit
    
    # Get app and owner's plan
    app_data = query_db(
        'SELECT u.plan_id, p.rate_limit FROM app a '
        'JOIN user u ON a.owner_id = u.id '
        'JOIN plan p ON u.plan_id = p.id '
        'WHERE a.name = ?',
        (app_name,),
        one=True
    )
    
    if not app_data or app_data['rate_limit'] == -1:
        return "10000 per day"  # Unlimited or very high limit
    
    return f"{app_data['rate_limit']} per day"

@bp.route('/auth', methods=['POST'])
@limiter.limit(get_rate_limit)
def authenticate():
    """
    Authenticate and activate a license key
    
    Expected JSON payload:
    {
        "app_name": "MyApp",
        "key": "license-key-here",
        "hwid": "hardware-id-here" (optional)
    }
    
    Returns:
    - 200: Success with expiry date
    - 400: Bad request (missing parameters)
    - 404: App not found
    - 403: Invalid key, expired, or HWID mismatch
    - 429: Rate limit exceeded
    """
    try:
        # Parse request data
        if request.is_json:
            data = request.get_json()
        else:
            data = request.form.to_dict()
        
        app_name = data.get('app_name')
        license_key = data.get('key')
        hwid = data.get('hwid')
        
        # Validate required parameters
        if not app_name or not license_key:
            return jsonify({
                'status': False,
                'message': 'app_name and key are required'
            }), 400
        
        # Find the app
        app = query_db(
            'SELECT * FROM app WHERE name = ?',
            (app_name,),
            one=True
        )
        
        if not app:
            logging.warning(f'API auth attempt for non-existent app: {app_name}')
            return jsonify({
                'status': False,
                'message': 'App not found'
            }), 404
        
        # Find the license key
        key_data = query_db(
            'SELECT * FROM license_key WHERE key = ? AND app_id = ?',
            (license_key, app['id']),
            one=True
        )
        
        if not key_data:
            logging.warning(f'API auth attempt with invalid key: {license_key} for app: {app_name}')
            return jsonify({
                'status': False,
                'message': 'Invalid license key'
            }), 403
        
        # Check if key is already used and HWID lock is enabled
        if key_data['used'] and app['hwid_lock']:
            if not hwid:
                return jsonify({
                    'status': False,
                    'message': 'Hardware ID required for this app'
                }), 403
            
            if key_data['hwid'] != hwid:
                logging.warning(f'HWID mismatch for key: {license_key}, expected: {key_data["hwid"]}, got: {hwid}')
                return jsonify({
                    'status': False,
                    'message': 'Hardware ID mismatch'
                }), 403
        
        # Check if key is expired
        if key_data['expires_at']:
            expires_at = datetime.fromisoformat(key_data['expires_at'])
            if expires_at < datetime.now():
                return jsonify({
                    'status': False,
                    'message': 'License key has expired'
                }), 403
        
        # Activate unused key
        if not key_data['used']:
            expires_at = datetime.now() + timedelta(days=app['expiry_days'])
            
            execute_db(
                'UPDATE license_key SET used = 1, used_at = ?, hwid = ?, expires_at = ? WHERE id = ?',
                (datetime.now().isoformat(), hwid, expires_at.isoformat(), key_data['id'])
            )
            
            logging.info(f'License key activated: {license_key} for app: {app_name}')
            
            return jsonify({
                'status': True,
                'message': 'License activated successfully',
                'expires': expires_at.isoformat()
            }), 200
        
        # Key is already used, return current expiry
        return jsonify({
            'status': True,
            'message': 'License is valid',
            'expires': key_data['expires_at']
        }), 200
        
    except Exception as e:
        logging.error(f'API auth error: {str(e)}')
        return jsonify({
            'status': False,
            'message': 'Internal server error'
        }), 500

@bp.route('/check_key', methods=['POST'])
@limiter.limit(get_rate_limit)
def check_key():
    """
    Check if a license key is valid without activating it
    
    Expected JSON payload:
    {
        "app_name": "MyApp",
        "key": "license-key-here"
    }
    
    Returns:
    - 200: Key status information
    - 400: Bad request (missing parameters)
    - 404: App or key not found
    - 429: Rate limit exceeded
    """
    try:
        # Parse request data
        if request.is_json:
            data = request.get_json()
        else:
            data = request.form.to_dict()
        
        app_name = data.get('app_name')
        license_key = data.get('key')
        
        # Validate required parameters
        if not app_name or not license_key:
            return jsonify({
                'status': False,
                'message': 'app_name and key are required'
            }), 400
        
        # Find the app
        app = query_db(
            'SELECT * FROM app WHERE name = ?',
            (app_name,),
            one=True
        )
        
        if not app:
            return jsonify({
                'status': False,
                'message': 'App not found'
            }), 404
        
        # Find the license key
        key_data = query_db(
            'SELECT * FROM license_key WHERE key = ? AND app_id = ?',
            (license_key, app['id']),
            one=True
        )
        
        if not key_data:
            return jsonify({
                'status': False,
                'message': 'License key not found'
            }), 404
        
        # Check if key is expired
        is_expired = False
        if key_data['expires_at']:
            expires_at = datetime.fromisoformat(key_data['expires_at'])
            is_expired = expires_at < datetime.now()
        
        return jsonify({
            'status': True,
            'key_info': {
                'used': bool(key_data['used']),
                'created_at': key_data['created_at'],
                'used_at': key_data['used_at'],
                'expires_at': key_data['expires_at'],
                'expired': is_expired,
                'hwid': key_data['hwid'] if key_data['used'] else None
            }
        }), 200
        
    except Exception as e:
        logging.error(f'API check_key error: {str(e)}')
        return jsonify({
            'status': False,
            'message': 'Internal server error'
        }), 500

@bp.route('/app_info', methods=['GET', 'POST'])
@limiter.limit("50 per hour")
def app_info():
    """
    Get information about an app
    
    Parameters:
    - app_name: Name of the app
    - secret: App secret (for authenticated requests)
    
    Returns app configuration and statistics
    """
    try:
        if request.method == 'POST':
            if request.is_json:
                data = request.get_json()
            else:
                data = request.form.to_dict()
        else:
            data = request.args.to_dict()
        
        app_name = data.get('app_name')
        secret = data.get('secret')
        
        if not app_name:
            return jsonify({
                'status': False,
                'message': 'app_name is required'
            }), 400
        
        # Find the app
        app = query_db(
            'SELECT * FROM app WHERE name = ?',
            (app_name,),
            one=True
        )
        
        if not app:
            return jsonify({
                'status': False,
                'message': 'App not found'
            }), 404
        
        # Basic app info (public)
        app_info = {
            'name': app['name'],
            'hwid_lock': bool(app['hwid_lock']),
            'expiry_days': app['expiry_days']
        }
        
        # If secret is provided and correct, include statistics
        if secret and secret == app['secret']:
            stats = query_db(
                'SELECT COUNT(*) as total, '
                'COUNT(CASE WHEN used = 1 THEN 1 END) as used, '
                'COUNT(CASE WHEN expires_at < ? THEN 1 END) as expired '
                'FROM license_key WHERE app_id = ?',
                (datetime.now().isoformat(), app['id']),
                one=True
            )
            
            app_info['statistics'] = {
                'total_keys': stats['total'],
                'used_keys': stats['used'],
                'unused_keys': stats['total'] - stats['used'],
                'expired_keys': stats['expired']
            }
        
        return jsonify({
            'status': True,
            'app_info': app_info
        }), 200
        
    except Exception as e:
        logging.error(f'API app_info error: {str(e)}')
        return jsonify({
            'status': False,
            'message': 'Internal server error'
        }), 500

@bp.errorhandler(429)
def ratelimit_handler(e):
    """Handle rate limit exceeded"""
    return jsonify({
        'status': False,
        'message': 'Rate limit exceeded. Please try again later.'
    }), 429
