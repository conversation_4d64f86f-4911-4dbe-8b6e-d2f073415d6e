#!/usr/bin/env python3
"""
PepeAuth Client Example
Demonstrates how to integrate PepeAuth client into your application
"""

from client import PepeAuthClient
import sys

def example_integration():
    """Example of integrating PepeAuth into your application"""
    print("🚀 PepeAuth Integration Example")
    print("=" * 50)
    
    # Initialize the client
    client = PepeAuthClient(
        api_url="http://127.0.0.1:5000",
        app_name="MyAwesomeApp"
    )
    
    # Example license keys (from our test setup)
    test_keys = [
        "GLBHKHB46D0KWASI",  # Unused key (can be activated)
        "VDNFEORB6GPPFAZL",  # Another unused key
        "INVALID-KEY-123"    # Invalid key
    ]
    
    print("🧪 Testing different license keys:")
    print("-" * 50)
    
    for i, key in enumerate(test_keys, 1):
        print(f"\n🔑 Test {i}: {key}")
        print("-" * 30)
        
        # Attempt authentication
        if client.login(key):
            print("✅ Authentication successful!")
            
            # Get user information
            user_info = client.get_user_info()
            
            if user_info:
                # Check if license is about to expire
                days_left = user_info.get('expires_in_days', 0)
                if days_left <= 7:
                    print(f"⚠️  Warning: License expires in {days_left} days!")
                elif days_left <= 1:
                    print("🚨 Critical: License expires soon!")
                
                # Your application logic here
                print("🎮 Starting application features...")
                print("   - Premium features unlocked")
                print("   - Full access granted")
            
            # Logout for next test
            client.logout()
        else:
            print("❌ Authentication failed!")
            print("🚫 Application access denied")
        
        print("-" * 30)

def protected_function():
    """Example of a function that requires authentication"""
    client = PepeAuthClient()
    
    # This would typically be stored securely or entered by user
    license_key = "H2YZE5A1A7F4X6H3"  # Use an unused key
    
    print("🔒 Accessing protected function...")
    
    if client.login(license_key):
        print("✅ Access granted to protected function")
        
        # Your protected code here
        print("🎯 Executing premium functionality...")
        print("   - Advanced features available")
        print("   - Full API access")
        
        return True
    else:
        print("❌ Access denied - Invalid license")
        return False

def main():
    """Main example runner"""
    try:
        # Run integration example
        example_integration()
        
        print("\n" + "=" * 50)
        print("🔒 Protected Function Example")
        print("=" * 50)
        
        # Run protected function example
        protected_function()
        
        print("\n🎉 Example completed successfully!")
        
    except KeyboardInterrupt:
        print("\n👋 Example interrupted by user")
    except Exception as e:
        print(f"\n❌ Example failed: {e}")

if __name__ == "__main__":
    main()
