#!/usr/bin/env python3
"""
Test the Premium license system with real license keys
"""

from app import create_app
import json

def test_premium_system():
    """Test Premium license system"""
    app = create_app()
    
    with app.test_client() as client:
        print("🎫 Testing Premium License System")
        print("=" * 50)
        
        # Test with valid activated key (using correct HWID)
        print("🔑 Testing with activated Premium key...")
        response = client.post('/api/v1/login_key',
                             json={'key': 'DXZESZV0PDP5872H', 'hwid': 'TEST-HWID-123'},
                             content_type='application/json')
        
        print(f"Status: {response.status_code}")
        data = response.get_json()
        print(f"Response: {json.dumps(data, indent=2)}")
        
        if response.status_code == 200 and data.get('status'):
            print("✅ Activated Premium key works correctly")
            session_token = data.get('session_token')
            
            # Test user_info endpoint
            print("\n👤 Testing user_info with session token...")
            response = client.post('/api/v1/user_info', 
                                 json={'session_token': session_token, 'key': 'DXZESZV0PDP5872H'},
                                 content_type='application/json')
            
            print(f"Status: {response.status_code}")
            data = response.get_json()
            print(f"Response: {json.dumps(data, indent=2)}")
            
            if response.status_code == 200:
                print("✅ User info endpoint works correctly")
            else:
                print("❌ User info endpoint failed")
        else:
            print("❌ Activated Premium key failed")
        
        # Test with unused Premium key
        print("\n🆕 Testing with unused Premium key...")
        response = client.post('/api/v1/login_key', 
                             json={'key': 'XO9IH22LKFTG4AJQ', 'hwid': 'NEW-DEVICE-456'},
                             content_type='application/json')
        
        print(f"Status: {response.status_code}")
        data = response.get_json()
        print(f"Response: {json.dumps(data, indent=2)}")
        
        if response.status_code == 200 and data.get('status'):
            print("✅ Unused Premium key activated successfully")
        else:
            print("❌ Unused Premium key activation failed")
        
        # Test with invalid key
        print("\n❌ Testing with invalid key...")
        response = client.post('/api/v1/login_key', 
                             json={'key': 'INVALID-KEY-999'},
                             content_type='application/json')
        
        print(f"Status: {response.status_code}")
        data = response.get_json()
        print(f"Response: {json.dumps(data, indent=2)}")
        
        if response.status_code == 403 and not data.get('status'):
            print("✅ Invalid key properly rejected")
        else:
            print("❌ Invalid key test failed")
        
        print("\n" + "=" * 50)
        print("🎉 Premium system testing completed!")

if __name__ == "__main__":
    test_premium_system()
