#!/usr/bin/env python3
"""
PepeAuth Client - KeyAuth-like License Authentication
Professional Python client for license validation and user authentication
"""

import requests
import json
import hmac
import hashlib
import platform
import uuid
import sys
import os
from datetime import datetime

class PepeAuthClient:
    """Professional KeyAuth-like authentication client"""
    
    def __init__(self, api_url="http://127.0.0.1:5000", app_name="PepeAuth"):
        self.api_url = api_url.rstrip('/')
        self.app_name = app_name
        self.session_token = None
        self.user_info = None
        self.hwid = self._generate_hwid()
        
    def _generate_hwid(self):
        """Generate hardware ID for device binding"""
        try:
            # Create HWID based on system information
            system_info = f"{platform.system()}-{platform.machine()}-{platform.processor()}"
            mac_address = ':'.join(['{:02x}'.format((uuid.getnode() >> elements) & 0xff) 
                                  for elements in range(0,2*6,2)][::-1])
            hwid_string = f"{system_info}-{mac_address}"
            
            # Generate MD5 hash for consistent HWID
            return hashlib.md5(hwid_string.encode()).hexdigest()[:16].upper()
        except:
            # Fallback HWID
            return hashlib.md5(f"{platform.system()}-fallback".encode()).hexdigest()[:16].upper()
    
    def _generate_hmac(self, message, secret="default-hmac-secret"):
        """Generate HMAC signature for API security"""
        return hmac.new(secret.encode(), message.encode(), hashlib.sha256).hexdigest()
    
    def _make_request(self, endpoint, data=None, method="POST"):
        """Make authenticated API request"""
        url = f"{self.api_url}/api/v1/{endpoint}"
        
        try:
            if method == "POST":
                response = requests.post(url, json=data, timeout=10)
            else:
                response = requests.get(url, params=data, timeout=10)
            
            return response
        except requests.exceptions.RequestException as e:
            print(f"❌ Network error: {e}")
            return None
    
    def login(self, license_key):
        """Authenticate with license key"""
        print(f"🔑 Authenticating with license key...")
        print(f"📱 Hardware ID: {self.hwid}")
        
        data = {
            "key": license_key,
            "hwid": self.hwid,
            "app_name": self.app_name
        }
        
        response = self._make_request("login_key", data)
        
        if not response:
            return False
        
        try:
            result = response.json()
        except Exception as e:
            print(f"❌ Invalid response from server: {e}")
            print(f"Response status: {response.status_code}")
            print(f"Response text: {response.text[:200]}...")
            return False
        
        if response.status_code == 200 and result.get('status'):
            self.session_token = result.get('session_token')
            self.user_info = result.get('user_info', {})
            
            print(f"✅ {result.get('message', 'Authentication successful')}")
            print(f"👤 Plan: {self.user_info.get('plan', 'Unknown')}")
            print(f"⏰ Expires in: {self.user_info.get('expires_in_days', 0)} days")
            
            return True
        else:
            print(f"❌ Authentication failed: {result.get('message', 'Unknown error')}")
            return False
    
    def get_user_info(self):
        """Get detailed user information"""
        if not self.session_token:
            print("❌ Not authenticated. Please login first.")
            return None
        
        data = {
            "session_token": self.session_token,
            "key": self.user_info.get('license_key', '')
        }
        
        response = self._make_request("user_info", data)
        
        if not response:
            return None
        
        try:
            result = response.json()
        except:
            print("❌ Invalid response from server")
            return None
        
        if response.status_code == 200 and result.get('status'):
            user_data = result.get('user_info', {})
            
            print("📋 User Information:")
            print(f"  Username: {user_data.get('username', 'N/A')}")
            print(f"  Email: {user_data.get('email', 'N/A')}")
            print(f"  Plan: {user_data.get('plan', 'N/A')}")
            print(f"  Hardware ID: {user_data.get('hwid', 'N/A')}")
            print(f"  Expires: {user_data.get('expires_at', 'N/A')}")
            print(f"  Days remaining: {user_data.get('expires_in_days', 0)}")
            
            return user_data
        else:
            print(f"❌ Failed to get user info: {result.get('message', 'Unknown error')}")
            return None
    
    def is_authenticated(self):
        """Check if client is authenticated"""
        return self.session_token is not None
    
    def logout(self):
        """Clear authentication data"""
        self.session_token = None
        self.user_info = None
        print("👋 Logged out successfully")

def main():
    """Main client application"""
    print("🐸 PepeAuth Client - KeyAuth-like License System")
    print("=" * 60)
    
    # Initialize client
    client = PepeAuthClient()
    
    print(f"🖥️  System: {platform.system()} {platform.release()}")
    print(f"📱 Hardware ID: {client.hwid}")
    print("=" * 60)
    
    # Get license key from user
    try:
        license_key = input("🔑 Enter your license key: ").strip()
        
        if not license_key:
            print("❌ License key cannot be empty")
            sys.exit(1)
        
        # Attempt authentication
        if client.login(license_key):
            print("\n🎉 Welcome, Premium user!")
            
            # Show user information
            print("\n" + "=" * 60)
            client.get_user_info()
            
            # Interactive menu
            while True:
                print("\n" + "=" * 60)
                print("📋 Available Actions:")
                print("  1. Show user information")
                print("  2. Check authentication status")
                print("  3. Logout and exit")
                
                choice = input("\n👉 Choose an action (1-3): ").strip()
                
                if choice == "1":
                    print("\n" + "-" * 40)
                    client.get_user_info()
                elif choice == "2":
                    status = "✅ Authenticated" if client.is_authenticated() else "❌ Not authenticated"
                    print(f"\n🔐 Status: {status}")
                elif choice == "3":
                    client.logout()
                    break
                else:
                    print("❌ Invalid choice. Please select 1-3.")
        else:
            print("\n❌ Authentication failed. Please check your license key.")
            sys.exit(1)
    
    except KeyboardInterrupt:
        print("\n\n👋 Goodbye!")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
