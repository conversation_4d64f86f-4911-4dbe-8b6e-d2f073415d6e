# 🐸 PepeAuth - KeyAuth Alternative

A comprehensive, modern license management system built with Flask and Python. PepeAuth provides all the features of KeyAuth with enhanced security, better UI/UX, and comprehensive API documentation.

![PepeAuth Dashboard](https://via.placeholder.com/800x400/1a1a1a/2ecc71?text=PepeAuth+Dashboard)

## ✨ Features

### 🔐 Authentication & Security
- **HWID Binding**: Bind license keys to specific hardware IDs
- **Rate Limiting**: Comprehensive rate limiting with Redis support
- **Ban System**: User and IP-based banning with audit trails
- **HMAC Security**: Secure API communication with HMAC-SHA256
- **Session Management**: Secure session handling with tokens
- **Comprehensive Logging**: Security, audit, and performance logging

### 📊 Dashboard & Management
- **Modern UI**: Beautiful, responsive dashboard with dark theme
- **User Dashboard**: Manage apps, create license keys, view analytics
- **Admin Panel**: Comprehensive admin tools for user and system management
- **Real-time Analytics**: Live statistics and performance metrics
- **Webhook Integration**: Real-time notifications for events

### 🚀 API & Integration
- **KeyAuth Compatible**: Drop-in replacement for KeyAuth APIs
- **Python Client Library**: Easy-to-use Python client for integration
- **RESTful APIs**: Well-documented REST APIs with OpenAPI spec
- **Webhook Support**: Configurable webhooks for real-time notifications
- **Multiple Plans**: Support for trial, premium, and lifetime licenses

### 🛠️ Developer Experience
- **Easy Setup**: One-command deployment with Docker
- **Comprehensive Docs**: Detailed documentation and examples
- **Client Libraries**: Python client with more languages coming
- **Testing Tools**: Built-in testing and debugging tools

## 🚀 Quick Start

### Prerequisites
- Python 3.8+
- SQLite (default) or PostgreSQL/MySQL
- Redis (optional, for rate limiting)

### Installation

1. **Clone the repository**
```bash
git clone https://github.com/yourusername/pepeauth.git
cd pepeauth
```

2. **Install dependencies**
```bash
pip install -r requirements.txt
```

3. **Initialize the database**
```bash
python init_db.py
```

4. **Configure environment**
```bash
cp .env.example .env
# Edit .env with your configuration
```

5. **Run the application**
```bash
python run.py
```

The application will be available at `http://localhost:5000`

### Docker Deployment

```bash
# Build and run with Docker Compose
docker-compose up -d

# Or build manually
docker build -t pepeauth .
docker run -p 5000:5000 pepeauth
```

## 📖 Usage

### Creating Your First Application

1. **Register an account** at `http://localhost:5000/register`
2. **Login** and navigate to the dashboard
3. **Create an application** with your desired settings
4. **Generate license keys** for your users
5. **Integrate** using our Python client or API directly

### Python Client Example

```python
from client.pepeauth_client import PepeAuthClient

# Initialize client
client = PepeAuthClient("http://localhost:5000", "your-app-secret")

# Authenticate user
if client.authenticate("LICENSE-KEY-HERE"):
    print(f"Welcome {client.user_info.username}!")
    print(f"Plan: {client.user_info.plan}")
    print(f"Days remaining: {client.user_info.expires_in_days}")
    
    # Log custom activity
    client.log_activity("app_start", "Application started successfully")
else:
    print(f"Authentication failed: {client.last_error}")
```

### API Integration

```python
import requests

# Authenticate with license key
response = requests.post("http://localhost:5000/api/v1/login_key", json={
    "key": "your-license-key",
    "app_secret": "your-app-secret",
    "hwid": "user-hardware-id"
})

if response.json()["status"]:
    user_info = response.json()["user_info"]
    print(f"Welcome {user_info['username']}!")
else:
    print(f"Authentication failed: {response.json()['message']}")
```

## 🔧 Configuration

### Environment Variables

```bash
# Application Settings
SECRET_KEY=your-secret-key-here
DEBUG=False
HOST=0.0.0.0
PORT=5000

# Database
DATABASE_URL=sqlite:///pepeauth.db
# DATABASE_URL=postgresql://user:pass@localhost/pepeauth

# Redis (optional)
REDIS_URL=redis://localhost:6379/0

# Email (optional)
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password

# Security
RATE_LIMIT_STORAGE_URL=redis://localhost:6379/1
MAX_HWID_RESETS=3
SESSION_TIMEOUT=3600
```

### Database Configuration

PepeAuth supports multiple database backends:

- **SQLite** (default): Perfect for development and small deployments
- **PostgreSQL**: Recommended for production
- **MySQL**: Also supported for production

## 📚 API Documentation

### Base URL
All API endpoints are prefixed with `/api/v1/`

### Authentication
Most endpoints require an `app_secret` parameter for authentication.

### Rate Limits
- Authentication: 10 requests per minute
- Other endpoints: 100 requests per hour
- HWID Reset: 5 requests per hour

### Response Format
All responses follow this format:
```json
{
    "status": true|false,
    "message": "Human readable message",
    "data": {} // Optional additional data
}
```

### Authentication Endpoints

#### POST `/api/v1/login_key`
Authenticate with a license key and get user information.

**Request:**
```json
{
    "key": "license-key-here",
    "app_secret": "your-app-secret",
    "hwid": "hardware-id-optional"
}
```

**Success Response (200):**
```json
{
    "status": true,
    "message": "Welcome, Premium user!",
    "user_info": {
        "username": "user123",
        "email": "<EMAIL>",
        "plan": "Premium",
        "hwid": "hardware-id",
        "app_name": "My Application",
        "expires_in_days": 30,
        "expires_at": "2024-02-01T00:00:00",
        "created_at": "2024-01-01T00:00:00",
        "used_at": "2024-01-15T12:00:00"
    },
    "session_token": "secure-session-token"
}
```

**Error Response (403):**
```json
{
    "status": false,
    "message": "Invalid license key or app secret"
}
```

#### POST `/api/v1/reset_hwid`
Reset hardware ID for a license key (limited per user).

**Request:**
```json
{
    "key": "license-key-here",
    "hwid": "new-hardware-id-optional"
}
```

**Success Response (200):**
```json
{
    "status": true,
    "message": "Hardware ID reset successfully",
    "new_hwid": "new-hardware-id",
    "resets_remaining": 2
}
```

#### POST `/api/v1/log`
Log custom activity from your application.

**Request:**
```json
{
    "app_secret": "your-app-secret",
    "key": "license-key-here",
    "action": "feature_used",
    "details": "User accessed premium feature"
}
```

#### POST `/api/v1/webhook`
Trigger webhook for testing purposes.

**Request:**
```json
{
    "app_secret": "your-app-secret",
    "event": "test",
    "data": {
        "message": "Test webhook"
    }
}
```

### Webhook Events

PepeAuth can send webhooks for various events:

- `auth_success`: Successful authentication
- `auth_fail`: Failed authentication attempt
- `key_created`: New license key generated
- `key_banned`: License key banned
- `hwid_reset`: Hardware ID reset
- `key_expired`: License key expired

**Webhook Payload:**
```json
{
    "event": "auth_success",
    "timestamp": "2024-01-15T12:00:00Z",
    "app_id": "123",
    "app_name": "My App",
    "data": {
        "license_key": "ABC123...",
        "hwid": "device123",
        "ip_address": "***********",
        "user_agent": "MyApp/1.0"
    }
}
```

## 🛡️ Security Features

### Rate Limiting
- **API Endpoints**: 10 requests per minute for auth, 100 per hour for other endpoints
- **IP-based**: Automatic IP banning for suspicious activity
- **User-based**: Per-user rate limiting for authenticated endpoints

### HWID Management
- **Binding**: License keys can be bound to specific hardware IDs
- **Reset Limits**: Configurable limits on HWID resets per user
- **Validation**: Comprehensive HWID format validation

### Audit Logging
- **Security Events**: All authentication attempts and security events
- **User Actions**: Complete audit trail of user actions
- **Admin Actions**: Detailed logging of administrative actions
- **Performance**: Request performance and slow query logging

## 🔄 Migration from KeyAuth

PepeAuth is designed as a drop-in replacement for KeyAuth. To migrate:

1. **Export your data** from KeyAuth
2. **Import using our migration script**: `python migrate_from_keyauth.py`
3. **Update your client code** to use the new endpoints (mostly compatible)
4. **Configure webhooks** if you were using them

### API Compatibility

Most KeyAuth endpoints are supported with the same request/response format:

- ✅ `/api/1.2/` → `/api/v1/`
- ✅ Authentication endpoints
- ✅ User management
- ✅ License key operations
- ✅ HWID management
- ✅ Webhook support

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

### Development Setup

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Make your changes and add tests
4. Run tests: `python -m pytest`
5. Commit your changes: `git commit -m 'Add amazing feature'`
6. Push to the branch: `git push origin feature/amazing-feature`
7. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: [Full documentation](https://pepeauth.readthedocs.io)
- **Issues**: [GitHub Issues](https://github.com/yourusername/pepeauth/issues)
- **Discord**: [Join our Discord](https://discord.gg/pepeauth)
- **Email**: <EMAIL>

## 🙏 Acknowledgments

- Inspired by KeyAuth but built from the ground up
- Thanks to the Flask and Python communities
- Special thanks to all contributors

---

**Made with ❤️ by the PepeAuth team**
