"""
PepeAuth Configuration
KeyAuth-like license management system configuration
"""

import os
from datetime import timedelta

class Config:
    """Base configuration class"""
    
    # Security
    SECRET_KEY = os.environ.get('SECRET_KEY', 'dev-secret-key-change-in-production-please')
    HMAC_SECRET = os.environ.get('HMAC_SECRET', 'hmac-secret-for-api-validation')
    
    # Database
    DATABASE_PATH = os.environ.get('DATABASE_PATH', 'instance/database.db')
    
    # Flask settings
    WTF_CSRF_TIME_LIMIT = None
    PERMANENT_SESSION_LIFETIME = timedelta(hours=24)
    
    # Rate limiting
    RATELIMIT_STORAGE_URL = os.environ.get('RATELIMIT_STORAGE_URL', 'memory://')
    
    # API settings
    API_VERSION = 'v1'
    DEFAULT_PLAN = 'Premium'
    
    # Security settings
    MAX_LOGIN_ATTEMPTS = 5
    LOGIN_ATTEMPT_TIMEOUT = 300  # 5 minutes

    # Password security
    MIN_PASSWORD_LENGTH = 8
    REQUIRE_UPPERCASE = True
    REQUIRE_LOWERCASE = True
    REQUIRE_DIGITS = True
    REQUIRE_SPECIAL_CHARS = True

    # API security
    API_KEY_LENGTH = 32
    SESSION_TOKEN_LENGTH = 32
    HMAC_ALGORITHM = 'sha256'

    # Rate limiting (requests per time window)
    RATE_LIMITS = {
        'login': '10 per minute',
        'register': '5 per minute',
        'api_auth': '20 per minute',
        'admin_create_key': '50 per hour',
        'default': '100 per hour'
    }

    # Security logging
    LOG_SECURITY_EVENTS = True
    LOG_FAILED_LOGINS = True
    LOG_API_REQUESTS = True
    
    # License settings
    DEFAULT_EXPIRY_DAYS = 30
    HWID_LOCK_ENABLED = True

class DevelopmentConfig(Config):
    """Development configuration"""
    DEBUG = True
    TESTING = False

class ProductionConfig(Config):
    """Production configuration"""
    DEBUG = False
    TESTING = False

    # Override with environment variables if available
    SECRET_KEY = os.environ.get('SECRET_KEY', Config.SECRET_KEY)
    HMAC_SECRET = os.environ.get('HMAC_SECRET', Config.HMAC_SECRET)

class TestingConfig(Config):
    """Testing configuration"""
    DEBUG = True
    TESTING = True
    DATABASE_PATH = ':memory:'  # Use in-memory database for tests

# Configuration mapping
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}
