{% extends "base.html" %}

{% block title %}Manage Users - Admin - PepeAuth{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center space-x-4 mb-4">
            <a href="{{ url_for('admin.index') }}" class="text-gray-400 hover:text-white">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
            </a>
            <h1 class="text-3xl font-bold text-white">Manage Users</h1>
        </div>
        <p class="text-gray-300">View and manage all user accounts</p>
    </div>
    
    <!-- Search and Filters -->
    <div class="glass-card p-6 rounded-xl mb-8">
        <form method="GET" class="flex flex-col sm:flex-row gap-4">
            <div class="flex-1">
                <input type="text" name="search" value="{{ search }}" 
                       placeholder="Search users by username or email..."
                       class="input-field">
            </div>
            <button type="submit" class="btn-primary">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
                Search
            </button>
        </form>
    </div>
    
    <!-- Users Table -->
    <div class="glass-card p-6 rounded-xl">
        <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold text-white">Users</h3>
            <span class="text-sm text-gray-300">{{ users|length }} user{{ 's' if users|length != 1 else '' }}</span>
        </div>
        
        {% if users %}
            <div class="overflow-x-auto">
                <table class="min-w-full">
                    <thead>
                        <tr class="border-b border-pepe-light-gray/20">
                            <th class="text-left py-3 px-4 text-sm font-medium text-gray-300">User</th>
                            <th class="text-left py-3 px-4 text-sm font-medium text-gray-300">Plan</th>
                            <th class="text-left py-3 px-4 text-sm font-medium text-gray-300">Admin</th>
                            <th class="text-left py-3 px-4 text-sm font-medium text-gray-300">Created</th>
                            <th class="text-right py-3 px-4 text-sm font-medium text-gray-300">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-pepe-light-gray/20">
                        {% for user in users %}
                            <tr class="hover:bg-pepe-light-gray/10">
                                <td class="py-3 px-4">
                                    <div>
                                        <p class="text-white font-medium">{{ user.username }}</p>
                                        <p class="text-gray-400 text-sm">{{ user.email }}</p>
                                    </div>
                                </td>
                                <td class="py-3 px-4">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                        {% if user.plan_name == 'Free' %}bg-gray-500/20 text-gray-400{% endif %}
                                        {% if user.plan_name == 'Pro' %}bg-blue-500/20 text-blue-400{% endif %}
                                        {% if user.plan_name == 'Military' %}bg-pepe-green/20 text-pepe-green{% endif %}">
                                        {{ user.plan_name }}
                                    </span>
                                </td>
                                <td class="py-3 px-4">
                                    {% if user.is_admin %}
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-500/20 text-red-400">
                                            Admin
                                        </span>
                                    {% else %}
                                        <span class="text-gray-400">User</span>
                                    {% endif %}
                                </td>
                                <td class="py-3 px-4 text-sm text-gray-300">{{ user.created_at }}</td>
                                <td class="py-3 px-4 text-right">
                                    <div class="flex items-center justify-end space-x-2">
                                        <a href="{{ url_for('admin.edit_user', user_id=user.id) }}" 
                                           class="text-blue-400 hover:text-blue-300">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                            </svg>
                                        </a>
                                        {% if user.id != current_user.id %}
                                            <form method="POST" action="{{ url_for('admin.delete_user', user_id=user.id) }}" 
                                                  class="inline" onsubmit="return confirm('Are you sure you want to delete this user?')">
                                                <button type="submit" class="text-red-400 hover:text-red-300">
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                                    </svg>
                                                </button>
                                            </form>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            {% if has_prev or has_next %}
                <div class="flex items-center justify-between mt-6">
                    <div class="flex items-center space-x-2">
                        {% if has_prev %}
                            <a href="{{ url_for('admin.users', page=page-1, search=search) }}" 
                               class="btn-secondary">Previous</a>
                        {% endif %}
                        {% if has_next %}
                            <a href="{{ url_for('admin.users', page=page+1, search=search) }}" 
                               class="btn-secondary">Next</a>
                        {% endif %}
                    </div>
                    <span class="text-sm text-gray-400">Page {{ page }}</span>
                </div>
            {% endif %}
        {% else %}
            <div class="text-center py-12">
                <div class="w-16 h-16 bg-pepe-gray rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-medium text-white mb-2">No users found</h3>
                <p class="text-gray-300">{% if search %}Try adjusting your search terms.{% else %}No users in the system yet.{% endif %}</p>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}
