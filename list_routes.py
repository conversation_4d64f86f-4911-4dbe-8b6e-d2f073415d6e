#!/usr/bin/env python3
"""
List all registered routes in the Flask application
"""

from app import create_app

def list_routes():
    """List all routes registered in the Flask app"""
    app = create_app()
    
    print("📋 Registered Routes:")
    print("=" * 50)
    
    for rule in app.url_map.iter_rules():
        methods = ', '.join(rule.methods - {'HEAD', 'OPTIONS'})
        print(f"{rule.rule:<30} {methods:<15} {rule.endpoint}")
    
    print("=" * 50)

if __name__ == "__main__":
    list_routes()
