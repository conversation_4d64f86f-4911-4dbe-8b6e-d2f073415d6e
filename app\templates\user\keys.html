{% extends "base.html" %}

{% block title %}License Keys - PepeAuth{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div>
                <h1 class="text-3xl font-bold text-white">License Key Management</h1>
                <p class="mt-1 text-gray-300">View and manage all your license keys</p>
            </div>
            <div class="mt-4 sm:mt-0 flex space-x-3">
                <a href="{{ url_for('dashboard.create_license') }}" class="btn-primary">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Create Keys
                </a>
                <button onclick="exportKeys()" class="btn-secondary">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    Export
                </button>
            </div>
        </div>
    </div>
    
    <!-- Stats Overview -->
    <div class="grid grid-cols-1 md:grid-cols-5 gap-6 mb-8">
        <div class="glass-card p-6 rounded-xl">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-blue-500/20 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m0 0a2 2 0 012 2m-2-2a2 2 0 00-2 2m2-2V5a2 2 0 00-2-2m0 0H9a2 2 0 00-2 2v0a2 2 0 002 2h2m-4 4v6a2 2 0 002 2h2a2 2 0 002-2v-6m-4 0a2 2 0 012-2h0a2 2 0 012 2v0"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-300">Total Keys</p>
                    <p class="text-2xl font-semibold text-white">{{ stats.total_keys }}</p>
                </div>
            </div>
        </div>
        
        <div class="glass-card p-6 rounded-xl">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-green-500/20 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-300">Active</p>
                    <p class="text-2xl font-semibold text-white">{{ stats.active_keys }}</p>
                </div>
            </div>
        </div>
        
        <div class="glass-card p-6 rounded-xl">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-pepe-green/20 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-pepe-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-300">Used</p>
                    <p class="text-2xl font-semibold text-white">{{ stats.used_keys }}</p>
                </div>
            </div>
        </div>
        
        <div class="glass-card p-6 rounded-xl">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-red-500/20 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-300">Expired</p>
                    <p class="text-2xl font-semibold text-white">{{ stats.expired_keys }}</p>
                </div>
            </div>
        </div>
        
        <div class="glass-card p-6 rounded-xl">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-yellow-500/20 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-300">Banned</p>
                    <p class="text-2xl font-semibold text-white">{{ stats.banned_keys }}</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Filters and Search -->
    <div class="glass-card p-6 rounded-xl mb-8">
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            <div class="flex-1 max-w-lg">
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </div>
                    <input type="text" 
                           class="input-field pl-10" 
                           placeholder="Search license keys..."
                           id="search-input">
                </div>
            </div>
            <div class="flex flex-wrap items-center gap-4">
                <select class="input-field w-auto" id="app-filter">
                    <option value="">All Apps</option>
                    {% for app in apps %}
                        <option value="{{ app.id }}">{{ app.name }}</option>
                    {% endfor %}
                </select>
                <select class="input-field w-auto" id="status-filter">
                    <option value="">All Status</option>
                    <option value="unused">Unused</option>
                    <option value="used">Used</option>
                    <option value="expired">Expired</option>
                    <option value="banned">Banned</option>
                </select>
                <select class="input-field w-auto" id="plan-filter">
                    <option value="">All Plans</option>
                    {% for plan in plans %}
                        <option value="{{ plan.id }}">{{ plan.name }}</option>
                    {% endfor %}
                </select>
                <button onclick="resetFilters()" class="btn-secondary text-sm">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    Reset
                </button>
            </div>
        </div>
    </div>
    
    <!-- Keys Table -->
    <div class="glass-card rounded-xl overflow-hidden">
        <div class="overflow-x-auto">
            <table class="table-glass" id="keys-table">
                <thead>
                    <tr>
                        <th class="w-4">
                            <input type="checkbox" id="select-all" class="w-4 h-4 text-pepe-green bg-pepe-gray border-pepe-light-gray rounded focus:ring-pepe-green focus:ring-2">
                        </th>
                        <th>License Key</th>
                        <th>Application</th>
                        <th>Plan</th>
                        <th>Status</th>
                        <th>HWID</th>
                        <th>Created</th>
                        <th>Expires</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for key in keys %}
                        <tr class="key-row" 
                            data-app="{{ key.app_id }}" 
                            data-status="{{ 'banned' if key.is_banned else ('expired' if key.expires_at and key.expires_at < now else ('used' if key.used else 'unused')) }}"
                            data-plan="{{ key.plan_id }}">
                            <td>
                                <input type="checkbox" class="key-checkbox w-4 h-4 text-pepe-green bg-pepe-gray border-pepe-light-gray rounded focus:ring-pepe-green focus:ring-2" value="{{ key.id }}">
                            </td>
                            <td>
                                <div class="flex items-center space-x-2">
                                    <code class="text-xs bg-pepe-darker px-2 py-1 rounded font-mono">{{ key.key[:12] }}...{{ key.key[-4:] }}</code>
                                    <button onclick="copyKey('{{ key.key }}')" class="text-gray-400 hover:text-white transition-colors" title="Copy Key">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                        </svg>
                                    </button>
                                </div>
                            </td>
                            <td>
                                <div class="flex items-center space-x-2">
                                    <span class="text-white">{{ key.app.name }}</span>
                                    {% if key.app.hwid_lock %}
                                        <div class="w-2 h-2 bg-yellow-500 rounded-full tooltip" data-tooltip="HWID Lock Enabled"></div>
                                    {% endif %}
                                </div>
                            </td>
                            <td>
                                <span class="badge badge-info">{{ key.plan.name }}</span>
                            </td>
                            <td>
                                {% if key.is_banned %}
                                    <span class="badge badge-error">Banned</span>
                                {% elif key.expires_at and key.expires_at < now %}
                                    <span class="badge badge-warning">Expired</span>
                                {% elif key.used %}
                                    <span class="badge badge-success">Used</span>
                                {% else %}
                                    <span class="badge badge-info">Unused</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if key.hwid %}
                                    <div class="flex items-center space-x-2">
                                        <code class="text-xs text-gray-400">{{ key.hwid[:8] }}...</code>
                                        {% if current_user.hwid_reset_count < current_user.max_hwid_resets %}
                                            <button onclick="resetHwid({{ key.id }})" 
                                                    class="text-yellow-400 hover:text-yellow-300 transition-colors" 
                                                    title="Reset HWID">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                                </svg>
                                            </button>
                                        {% endif %}
                                    </div>
                                {% else %}
                                    <span class="text-gray-500 text-sm">Not bound</span>
                                {% endif %}
                            </td>
                            <td>
                                <span class="text-sm text-gray-400">{{ key.created_at.strftime('%Y-%m-%d') }}</span>
                            </td>
                            <td>
                                {% if key.expires_at %}
                                    <span class="text-sm text-gray-400">{{ key.expires_at.strftime('%Y-%m-%d') }}</span>
                                {% else %}
                                    <span class="text-sm text-pepe-green">Lifetime</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="flex items-center space-x-2">
                                    {% if not key.is_banned %}
                                        <button onclick="banKey({{ key.id }})" 
                                                class="text-red-400 hover:text-red-300 transition-colors" 
                                                title="Ban Key">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728"></path>
                                            </svg>
                                        </button>
                                    {% else %}
                                        <button onclick="unbanKey({{ key.id }})" 
                                                class="text-green-400 hover:text-green-300 transition-colors" 
                                                title="Unban Key">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                            </svg>
                                        </button>
                                    {% endif %}
                                    <button onclick="deleteKey({{ key.id }})" 
                                            class="text-red-400 hover:text-red-300 transition-colors" 
                                            title="Delete Key">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                        </svg>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- Bulk Actions -->
        <div class="p-4 border-t border-pepe-light-gray/20 bg-pepe-darker/30" id="bulk-actions" style="display: none;">
            <div class="flex items-center justify-between">
                <span class="text-sm text-gray-300" id="selected-count">0 keys selected</span>
                <div class="flex space-x-2">
                    <button onclick="bulkBan()" class="btn-danger text-sm">Ban Selected</button>
                    <button onclick="bulkDelete()" class="btn-danger text-sm">Delete Selected</button>
                    <button onclick="bulkExport()" class="btn-secondary text-sm">Export Selected</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Pagination -->
    {% if pagination.pages > 1 %}
        <div class="flex items-center justify-between mt-8">
            <div class="text-sm text-gray-400">
                Showing {{ pagination.per_page * (pagination.page - 1) + 1 }} to {{ pagination.per_page * pagination.page if pagination.page < pagination.pages else pagination.total }} of {{ pagination.total }} keys
            </div>
            <div class="flex space-x-2">
                {% if pagination.has_prev %}
                    <a href="{{ url_for('dashboard.keys', page=pagination.prev_num) }}" class="btn-secondary text-sm">Previous</a>
                {% endif %}
                
                {% for page_num in pagination.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != pagination.page %}
                            <a href="{{ url_for('dashboard.keys', page=page_num) }}" class="btn-secondary text-sm">{{ page_num }}</a>
                        {% else %}
                            <span class="btn-primary text-sm">{{ page_num }}</span>
                        {% endif %}
                    {% else %}
                        <span class="text-gray-400">...</span>
                    {% endif %}
                {% endfor %}
                
                {% if pagination.has_next %}
                    <a href="{{ url_for('dashboard.keys', page=pagination.next_num) }}" class="btn-secondary text-sm">Next</a>
                {% endif %}
            </div>
        </div>
    {% endif %}
</div>
{% endblock %}

{% block scripts %}
<script>
// Search and filter functionality
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('search-input');
    const appFilter = document.getElementById('app-filter');
    const statusFilter = document.getElementById('status-filter');
    const planFilter = document.getElementById('plan-filter');
    const selectAll = document.getElementById('select-all');
    const keyCheckboxes = document.querySelectorAll('.key-checkbox');
    const bulkActions = document.getElementById('bulk-actions');
    const selectedCount = document.getElementById('selected-count');
    
    // Search functionality
    searchInput.addEventListener('input', filterKeys);
    appFilter.addEventListener('change', filterKeys);
    statusFilter.addEventListener('change', filterKeys);
    planFilter.addEventListener('change', filterKeys);
    
    // Select all functionality
    selectAll.addEventListener('change', function() {
        keyCheckboxes.forEach(checkbox => {
            if (checkbox.closest('tr').style.display !== 'none') {
                checkbox.checked = this.checked;
            }
        });
        updateBulkActions();
    });
    
    // Individual checkbox functionality
    keyCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateBulkActions);
    });
    
    function filterKeys() {
        const searchTerm = searchInput.value.toLowerCase();
        const appId = appFilter.value;
        const status = statusFilter.value;
        const planId = planFilter.value;
        
        document.querySelectorAll('.key-row').forEach(row => {
            const keyText = row.textContent.toLowerCase();
            const rowApp = row.dataset.app;
            const rowStatus = row.dataset.status;
            const rowPlan = row.dataset.plan;
            
            const matchesSearch = !searchTerm || keyText.includes(searchTerm);
            const matchesApp = !appId || rowApp === appId;
            const matchesStatus = !status || rowStatus === status;
            const matchesPlan = !planId || rowPlan === planId;
            
            if (matchesSearch && matchesApp && matchesStatus && matchesPlan) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
                row.querySelector('.key-checkbox').checked = false;
            }
        });
        
        updateBulkActions();
    }
    
    function updateBulkActions() {
        const checkedBoxes = document.querySelectorAll('.key-checkbox:checked');
        const count = checkedBoxes.length;
        
        if (count > 0) {
            bulkActions.style.display = 'block';
            selectedCount.textContent = `${count} key${count > 1 ? 's' : ''} selected`;
        } else {
            bulkActions.style.display = 'none';
        }
        
        // Update select all checkbox
        const visibleCheckboxes = Array.from(keyCheckboxes).filter(cb => 
            cb.closest('tr').style.display !== 'none'
        );
        const checkedVisible = visibleCheckboxes.filter(cb => cb.checked);
        
        selectAll.indeterminate = checkedVisible.length > 0 && checkedVisible.length < visibleCheckboxes.length;
        selectAll.checked = visibleCheckboxes.length > 0 && checkedVisible.length === visibleCheckboxes.length;
    }
});

// Key management functions
function copyKey(key) {
    navigator.clipboard.writeText(key).then(() => {
        // Show success feedback
        showNotification('License key copied to clipboard!', 'success');
    });
}

function resetHwid(keyId) {
    if (confirm('Are you sure you want to reset the HWID for this key? This will allow it to be used on a different device.')) {
        fetch(`/dashboard/key/${keyId}/reset-hwid`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('meta[name=csrf-token]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('HWID reset successfully!', 'success');
                location.reload();
            } else {
                showNotification('Error: ' + data.message, 'error');
            }
        });
    }
}

function banKey(keyId) {
    const reason = prompt('Enter ban reason (optional):');
    if (reason !== null) {
        fetch(`/dashboard/key/${keyId}/ban`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('meta[name=csrf-token]').getAttribute('content')
            },
            body: JSON.stringify({ reason: reason })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('Key banned successfully!', 'success');
                location.reload();
            } else {
                showNotification('Error: ' + data.message, 'error');
            }
        });
    }
}

function unbanKey(keyId) {
    if (confirm('Are you sure you want to unban this key?')) {
        fetch(`/dashboard/key/${keyId}/unban`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('meta[name=csrf-token]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('Key unbanned successfully!', 'success');
                location.reload();
            } else {
                showNotification('Error: ' + data.message, 'error');
            }
        });
    }
}

function deleteKey(keyId) {
    if (confirm('Are you sure you want to delete this key? This action cannot be undone.')) {
        fetch(`/dashboard/key/${keyId}/delete`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('meta[name=csrf-token]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('Key deleted successfully!', 'success');
                location.reload();
            } else {
                showNotification('Error: ' + data.message, 'error');
            }
        });
    }
}

// Bulk operations
function getSelectedKeys() {
    return Array.from(document.querySelectorAll('.key-checkbox:checked')).map(cb => cb.value);
}

function bulkBan() {
    const keys = getSelectedKeys();
    if (keys.length === 0) return;
    
    const reason = prompt(`Enter ban reason for ${keys.length} keys (optional):`);
    if (reason !== null) {
        fetch('/dashboard/keys/bulk-ban', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('meta[name=csrf-token]').getAttribute('content')
            },
            body: JSON.stringify({ keys: keys, reason: reason })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification(`${keys.length} keys banned successfully!`, 'success');
                location.reload();
            } else {
                showNotification('Error: ' + data.message, 'error');
            }
        });
    }
}

function bulkDelete() {
    const keys = getSelectedKeys();
    if (keys.length === 0) return;
    
    if (confirm(`Are you sure you want to delete ${keys.length} keys? This action cannot be undone.`)) {
        fetch('/dashboard/keys/bulk-delete', {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('meta[name=csrf-token]').getAttribute('content')
            },
            body: JSON.stringify({ keys: keys })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification(`${keys.length} keys deleted successfully!`, 'success');
                location.reload();
            } else {
                showNotification('Error: ' + data.message, 'error');
            }
        });
    }
}

function bulkExport() {
    const keys = getSelectedKeys();
    if (keys.length === 0) return;
    
    window.location.href = `/dashboard/keys/export?keys=${keys.join(',')}`;
}

function exportKeys() {
    window.location.href = '/dashboard/keys/export';
}

function resetFilters() {
    document.getElementById('search-input').value = '';
    document.getElementById('app-filter').value = '';
    document.getElementById('status-filter').value = '';
    document.getElementById('plan-filter').value = '';
    
    document.querySelectorAll('.key-row').forEach(row => {
        row.style.display = '';
    });
    
    document.querySelectorAll('.key-checkbox').forEach(cb => {
        cb.checked = false;
    });
    
    document.getElementById('select-all').checked = false;
    document.getElementById('bulk-actions').style.display = 'none';
}

function showNotification(message, type) {
    // Simple notification system
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg ${
        type === 'success' ? 'bg-green-500' : 'bg-red-500'
    } text-white`;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.remove();
    }, 3000);
}
</script>
{% endblock %}
