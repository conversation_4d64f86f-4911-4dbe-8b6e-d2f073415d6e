{% extends "base.html" %}

{% block title %}Manage Apps - Admin - PepeAuth{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center space-x-4 mb-4">
            <a href="{{ url_for('admin.index') }}" class="text-gray-400 hover:text-white">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
            </a>
            <h1 class="text-3xl font-bold text-white">Manage Apps</h1>
        </div>
        <p class="text-gray-300">View and manage all applications in the system</p>
    </div>
    
    <!-- Search and Filters -->
    <div class="glass-card p-6 rounded-xl mb-8">
        <form method="GET" class="flex flex-col sm:flex-row gap-4">
            <div class="flex-1">
                <input type="text" name="search" value="{{ search }}" 
                       placeholder="Search apps by name or owner..."
                       class="input-field">
            </div>
            <button type="submit" class="btn-primary">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
                Search
            </button>
        </form>
    </div>
    
    <!-- Apps Table -->
    <div class="glass-card p-6 rounded-xl">
        <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold text-white">Applications</h3>
            <span class="text-sm text-gray-300">{{ apps|length }} app{{ 's' if apps|length != 1 else '' }}</span>
        </div>
        
        {% if apps %}
            <div class="overflow-x-auto">
                <table class="min-w-full">
                    <thead>
                        <tr class="border-b border-pepe-light-gray/20">
                            <th class="text-left py-3 px-4 text-sm font-medium text-gray-300">App Name</th>
                            <th class="text-left py-3 px-4 text-sm font-medium text-gray-300">Owner</th>
                            <th class="text-left py-3 px-4 text-sm font-medium text-gray-300">Keys</th>
                            <th class="text-left py-3 px-4 text-sm font-medium text-gray-300">Settings</th>
                            <th class="text-left py-3 px-4 text-sm font-medium text-gray-300">Created</th>
                            <th class="text-right py-3 px-4 text-sm font-medium text-gray-300">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-pepe-light-gray/20">
                        {% for app in apps %}
                            <tr class="hover:bg-pepe-light-gray/10">
                                <td class="py-3 px-4">
                                    <p class="text-white font-medium">{{ app.name }}</p>
                                </td>
                                <td class="py-3 px-4">
                                    <p class="text-gray-300">{{ app.username }}</p>
                                </td>
                                <td class="py-3 px-4">
                                    <p class="text-white">{{ app.key_count }}</p>
                                </td>
                                <td class="py-3 px-4">
                                    <div class="flex items-center space-x-2">
                                        {% if app.hwid_lock %}
                                            <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-yellow-500/20 text-yellow-400">
                                                HWID Lock
                                            </span>
                                        {% endif %}
                                        <span class="text-gray-400 text-sm">{{ app.expiry_days }}d</span>
                                    </div>
                                </td>
                                <td class="py-3 px-4 text-sm text-gray-300">{{ app.created_at }}</td>
                                <td class="py-3 px-4 text-right">
                                    <form method="POST" action="{{ url_for('admin.delete_app', app_id=app.id) }}" 
                                          class="inline" onsubmit="return confirm('Are you sure you want to delete this app and all its keys?')">
                                        <button type="submit" class="text-red-400 hover:text-red-300">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                            </svg>
                                        </button>
                                    </form>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            {% if has_prev or has_next %}
                <div class="flex items-center justify-between mt-6">
                    <div class="flex items-center space-x-2">
                        {% if has_prev %}
                            <a href="{{ url_for('admin.apps', page=page-1, search=search) }}" 
                               class="btn-secondary">Previous</a>
                        {% endif %}
                        {% if has_next %}
                            <a href="{{ url_for('admin.apps', page=page+1, search=search) }}" 
                               class="btn-secondary">Next</a>
                        {% endif %}
                    </div>
                    <span class="text-sm text-gray-400">Page {{ page }}</span>
                </div>
            {% endif %}
        {% else %}
            <div class="text-center py-12">
                <div class="w-16 h-16 bg-pepe-gray rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-medium text-white mb-2">No apps found</h3>
                <p class="text-gray-300">{% if search %}Try adjusting your search terms.{% else %}No applications in the system yet.{% endif %}</p>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}
